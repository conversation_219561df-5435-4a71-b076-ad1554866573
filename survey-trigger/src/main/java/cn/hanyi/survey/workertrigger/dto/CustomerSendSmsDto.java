package cn.hanyi.survey.workertrigger.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.Duration;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CustomerSendSmsDto {
    private Long orgId;
    private Long userId;
    private Long taskProgressId;
    private Long channelRecordId;
    private Long thirdpartyAuthId;
    private String content;
    private String mobile;
    private String templateId;
    private String templateName;
    private String signId;
    private String realSign;
    private Duration delay;
    private String timed;
}
