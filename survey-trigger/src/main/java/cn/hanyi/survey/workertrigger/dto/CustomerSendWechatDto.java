package cn.hanyi.survey.workertrigger.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.Duration;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CustomerSendWechatDto {
    private Long orgId;
    private Long userId;
    private Long taskProgressId;
    private Long channelRecordId;
    private Long thirdpartyAuthId;
    private String appId;
    private String templateId;
    private String openId;
    private String message;
    private String url;
    private Duration delay;
    private String timed;
}
