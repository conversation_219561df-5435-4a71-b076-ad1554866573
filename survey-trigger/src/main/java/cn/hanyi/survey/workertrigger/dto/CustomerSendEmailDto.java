package cn.hanyi.survey.workertrigger.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.Duration;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CustomerSendEmailDto {
    private Long orgId;
    private Long userId;
    private Long taskProgressId;
    private Long channelRecordId;
    private Long thirdpartyAuthId;
    private String email;
    private String sender;
    private String title;
    private String content;
    private Duration delay;
    private String timed;
}
