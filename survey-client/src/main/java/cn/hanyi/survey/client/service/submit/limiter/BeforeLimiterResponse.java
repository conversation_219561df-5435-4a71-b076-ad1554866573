package cn.hanyi.survey.client.service.submit.limiter;

import cn.hanyi.survey.client.service.submit.IBeforeLimiter;
import cn.hanyi.survey.client.service.submit.ISubmitLoader;
import cn.hanyi.survey.client.service.submit.SubmitContext;
import cn.hanyi.survey.core.constant.ResponseStatus;
import cn.hanyi.survey.core.constant.error.SurveyErrorCode;
import cn.hanyi.survey.core.dto.SubmitAdditionDataDto;
import cn.hanyi.survey.core.entity.SurveyResponse;
import cn.hanyi.survey.core.exception.SurveyErrorException;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Order(1060)
@Component
@ConditionalOnProperty(value = "survey.client.enabled", havingValue = "true")
public class BeforeLimiterResponse implements IBeforeLimiter {

    @Autowired
    private ISubmitLoader loader;

    @Override
    public void check(SubmitContext context) {
        SurveyResponse response = loader.requireResponse(context);
        // 如果下次提交的时候，ip 变了，则更新 response 的 ip
        if (StringUtils.isNotEmpty(context.getIp())) {
            response.setIp(context.getIp());
        }
        //更新trackId
        response.setTrackId(context.getData().getTrackId());
        //更新渠道id
        response.setChannelId(context.getData().getChannelId());
        //context设置sendToken
        if (context.getData().getAdditionData() == null && StringUtils.isNotEmpty(response.getAdditionData())) {
            context.getData().setAdditionData(JsonHelper.toObject(response.getAdditionData(), SubmitAdditionDataDto.class));
        }
        //设置clientId
        if (StringUtils.isEmpty(context.getClientId()) || !context.getClientId().equals(response.getClientId())) {
            context.setClientId(response.getClientId());
        }
        checkStatus(response);
    }

    private void checkStatus(SurveyResponse response) {
        if (response.getIsCompleted()
                || response.getStatus() == ResponseStatus.EARLY_COMPLETED) {
            throw new SurveyErrorException(SurveyErrorCode.ALREADY_SUBMIT);
        }
        if (response.getStatus() != ResponseStatus.INIT) {
            throw new SurveyErrorException(SurveyErrorCode.ALREADY_SUBMIT);
        }
    }

}
