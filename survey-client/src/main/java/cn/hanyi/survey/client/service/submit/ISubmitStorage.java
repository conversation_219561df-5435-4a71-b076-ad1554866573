package cn.hanyi.survey.client.service.submit;


import cn.hanyi.survey.client.dto.ClientChannel;
import cn.hanyi.survey.client.dto.ClientQuestion;
import cn.hanyi.survey.client.dto.ClientSurvey;
import cn.hanyi.survey.core.entity.SurveyResponse;
import cn.hanyi.survey.core.entity.SurveyResponseCell;
import cn.hanyi.survey.core.entity.SurveySendRecord;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Supplier;

public interface ISubmitStorage {

    <T> List<T> nativeQueryList(String sql, Class<T> tClass);

    ClientSurvey getSurvey(Long surveyId);

    SurveyResponse getResponse(Long surveyId, String clientId);

    SurveyResponse getResponse(Long responseId);

    SurveyResponse addResponse(SurveyResponse response);

    List<SurveyResponseCell> getCells(Set<Long> cellIds, Map<Long, ClientQuestion> idMap);

    List<SurveyResponseCell> getCells(Long surveyId, Long responseId, Map<Long, ClientQuestion> idMap);

    ClientChannel getChannel(Long channelId);

    SurveySendRecord getChannelRecord(Long surveyId, Long channelId, String clientId);

    void updateChannelRecord(SurveySendRecord record);

    void save(SubmitContext context);

}
