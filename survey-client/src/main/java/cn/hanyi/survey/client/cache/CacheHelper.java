package cn.hanyi.survey.client.cache;

import org.befun.extension.service.NativeSqlHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.*;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.List;

@Service
public class CacheHelper {

    @Value("${survey.cache.enabled:false}")
    protected boolean enableCache;
    @Autowired
    protected StringRedisTemplate stringRedisTemplate;
    @Autowired
    protected NativeSqlHelper nativeSqlHelper;
    protected static final int cacheExpire = 7; // day
    protected static final String SYNC_PROGRESS = "2";
    protected static final String SYNC_COMPLETE = "1";
    protected static final String NOT_EXIST_FLAG = "notExist";
    private static final String LOCK_SUBMIT = "lock:survey-submit:%d:%s";

    public boolean lockSurveySubmit(Long surveyId, String clientId) {
        String key = String.format(LOCK_SUBMIT, surveyId, clientId);
        Boolean r = valueOpt().setIfAbsent(key, "1", Duration.ofSeconds(10));
        return r != null && r;
    }

    public void unlockSurveySubmit(Long surveyId, String clientId) {
        String key = String.format(LOCK_SUBMIT, surveyId, clientId);
        delete(key);
    }

    protected void syncCompleted(String syncKey) {
        valueOpt().set(syncKey, SYNC_COMPLETE);
        expireSyncKey(syncKey);
    }

    protected void expireKey(String key) {
        stringRedisTemplate.expire(key, Duration.ofDays(cacheExpire));
    }

    protected void expireSyncKey(String syncKey) {
        stringRedisTemplate.expire(syncKey, Duration.ofDays(cacheExpire));
    }

    protected void delete(String key) {
        stringRedisTemplate.delete(key);
    }

    protected void delete(List<String> keys) {
        stringRedisTemplate.delete(keys);
    }

    protected ValueOperations<String, String> valueOpt() {
        return stringRedisTemplate.opsForValue();
    }

    protected ZSetOperations<String, String> zSetOpt() {
        return stringRedisTemplate.opsForZSet();
    }

    protected SetOperations<String, String> setOpt() {
        return stringRedisTemplate.opsForSet();
    }

    protected HashOperations<String, String, String> hashOpt() {
        return stringRedisTemplate.opsForHash();
    }

    protected boolean unboxBool(Boolean bool) {
        return bool != null && bool;
    }

    protected long unboxLong(Long l) {
        return l == null ? 0L : l;
    }

    protected Pageable lastOne() {
        return PageRequest.of(0, 1, Sort.by("finishTime").descending());
    }
}
