package cn.hanyi.survey.client.service;

import cn.hanyi.survey.client.dto.ClientSurveyBehaviorDto;
import cn.hanyi.survey.core.entity.SurveyBehaviorRecord;
import cn.hanyi.survey.core.repository.SurveyBehaviorRecordRepository;
import cn.hanyi.survey.core.repository.SurveyResponseRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/9/26 16:47
 */
@Slf4j
@Service
public class SurveyBehaviorService {

    @Autowired
    private SurveyBehaviorRecordRepository recordRepository;
    @Autowired
    private SurveyResponseRepository responseRepository;
    @Autowired
    private SurveyBehaviorRecordRepository surveyBehaviorRecordRepository;

    /**
     * 新增行为记录 如果已存在 叠加停留时间和返回次数
     *
     * @param surveyId
     * @param behaviorDto
     */
    public void addSurveyBehaviorDto(Long surveyId, ClientSurveyBehaviorDto behaviorDto) {
        if (behaviorDto == null || behaviorDto.getBehaviorList() == null || behaviorDto.getBehaviorList().size() == 0) {
            log.info("survey behavior,surveyId:{}", surveyId);
            return;
        }
        behaviorDto.getBehaviorList().stream().forEach(behavior -> {
            if (behavior.getResponseId() == null || behavior.getPage() == null) return;
            SurveyBehaviorRecord record = getBehaviorRecord(behavior.getResponseId(), behavior.getPage());
            if (record == null) {
                record = new SurveyBehaviorRecord();
                record.setResponseId(behavior.getResponseId());
                record.setPage(behavior.getPage());
                record.setDuration(behavior.getDuration());
                record.setReturnTimes(behavior.getReturnTimes());
                record.setQCodes(behavior.getQCodes());
                record.setSurveyId(surveyId);
                recordRepository.save(record);
            } else {
                record.setDuration(behavior.getDuration() + record.getDuration());
                record.setReturnTimes(behavior.getReturnTimes() + record.getReturnTimes());
                recordRepository.save(record);
            }
        });
    }

    public SurveyBehaviorRecord getBehaviorRecord(Long responseId, Integer page) {
        Optional<SurveyBehaviorRecord> optional = recordRepository.findByResponseIdAndPage(responseId, page);
        return optional.orElse(null);
    }

    /**
     * 根据最后一条responseId build header
     * @param surveyId
     * @return
     */
    public List getBehaviorHeader(Long surveyId, Long responseId) {
        if(surveyId == null || responseId == null) return null;
        List<SurveyBehaviorRecord> behaviorRecordList = recordRepository.findByResponseId(responseId);
        if(behaviorRecordList == null || behaviorRecordList.size() == 0) return null;
        Integer totalPage = behaviorRecordList.stream().mapToInt(SurveyBehaviorRecord::getPage).max().getAsInt();
        if(totalPage == 0 || totalPage == null) return null;
        List<String> headers = new ArrayList<>(){{
            add("id");
        }};
        for (int i = 0; i < totalPage; i++) {
            headers.add(String.format("page%d_duration", i+1));
            headers.add(String.format("page%d_return_times", i+1));
        }
        return headers;
    }

    /**
     * build 下载的行为数据data
     * @param responseIds
     * @return
     */
    public List<List> getBehaviorData(List<Long> responseIds, Integer totalPage) {
        if(responseIds == null || responseIds.size() == 0) return null;
        List<SurveyBehaviorRecord> behaviorRecordList = recordRepository.findByResponseIdInOrderByPage(responseIds);
        List<List> behaviorData = new ArrayList<>();
        Map<Long,List<SurveyBehaviorRecord>> behaviorTree = new HashMap<>();
        responseIds.stream().forEach(responseId ->{
            List<SurveyBehaviorRecord> node = new ArrayList<>();
            behaviorRecordList.stream().forEach(record -> {
                if(record.getResponseId().equals(responseId)) {
                    node.add(record);
                }
            });
            if (node.size() > 0)
                behaviorTree.put(responseId, node.stream().sorted(Comparator.comparing(SurveyBehaviorRecord::getPage)).collect(Collectors.toList()));
        });

        for (Map.Entry<Long,List<SurveyBehaviorRecord>> entry : behaviorTree.entrySet()) {
            List ans = new ArrayList(){{
                add(entry.getKey().toString());
            }};
            if(entry.getValue() != null && entry.getValue().size() > 0) {
                entry.getValue().stream().forEach(r ->{
                    ans.add(r.getDuration());
                    ans.add(r.getReturnTimes());
                });
            } else {
                for (int i = 0; i < totalPage; i++) {
                    //duration和returnTimes 用空格填充
                    ans.add("");
                    ans.add("");
                }
            }
            behaviorData.add(ans);
        }
        return behaviorData;
    }

    public List<SurveyBehaviorRecord> findAllBySurveyId(Long surveyId) {
        return surveyBehaviorRecordRepository.findAllBySurveyId(surveyId);
    }
}
