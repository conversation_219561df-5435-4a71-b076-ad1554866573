package cn.hanyi.survey.client.cache;

import cn.hanyi.survey.client.dto.ClientSurvey;
import cn.hanyi.survey.core.constant.ResponseStatus;
import cn.hanyi.survey.core.projection.SimpleResponse;
import cn.hanyi.survey.core.repository.SurveyResponseRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.utils.DateHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.redis.core.DefaultTypedTuple;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ForkJoinPool;

@Slf4j
@Service
public class LimitSurveyCacheHelper extends CacheHelper {
    private static final String K_ID = "cache:survey-limit:%d:id";              // surveyId   zset value=responseId, score=responseStatus
    private static final String K_IP = "cache:survey-limit:%d:ip";              // surveyId   zset value=ip, score=finishTime
    private static final String K_CLIENT_ID = "cache:survey-limit:%d:client";   // surveyId   zset value=clientId, score=responseId
    private static final String K_SYNC = "cache:survey-limit:%d:sync";          // surveyId
    private static final int SIZE_PER_PAGE = 200;

    @Autowired
    private SurveyResponseRepository surveyResponseRepository;
    private final ForkJoinPool executorService = ForkJoinPool.commonPool();

    public void add(ClientSurvey survey, Long responseId, String ip, double finishTime) {
        Long surveyId = survey.getId();
        String idKey = idKey(surveyId);
        String ipKey = ipKey(surveyId);
        String clientIdKey = clientIdKey(surveyId);
        zSetOpt().add(idKey, responseId.toString(), ResponseStatus.FINAL_SUBMIT.ordinal());
        if (StringUtils.isNotEmpty(ip) && isEnableLimit(survey)) {
            zSetOpt().add(ipKey, ip, finishTime);
        }
        expireKey(idKey);
        expireKey(ipKey);
        delete(clientIdKey);
    }

    public void add(ClientSurvey survey, Long responseId, String ip, double finishTime, ResponseStatus status) {
        Long surveyId = survey.getId();
        String idKey = idKey(surveyId);
        String ipKey = ipKey(surveyId);
        String clientIdKey = clientIdKey(surveyId);
        zSetOpt().add(idKey, responseId.toString(), status.ordinal());
        if (StringUtils.isNotEmpty(ip) && isEnableLimit(survey)) {
            zSetOpt().add(ipKey, ip, finishTime);
        }
        expireKey(idKey);
        expireKey(ipKey);
        delete(clientIdKey);
    }

    private boolean isEnableLimit(ClientSurvey survey) {
        return unboxBool(survey.getEnableIpLimit());
    }

//    public void add(Long surveyId, Long responseId, String clientId) {
//        zSetOpt().add(clientIdKey(surveyId), clientId, responseId);
//    }

    public void updateResponseStatus(Long surveyId, Long responseId, String status) {
        zSetOpt().add(idKey(surveyId), responseId.toString(), ResponseStatus.valueOf(status).ordinal());
    }

    public void removeAllSurveyResponse(Long surveyId) {
        Optional.ofNullable(idKey(surveyId)).ifPresent(key -> {
            delete(key);
            log.info("删除问卷{}的答卷id列表缓存:{}", surveyId, key);
        });
        Optional.ofNullable(ipKey(surveyId)).ifPresent(key -> {
            delete(key);
            log.info("删除问卷{}的ip列表缓存:{}", surveyId, key);
        });
//        Optional.ofNullable(clientIdKey(surveyId)).ifPresent(key -> {
//            delete(key);
//            log.info("删除问卷{}的clientId列表缓存:{}", surveyId, key);
//        });
        Optional.ofNullable(syncKey(surveyId)).ifPresent(key -> {
            delete(key);
            log.info("删除问卷{}的同步状态缓存:{}", surveyId, key);
        });
    }

    public void removeResponse(Long surveyId, Long responseId, String clientId, String ip) {
        zSetOpt().remove(idKey(surveyId), responseId.toString());
//        if (StringUtils.isNotEmpty(clientId)) {
//            zSetOpt().remove(clientIdKey(surveyId), clientId);
//        }
        if (StringUtils.isNotEmpty(ip)) {
            Date laseFinishTime = laseFinishTimeByIpInDb(surveyId, ip);
            String ipKey = ipKey(surveyId);
            if (laseFinishTime == null) {
                // 已经没有这个ip的数据了，直接删除这个ip
                zSetOpt().remove(ipKey, ip);
            } else {
                // 还有这个ip的数据，找到最近的一个提交时间，并更新
                zSetOpt().add(ipKey, ip, laseFinishTime.getTime());
            }
        }
    }

//    public Long getResponseIdByClientId(Long surveyId, String clientId) {
//        if (isSyncCompleted(surveyId)) {
//            Double score = zSetOpt().score(clientIdKey(surveyId), clientId);
//            if (score != null) {
//                return score.longValue();
//            }
//        } else {
//            // 开始异步缓存
//            async(surveyId);
//        }
//        return null;
//    }

    /**
     * 查询指定 surveyId 的 正常提交的答卷数量
     */
    public long count(Long surveyId) {
        if (!enableCache) {
            return surveyResponseRepository.countBySurveyIdAndStatus(surveyId, ResponseStatus.FINAL_SUBMIT);
        }
        if (isSyncCompleted(surveyId)) {
            double score = ResponseStatus.FINAL_SUBMIT.ordinal();
            return unboxLong(zSetOpt().count(idKey(surveyId), score, score));
        } else {
            // 开始异步缓存
            async(surveyId);
            return surveyResponseRepository.countBySurveyIdAndStatus(surveyId, ResponseStatus.FINAL_SUBMIT);
        }
    }

    /**
     * 查询指定ip,最后一次填答指定 surveyId 的时间
     */
    public LocalDateTime lastTimeByIp(Long surveyId, String ip) {
        if (!enableCache) {
            return DateHelper.toLocalDateTime(laseFinishTimeByIpInDb(surveyId, ip));
        }
        if (isSyncCompleted(surveyId)) {
            Double score = zSetOpt().score(ipKey(surveyId), ip);
            if (score != null) {
                return DateHelper.toLocalDateTime(new Date(score.longValue()));
            }
        } else {
            // 开始异步缓存
            async(surveyId);
            // 提交时间倒序，第一条记录
            return DateHelper.toLocalDateTime(laseFinishTimeByIpInDb(surveyId, ip));
        }
        return null;
    }

    private Date laseFinishTimeByIpInDb(Long surveyId, String ip) {
        List<SimpleResponse> list = surveyResponseRepository.findSimpleBySurveyIdAndIsCompletedIsTrueAndIp(surveyId, ip, lastOne());
        if (CollectionUtils.isNotEmpty(list)) {
            return Optional.ofNullable(list.get(0)).map(SimpleResponse::getFinishTime).orElse(null);
        }
        return null;
    }

    private boolean isSyncCompleted(Long surveyId) {
        String syncKey = syncKey(surveyId);
        String sync = valueOpt().get(syncKey);
        return sync != null && sync.equals(SYNC_COMPLETE);
    }

    void async(Long surveyId) {
        executorService.execute(() -> sync(surveyId));
    }

    private void sync(Long surveyId) {
        String syncKey = syncKey(surveyId);
        Boolean sync = valueOpt().setIfAbsent(syncKey, SYNC_PROGRESS);
        if (sync != null && sync) {
            String idKey = idKey(surveyId);
            String ipKey = ipKey(surveyId);
            String clientIdKey = clientIdKey(surveyId);
            try {
                int page = 0;
                boolean hasNext = true;
                while (hasNext) {
                    List<SimpleResponse> list = surveyResponseRepository.findSimpleBySurveyIdAndIsCompletedIsTrue(surveyId, PageRequest.of(page, SIZE_PER_PAGE));
                    if (CollectionUtils.isNotEmpty(list)) {
//                        cacheItems(idKey, ipKey, clientIdKey, list);
                        cacheItems(idKey, ipKey, null, list);
                        page++;
                    } else {
                        hasNext = false;
                    }
                }
            } finally {
                expireKey(idKey);
                expireKey(ipKey);
                expireKey(clientIdKey);
                syncCompleted(syncKey);
            }
        }
    }

    private void cacheItems(String idKey, String ipKey, String clientIdKey, List<SimpleResponse> list) {
        Set<ZSetOperations.TypedTuple<String>> ipSet = new HashSet<>();
        Set<ZSetOperations.TypedTuple<String>> idSet = new HashSet<>();
//        Set<ZSetOperations.TypedTuple<String>> clientIdSet = new HashSet<>();
        list.forEach(i -> {
            if (StringUtils.isNotEmpty(i.getIp())) {
                double score = Optional.ofNullable(i.getFinishTime()).orElse(new Date()).getTime();
                ipSet.add(new DefaultTypedTuple<>(i.getIp(), score));
            }
            double score = Optional.ofNullable(i.getStatus()).orElse(ResponseStatus.DELETED).ordinal();
            idSet.add(new DefaultTypedTuple<>(i.getId().toString(), score));
//            clientIdSet.add(new DefaultTypedTuple<>(i.getClientId(), i.getId().doubleValue()));
        });
        if (!ipSet.isEmpty()) {
            zSetOpt().add(ipKey, ipSet);
        }
        if (!idSet.isEmpty()) {
            zSetOpt().add(idKey, idSet);
        }
//        if (!clientIdKey.isEmpty()) {
//            zSetOpt().add(clientIdKey, clientIdSet);
//        }
    }

    private String syncKey(Long surveyId) {
        return String.format(K_SYNC, surveyId);
    }

    private String ipKey(Long surveyId) {
        return String.format(K_IP, surveyId);
    }

    private String idKey(Long surveyId) {
        return String.format(K_ID, surveyId);
    }

    private String clientIdKey(Long surveyId) {
        return String.format(K_CLIENT_ID, surveyId);
    }
}
