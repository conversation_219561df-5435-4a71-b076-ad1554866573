package cn.hanyi.survey.client.service.submit;

import cn.hanyi.survey.client.cache.bean.CacheApiKey;
import cn.hanyi.survey.client.cache.bean.CacheDepartment;
import cn.hanyi.survey.client.dto.ClientChannel;
import cn.hanyi.survey.client.dto.ClientQuestion;
import cn.hanyi.survey.client.dto.ClientQuota;
import cn.hanyi.survey.client.dto.ClientSurvey;
import cn.hanyi.survey.core.constant.channel.SurveyCollectorMethod;
import cn.hanyi.survey.core.entity.SurveyResponse;
import cn.hanyi.survey.core.entity.SurveyResponseCell;
import cn.hanyi.survey.core.entity.SurveySendRecord;

import java.util.List;
import java.util.Map;

/**
 * 提供{@link SubmitContext}数据的获取方法
 */
public interface ISubmitLoader {

    Long requireSurveyId(SubmitContext context);

    Long requireOrgId(SubmitContext context);

    String requireClientId(SubmitContext context);

    ClientSurvey requireSurvey(SubmitContext context);

    List<ClientQuota> getQuotas(SubmitContext context);

    List<ClientQuestion> requireQuestions(SubmitContext context);

    Map<String, ClientQuestion> requireQuestionNameMap(SubmitContext context);

    Map<Long, ClientQuestion> requireQuestionIdMap(SubmitContext context);

    SurveyResponse requireResponse(SubmitContext context);

    ClientChannel getChannel(SubmitContext context);

    SurveySendRecord getChannelRecord(SubmitContext context);

    String getIp(SubmitContext context);

    List<SurveyResponseCell> requireOldCells(SubmitContext context);

    List<SurveyResponseCell> requireNewCells(SubmitContext context);

    CacheDepartment getDepartment(SubmitContext context);

    CacheApiKey getApikey(SubmitContext context);

    String requireExternalUserId(SubmitContext context);

    SurveyCollectorMethod requireSurveyCollectorMethod(SubmitContext context);

    /**
     * 通过传入的参数，加载完整的 SubmitContext 数据
     */
    void loadAllParams(SubmitContext context);

    /**
     * 获取问卷结构时，构造返回的完整信息
     */
    void startSurvey(SubmitContext context);

    /**
     * 比较旧的列表和新的列表，计算出 新增 修改 删除 的答案
     */
    void compareCells(SubmitContext context);

    /**
     * 同步最终结果
     * 写入数据库
     * 写入缓存
     */
    void sync(SubmitContext context);

    /**
     * 更新collectorMethod
     * 写入数据库
     * 写入缓存
     */
    void updateCollectorMethod(SubmitContext context);

}
