package cn.hanyi.survey.client.cache;

import cn.hanyi.survey.client.cache.hash.ApiKeyCacheHelper;
import cn.hanyi.survey.client.cache.hash.DepartmentCacheHelper;
import cn.hanyi.survey.client.dto.ClientChannel;
import cn.hanyi.survey.client.service.submit.SubmitContext;
import cn.hanyi.survey.core.entity.SurveyResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;

@Service
public class UpdateCacheHelper {

    @Autowired
    private SurveyCacheHelper surveyCacheHelper;
    @Autowired
    private ResponseCacheHelper responseCacheHelper;
    @Autowired
    private LimitSurveyCacheHelper limitSurveyCacheHelper;
    @Autowired
    private LimitChannelCacheHelper limitChannelCacheHelper;
    @Autowired
    private DepartmentCacheHelper departmentCacheHelper;
    @Autowired
    private ApiKeyCacheHelper apiKeyCacheHelper;
    @Autowired
    private LimitMobileCacheHelper limitMobileCacheHelper;

    /**
     * 获取问卷，提交问卷后更新缓存
     */
    public void changeBySubmit(SubmitContext context) {
        Long surveyId = context.getSurveyId();
        SurveyResponse response = context.getResponse();
        responseCacheHelper.setCache(surveyId, context.getClientId(), response, context.getNewCells());
//        limitSurveyCacheHelper.add(context.getSurveyId(), response.getId(), context.getClientId());
        if (context.isSubmit() && context.getData().getIsCompleted()) {
            double finishTime = response.getFinishTime().getTime();
            String ip = context.getIp();
            limitSurveyCacheHelper.add(context.getSurvey(), response.getId(), response.getIp(), finishTime, response.getStatus());
            ClientChannel channel = context.getChannel();
            if (channel != null) {
                String openId = response.getOpenid();
                if (StringUtils.isNotEmpty(ip) || StringUtils.isNotEmpty(openId)) {
                    limitChannelCacheHelper.add(channel, ip, openId, finishTime);
                }
            }
            limitMobileCacheHelper.add(surveyId, response.getId(), context.getQuestions(), context.getData().getData());
        }
    }

    /**
     * 重置apiKey时更新
     */
    public void changeByRefreshApiKey(Long orgId) {
        apiKeyCacheHelper.remove(orgId);
    }

    /**
     * 修改部门时更新
     */
    public void changeByUpdateDepartment(@NotNull Long orgId, @NotNull Long departmentId, String code) {
        departmentCacheHelper.remove(departmentId, orgId, code);
    }
}
