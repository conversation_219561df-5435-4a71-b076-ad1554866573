package cn.hanyi.survey.client.service.luckyDraw;

import cn.hanyi.survey.core.constant.lottery.LotteryType;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2022/11/7 15:53:34
 */
public class LuckyDrawMap {

    private static Map<LotteryType, LuckyDrawService> luckyDrawMap = new ConcurrentHashMap();

    public static void put(LotteryType type, LuckyDrawService luckyDraw) {
        luckyDrawMap.put(type, luckyDraw);
    }

    public static LuckyDrawService get(LotteryType type) {
        return luckyDrawMap.get(type);
    }

    public static Map<LotteryType, LuckyDrawService> getData() {
        return luckyDrawMap;
    }
}
