package cn.hanyi.survey.client.service.submit.validator.action;

import cn.hanyi.survey.client.dto.ClientQuestion;

public interface ValidatorNotNull {

    default String validateNotNull(ClientQuestion question, Object valueObject) {
        boolean success = true;
        StringBuilder stringBuilder = new StringBuilder();
        if (valueObject == null) {
            success = false;
        }
        if (!success) {
            stringBuilder.append("目标值：不能为null，").append("实际值：null").append(", 不匹配");
            return stringBuilder.toString();
        }
        return null;
    }
}
