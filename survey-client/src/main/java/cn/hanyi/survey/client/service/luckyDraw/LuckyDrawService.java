package cn.hanyi.survey.client.service.luckyDraw;

import cn.hanyi.survey.client.constant.SurveyLotteryConstant;
import cn.hanyi.survey.client.exception.LotteryException;
import cn.hanyi.survey.core.constant.lottery.LotteryType;
import cn.hanyi.survey.core.constant.lottery.SurveyLotteryStatus;
import cn.hanyi.survey.core.dto.lottery.SurveyLotteryParam;
import cn.hanyi.survey.core.entity.SurveyLottery;
import org.springframework.data.redis.core.RedisTemplate;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/11/7 15:50:00
 */
public interface LuckyDrawService {

    LotteryType type();

    Object luckyDraw(Long lotteryId, SurveyLotteryParam param);

    /**
     * 判断上一次抽奖是否结束
     */
    default void checkLotteryStatus(Long responseId, RedisTemplate redisTemplate) {
        String key = String.format(SurveyLotteryConstant.LAST_LOTTERY_OVER_LOCK, responseId);
        Boolean isOver = redisTemplate.opsForValue().setIfAbsent(key, "1", 10, TimeUnit.SECONDS);
        if (!isOver) {
            throw new LotteryException("上一次抽奖未结束");
        }
    }

    /**
     * 检查抽奖活动是否可以抽奖
     *
     * @param lottery
     */
    default void checkLottery(SurveyLottery lottery) {
        Date now = new Date();
        if (lottery.getStatus().equals(SurveyLotteryStatus.PAUSING) || lottery.getStatus().equals(SurveyLotteryStatus.CLOSED))
            throw new LotteryException("活动已结束");
        if ((lottery.getIsTimeLimit() && lottery.getEndTime() != null && lottery.getEndTime().before(now)))
            throw new LotteryException("活动已结束");
        if ((lottery.getIsTimeLimit() && lottery.getStartTime() != null && lottery.getStartTime().after(now)))
            throw new LotteryException("活动未开始");
    }

    default String getYearMonthDay() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(new Date());
    }

    /**
     * 获取抽奖次数
     *
     * @param lotteryNum
     */
    default void checkLotteryNum(Integer lotteryNum) {
        if (lotteryNum == null || lotteryNum <= 0) {
            throw new LotteryException("抽奖次数已用完");
        }
    }
}
