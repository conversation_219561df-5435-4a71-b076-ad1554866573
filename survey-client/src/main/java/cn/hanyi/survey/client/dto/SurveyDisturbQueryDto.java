package cn.hanyi.survey.client.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.query.ResourceCustomQueryDto;

@Getter
@Setter
public class SurveyDisturbQueryDto extends ResourceCustomQueryDto {
    @JsonProperty("_q")
    @Schema(description = "关键字查询", example = "_q=abc")
    private String q = null;

    @Schema(description = "规则名称")
    private String name;

    @Schema(description = "问卷ID")
    private String sids;

    @Schema(description = "问卷名称")
    private String titles;

    @Schema(description = "启用状态 0:未设置，1:回收中")
    private Boolean status;


}
