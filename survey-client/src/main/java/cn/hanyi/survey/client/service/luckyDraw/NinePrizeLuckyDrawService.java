package cn.hanyi.survey.client.service.luckyDraw;

import cn.hanyi.survey.client.constant.SurveyLotteryConstant;
import cn.hanyi.survey.client.exception.LotteryException;
import cn.hanyi.survey.core.constant.ResponseStatus;
import cn.hanyi.survey.core.constant.lottery.LotteryPrizeType;
import cn.hanyi.survey.core.constant.lottery.LotteryType;
import cn.hanyi.survey.core.constant.lottery.PrizeSendStatus;
import cn.hanyi.survey.core.dto.lottery.LotteryNumLimitResult;
import cn.hanyi.survey.core.dto.lottery.SurveyClientLotteryResult;
import cn.hanyi.survey.core.dto.lottery.SurveyLotteryParam;
import cn.hanyi.survey.core.entity.SurveyLottery;
import cn.hanyi.survey.core.entity.SurveyLotteryPrize;
import cn.hanyi.survey.core.entity.SurveyLotteryPrizeWinner;
import cn.hanyi.survey.core.entity.SurveyResponse;
import cn.hanyi.survey.core.repository.SurveyLotteryPrizeRepository;
import cn.hanyi.survey.core.repository.SurveyLotteryPrizeWinnerRepository;
import cn.hanyi.survey.core.repository.SurveyLotteryRepository;
import cn.hanyi.survey.core.repository.SurveyResponseRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/9/21 18:11:17
 */
@Service
@Slf4j
public class NinePrizeLuckyDrawService implements LuckyDrawService {
    
    @Autowired
    private SurveyLotteryRepository surveyLotteryRepository;

    @Autowired
    private SurveyLotteryPrizeRepository surveyLotteryPrizeRepository;

    @Autowired
    private SurveyResponseRepository surveyResponseRepository;

    @Autowired
    private SurveyLotteryPrizeWinnerRepository surveyLotteryPrizeWinnerRepository;

    @Autowired
    private StringRedisTemplate redisTemplate;


    @Override
    public LotteryType type() {
        return LotteryType.NINE_PRIZE;
    }

    /**
     * 九宫格抽奖
     *
     * @param lotteryId
     * @return 抽奖次数、
     */
    @Transactional(rollbackFor = Exception.class)
    public SurveyClientLotteryResult luckyDraw(Long lotteryId, SurveyLotteryParam param) {
        log.info("九宫格抽奖开始...");
        Long responseId = param.getResponseId();
        SurveyResponse response = surveyResponseRepository.findById(responseId).orElseThrow(() -> new LotteryException("答卷不存在"));
        //判断抽奖次数是否为0
        checkLotteryNum(response.getLotteryNum());
        //活动是否在活动期限内
        SurveyLottery lottery = surveyLotteryRepository.findById(lotteryId).orElseThrow(() -> new LotteryException("活动已结束"));
        checkLottery(lottery);

        //是否正常结束判断
        if (lottery.getIsComplete() && ResponseStatus.EARLY_COMPLETED.equals(response.getStatus())) {
            throw new LotteryException("非常抱歉，您不满足本次抽奖条件");
        }
        //所有奖品
        List<SurveyLotteryPrize> lotteryList = surveyLotteryPrizeRepository.findByLottery(lottery, Sort.by("sequence").ascending());
        //抽奖
        int index = doDraw(lotteryList);
        SurveyLotteryPrize prize = lotteryList.get(index);
        //奖品更新检查
        checkPrizeUpdateLock(prize.getId());

        //防止重复点击
        checkLotteryStatus(responseId, redisTemplate);

        String code = null;
        LotteryNumLimitResult prizeNumLimit = null;
        SurveyLotteryPrizeWinner prizeWinner = null;
        try {
            //只记录中奖的
            if (prize.getIsPrize()) {
                //当日中奖额度判断
                prizeNumLimit = lotteryNumLimit(lotteryId);
                if (prizeNumLimit.getIsPrizeNumLimit()) {
                    //奖品额度到达限制后，抽奖次数也需要减一
                    log.info("额度达到限制");
                    response.setLotteryNum(response.getLotteryNum() - 1);
                    surveyResponseRepository.save(response);
                    return new SurveyClientLotteryResult(getThanksIndex(lotteryList), response.getLotteryNum(), null);
                }

                //虚拟奖品需要返回兑换码
                if (prize.getType().equals(LotteryPrizeType.VIRTUAL_PRIZE)) {
                    code = popCode(prize);
                    log.info("兑换码code：{}", code);
                    if (code == null) {
                        if (prizeNumLimit.getPrizeNumLimitStatus() >= 0) {
                            //回退中奖数量
                            redisTemplate.opsForValue().decrement(String.format(SurveyLotteryConstant.TODAY_PRIZE_LIMIT, lotteryId, getYearMonthDay()));
                        }
                        //存在null情况返回谢谢参与
                        response.setLotteryNum(response.getLotteryNum() - 1);
                        surveyResponseRepository.save(response);
                        return new SurveyClientLotteryResult(getThanksIndex(lotteryList), response.getLotteryNum(), null);
                    }
                }

                // 保存抽中的奖品
                prizeWinner = surveyLotteryPrizeWinnerRepository.save(getPrizeWinner(prize, responseId, code));
                int updatedRows = surveyLotteryPrizeRepository.updateWinnerStats(prize.getId(), 0);
                if (updatedRows == 0) {
                    // 更新失败，可能是并发冲突，抛出异常触发重试
                    throw new LotteryException("奖品统计更新失败，请重试");
                }

                //所有奖品中奖数量，包括中途更改奖品类型的
                int lotteryUpdatedRows = surveyLotteryRepository.updateTotalWinnerNum(lotteryId);
                if (lotteryUpdatedRows == 0) {
                    // 更新失败，可能是并发冲突，抛出异常触发重试
                    throw new LotteryException("抽奖统计更新失败，请重试");
                }
            }
            //  抽奖次数,是否中奖都减一
            response.setLotteryNum(response.getLotteryNum() - 1);
            surveyResponseRepository.save(response);
            Long prizeWinnerId = prizeWinner != null ? prizeWinner.getId() : null;
            return new SurveyClientLotteryResult(index, response.getLotteryNum(), code, prizeWinnerId);
        } catch (Exception e) {
            log.info("发生异常：{}", e);
            if (prize != null && prize.getIsPrize()) {
                if (prize.getType().equals(LotteryPrizeType.VIRTUAL_PRIZE) && code != null) {
                    //回退兑换码
                    redisTemplate.opsForList().rightPush(String.format(SurveyLotteryConstant.PRIZE_CODES, prize.getId()), code);
                }
                if (prizeNumLimit.getPrizeNumLimitStatus() >= 0) {
                    //回退中奖数量
                    redisTemplate.opsForValue().decrement(String.format(SurveyLotteryConstant.TODAY_PRIZE_LIMIT, lotteryId, getYearMonthDay()));
                }
            }
            //随机返回一个谢谢参与，不让前端一直转
            response.setLotteryNum(response.getLotteryNum() - 1);
            surveyResponseRepository.save(response);
            return new SurveyClientLotteryResult(getThanksIndex(lotteryList), response.getLotteryNum(), null);
        } finally {
            redisTemplate.delete(String.format(SurveyLotteryConstant.LAST_LOTTERY_OVER_LOCK, responseId));
        }
    }

    /**
     * 判断上一次抽奖是否结束
     */
    void checkLotteryStatus(Long responseId) {
        String key = String.format(SurveyLotteryConstant.LAST_LOTTERY_OVER_LOCK, responseId);
        Boolean isOver = redisTemplate.opsForValue().setIfAbsent(key, "1", 10, TimeUnit.SECONDS);
        if (!isOver) {
            throw new LotteryException("上一次抽奖未结束");
        }
    }

    /**
     * 抽奖过程中判断奖品是否在被更新
     */
    public void checkPrizeUpdateLock(Long prizeId) {
        String s = redisTemplate.opsForValue().get(String.format(SurveyLotteryConstant.UPDATE_PRIZE_LOCK, prizeId));
        if (s != null) {
            throw new LotteryException("系统异常");
        }
    }

    /**
     * 扣减兑换码
     *
     * @param prize
     * @return
     */
    public String popCode(SurveyLotteryPrize prize) {
        if (prize.getType().equals(LotteryPrizeType.VIRTUAL_PRIZE)) {
            String prizeCode = String.format(SurveyLotteryConstant.PRIZE_CODES, prize.getId());
            String code = redisTemplate.opsForList().rightPop(prizeCode);
            return code;
        }
        return null;
    }

    /**
     * 保存中奖信息
     *
     * @param prize
     */
    public SurveyLotteryPrizeWinner getPrizeWinner(SurveyLotteryPrize prize, Long responseId, String code) {
        SurveyLotteryPrizeWinner winner = SurveyLotteryPrizeWinner.builder()
                .surveyId(prize.getLottery().getSid())
                .lotteryId(prize.getLottery().getId())
                .prizeId(prize.getId())
                .responseId(responseId)
                .type(prize.getType())
                .status(PrizeSendStatus.NOT_SEND)
                .prizeCode(code)
                .prizeName(prize.getPrizeName())
                .build();
        //虚拟奖品默认已发放
        if (prize.getType().equals(LotteryPrizeType.VIRTUAL_PRIZE)) {
            winner.setStatus(PrizeSendStatus.IS_SENT);
            winner.setSendTime(new Date());
        }
        return winner;
    }

    /**
     * 判断当天中奖的数量是否大于配置的额度
     *
     * @return
     */
    public LotteryNumLimitResult lotteryNumLimit(Long lotteryId) {
        LotteryNumLimitResult result = new LotteryNumLimitResult();
        //奖品限额数量
        SurveyLottery surveyLottery = surveyLotteryRepository.findById(lotteryId).orElseThrow(() -> new LotteryException("活动已经失效"));
        //不存在等于1，当日奖品限额，每次中奖都新增
        Long winnerSum = redisTemplate.opsForValue().increment(String.format(SurveyLotteryConstant.TODAY_PRIZE_LIMIT, lotteryId, getYearMonthDay()));

        if (!surveyLottery.getIsLimit()) {
            return result;
        }
        if (surveyLottery.getIsLimit() && surveyLottery.getPrizeLimit() == null) {
            return result;
        }
        boolean res = winnerSum > surveyLottery.getPrizeLimit();
        result.setPrizeNumLimitStatus(winnerSum);
        result.setIsPrizeNumLimit(res);
        if (res) {
            //奖品限额,返回谢谢参与，回退中奖数量
            redisTemplate.opsForValue().decrement(String.format(SurveyLotteryConstant.TODAY_PRIZE_LIMIT, lotteryId, getYearMonthDay()));
        }
        return result;
    }

    /**
     * 随机返回一个谢谢参与的索引
     *
     * @return
     */
    public Integer getThanksIndex(List<SurveyLotteryPrize> lotteryList) {
        List<Long> collect = lotteryList.stream().map(x -> {
            return x.getId();
        }).collect(Collectors.toList());
        List<SurveyLotteryPrize> thanks = lotteryList.stream().filter(x -> !x.getIsPrize()).collect(Collectors.toList());
        Collections.shuffle(thanks);
        return collect.indexOf(thanks.get(0).getId());
    }


    /**
     * 九宫格抽奖算法
     * * 概率分别是 A:10， B:20， C:30， D:40    总：100
     * * A= 10/100 = 0.1
     * * B= (10+20)/100 = 0.3
     * * C= (10+20+30)/100 = 0.6
     * * D= (10+20+30+40)/100 = 1
     * * 随机数：[0,1)
     * * 如果随机数小于0.1索引等于0，小于0.3索引等于1,以此类推
     *
     * @param lotteryList 所有奖品
     * @return
     */
    public int doDraw(List<SurveyLotteryPrize> lotteryList) {
        //所有奖品和奖品数量不等于0的概率：如果数据量等于0概率分配到谢谢参与
        Double prizeRateSum = lotteryList.stream().filter(x -> x.getIsPrize() && x.getNumber() > x.getWinnerNum()).mapToDouble(SurveyLotteryPrize::getPercent).sum();

        //总概率=100
        Double countRate = 100.0;
        Double thanksRateSum = countRate - prizeRateSum;
        //谢谢参与个数
        long count = lotteryList.stream().filter(x -> !x.getIsPrize()).count();
        //每个谢谢参与的平均概率
        Double thanksRate = thanksRateSum / count;

        //保存每个奖品的概率区间
        List<Double> rates = new ArrayList<>();
        Double sum = 0.0;
        for (SurveyLotteryPrize prize : lotteryList) {
            //奖品数量为0，概率变为0
            if (prize.getIsPrize() && prize.getWinnerNum() >= prize.getNumber()) {
                sum = sum + 0.0;
                rates.add(sum / countRate);
                continue;
            }
            //谢谢参与：增加谢谢参与的平均概率
            if (!prize.getIsPrize()) {
                sum = sum + thanksRate;
            } else {
                sum = sum + prize.getPercent();
            }
            rates.add(sum / countRate);
        }
        Double random = new Random().nextDouble();
        rates.add(random);
        Collections.sort(rates);
        //抽中的奖品索引
        return rates.indexOf(random);
    }
}






















