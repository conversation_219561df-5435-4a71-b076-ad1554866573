package cn.hanyi.survey.client.service.submit.validator.action;

import cn.hanyi.survey.client.dto.ClientQuestion;

public interface ValidatorMinMax {

    default String validateMinMax(ClientQuestion question, Object valueObject) {
        boolean success = true;
        StringBuilder stringBuilder = new StringBuilder();
        if (valueObject instanceof Number) {
            double value = ((Number) valueObject).doubleValue();
            if (question.getMin() != null) {
                if (value < question.getMin()) {
                    success = false;
                    stringBuilder.append("最小值：").append(question.getMin());
                }
            }
            if (question.getMax() != null) {
                if (value > question.getMax()) {
                    success = false;
                    stringBuilder.append("最大值：").append(question.getMax());
                }
            }
        }
        if (!success) {
            stringBuilder.append(", ").append("实际值：").append(valueObject).append(", 不匹配");
            return stringBuilder.toString();
        }
        return null;
    }
}
