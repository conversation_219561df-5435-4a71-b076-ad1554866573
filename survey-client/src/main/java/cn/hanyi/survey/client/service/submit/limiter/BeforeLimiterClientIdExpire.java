package cn.hanyi.survey.client.service.submit.limiter;

import cn.hanyi.ctm.dto.customer.CustomerClientIdParamDto;
import cn.hanyi.survey.client.service.submit.IBeforeLimiter;
import cn.hanyi.survey.client.service.submit.ISubmitLoader;
import cn.hanyi.survey.client.service.submit.SubmitContext;
import cn.hanyi.survey.core.exception.DateLimitException;
import org.befun.core.utils.DateHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Order(1010)
@Component
@ConditionalOnProperty(value = "survey.client.enabled", havingValue = "true")
public class BeforeLimiterClientIdExpire implements IBeforeLimiter {

    @Autowired
    private ISubmitLoader loader;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public void check(SubmitContext context) {
        String clientId = loader.requireClientId(context);
        String expireKey = CustomerClientIdParamDto.clientIdParamKey(clientId);
        Object value = stringRedisTemplate.opsForHash().get(expireKey, "expireTime");
        LocalDateTime expireTime;
        if (value != null && (expireTime = DateHelper.parseAdjust(value.toString())) != null && LocalDateTime.now().isAfter(expireTime)) {
            throw new DateLimitException("抱歉，您访问的页面已过期！");
        }
    }
}
