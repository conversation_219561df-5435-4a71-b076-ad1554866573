package cn.hanyi.survey.client.utils;

import cn.hanyi.survey.client.dto.SurveyParamsRequestEncryptedDto;
import cn.hanyi.survey.client.dto.SurveyParamsResponseEncryptedDto;
import cn.hanyi.survey.client.dto.SurveySubmitRequestEncryptedDto;
import cn.hanyi.survey.core.dto.SurveySubmitRequestDto;
import org.apache.commons.collections4.MapUtils;
import org.befun.auth.configuration.AuthProperties;
import org.befun.auth.utils.EncryptDecryptUtils;
import org.befun.core.exception.BadRequestException;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.service.LinkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class EncryptDecryptHelper {

    @Autowired
    private AuthProperties authProperties;
    @Autowired
    private LinkService linkService;

    public SurveyParamsResponseEncryptedDto encryptParams(String shortCode, SurveyParamsRequestEncryptedDto requestDto) {
        Map<String, Object> params = linkService.getSurveyParamsByCode(shortCode);
        if (MapUtils.isEmpty(params)) {
            throw new BadRequestException("短链编号不存在");
        }
        String formatParams = JsonHelper.toJson(params);
        String encryptedParams;
        if ("sm2/sm4".equals(requestDto.getEncryptType())) {
            encryptedParams = encryptBySm4(decryptBySm2(requestDto.getEncryptedKey()), formatParams);
        } else {
            encryptedParams = encryptByAes(decryptByRsa(requestDto.getEncryptedKey()), formatParams);
        }
        return new SurveyParamsResponseEncryptedDto(encryptedParams);
    }

    public SurveySubmitRequestDto decrypt(SurveySubmitRequestEncryptedDto encryptedDto) {
        if ("sm2/sm4".equals(encryptedDto.getEncryptType())) {
            return decryptBySm2Sm4(encryptedDto.getEncryptedKey(), encryptedDto.getEncryptedData());
        } else {
            return decryptByRsaAes(encryptedDto.getEncryptedKey(), encryptedDto.getEncryptedData());
        }
    }

    private String decryptByRsa(String encryptedKey) {
        String base64Key = EncryptDecryptUtils.decryptRsa(authProperties.getRsaPrivateKey(), authProperties.getRsaModPadding(), encryptedKey);
        if (base64Key == null) {
            throw new BadRequestException("RSA解密AES秘钥失败");
        }
        return base64Key;
    }

    private SurveySubmitRequestDto decryptByRsaAes(String encryptedKey, String encryptedData) {
        String base64Key = decryptByRsa(encryptedKey);
        String content = EncryptDecryptUtils.decryptAes(base64Key, authProperties.getAesModPadding(), encryptedData);
        if (content == null) {
            throw new BadRequestException("AES解密提交数据失败");
        }
        SurveySubmitRequestDto data = JsonHelper.toObject(content, SurveySubmitRequestDto.class);
        if (data == null) {
            data = new SurveySubmitRequestDto();
        }
        data.setEncryptType("rsa/aes");
        data.setBase64Key(base64Key);
        return data;
    }

    private String decryptBySm2(String encryptedKey) {
        String base64Key = EncryptDecryptUtils.decryptSm2(authProperties.getSm2PrivateKey(), encryptedKey);
        if (base64Key == null) {
            throw new BadRequestException("SM2解密SM4秘钥失败");
        }
        return base64Key;
    }

    private SurveySubmitRequestDto decryptBySm2Sm4(String encryptedKey, String encryptedData) {
        String base64Key = decryptBySm2(encryptedKey);
        String content = EncryptDecryptUtils.decryptSm4(base64Key, encryptedData);
        if (content == null) {
            throw new BadRequestException("SM4解密提交数据失败");
        }
        SurveySubmitRequestDto data = JsonHelper.toObject(content, SurveySubmitRequestDto.class);
        if (data == null) {
            data = new SurveySubmitRequestDto();
        }
        data.setEncryptType("sm2/sm4");
        data.setBase64Key(base64Key);
        return data;
    }

    public String encrypt(String encryptType, String base64Key, String originData) {
        if ("sm2/sm4".equals(encryptType)) {
            return encryptBySm4(base64Key, originData);
        } else {
            return encryptByAes(base64Key, originData);
        }
    }

    private String encryptByAes(String base64Key, String originData) {
        if (base64Key == null) {
            return null;
        }
        String content = EncryptDecryptUtils.encryptAes(base64Key, authProperties.getAesModPadding(), originData);
        if (content == null) {
            throw new BadRequestException("AES加密提交数据失败");
        }
        return content;
    }

    private String encryptBySm4(String base64Key, String originData) {
        if (base64Key == null) {
            return null;
        }
        String content = EncryptDecryptUtils.encryptSm4(base64Key, originData);
        if (content == null) {
            throw new BadRequestException("SM4加密提交数据失败");
        }
        return content;
    }


}
