package cn.hanyi.survey.client.cache.hash;

import cn.hanyi.survey.client.cache.BaseCacheHelper;
import cn.hanyi.survey.client.cache.bean.CacheApiKey;
import cn.hanyi.survey.client.cache.bean.ParamOrgId;
import cn.hanyi.survey.client.service.submit.ISubmitStorage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 所有数据都存在一个hash结构中，通过hashKey获取数据
 * key              hashKey             value
 * cache:api-key    {orgId}             {CacheApiKey}
 */
@Slf4j
@Service
public class ApiKeyCacheHelper extends BaseCacheHelper<ParamOrgId, CacheApiKey> {

    private static final String K = "cache:api-key";
    private static final String K_LOCK = "cache:api-key:%s-lock";

    @Autowired
    protected ISubmitStorage storage;

    public ApiKeyCacheHelper() {
        super("api key", false);
    }

    public CacheApiKey get(Long orgId) {
        if (!enableCache) {
            return getDbValue(new ParamOrgId(orgId));
        }
        return getOrSync(new ParamOrgId(orgId));
    }

    public void remove(Long orgId) {
        ParamOrgId param = new ParamOrgId(orgId);
        hashOpt().delete(key(param), param.value());
    }

    @Override
    protected String key(ParamOrgId param) {
        return K;
    }

    @Override
    protected String lockKey(String key, ParamOrgId param) {
        return String.format(K_LOCK, param.value());
    }

    @Override
    protected CacheApiKey getCacheValue(String key, ParamOrgId param) {
        Object cache = hashOpt().get(key, param.value());
        if (cache != null) {
            String s;
            if ((s = cache.toString()).equals(NOT_EXIST_FLAG)) {
                throw new RuntimeException(String.format("%s 不存在 %s", logText, param.log));
            } else {
                return JsonHelper.toObject(s, CacheApiKey.class);
            }
        }
        return null;
    }

    @Override
    protected CacheApiKey syncValue(String key, ParamOrgId param) {
        CacheApiKey value = getDbValue(param);
        if (value != null) {
            hashOpt().put(key, param.value(), JsonHelper.toJson(value));
            return value;
        } else {
            hashOpt().put(key, param.value(), NOT_EXIST_FLAG);
            log.info("{} 不存在，并标记 {}", logText, param.log);
            return null;
        }
    }

    @Override
    protected CacheApiKey getDbValue(ParamOrgId param) {
        String sql = String.format("select ak.api_key `key`, ak.api_secret secret from api_key ak where ak.user_id in " +
                "(select u.id from `user` u where u.org_id=%d and u.is_admin=1) limit 1", param.orgId);
        List<CacheApiKey> list = storage.nativeQueryList(sql, CacheApiKey.class);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }
}
