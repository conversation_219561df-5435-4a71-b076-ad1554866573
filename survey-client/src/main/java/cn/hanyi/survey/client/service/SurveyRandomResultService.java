package cn.hanyi.survey.client.service;

import cn.hanyi.survey.client.dto.SurveyRandomResultRequest;
import cn.hanyi.survey.core.constant.question.ItemsOrderType;
import cn.hanyi.survey.core.constant.question.QuestionRandomType;
import cn.hanyi.survey.core.entity.*;
import cn.hanyi.survey.core.repository.SurveyQuestionItemRepository;
import cn.hanyi.survey.core.repository.SurveyRandomResultRepository;
import cn.hanyi.survey.core.repository.SurveyResponseRepository;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.exception.BadRequestException;
import org.befun.core.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.hanyi.survey.core.utilis.QuestionsUtils.questionsFilterGroup;
import static cn.hanyi.survey.core.utilis.QuestionsUtils.questionsGroup;

/**
 * <AUTHOR>
 * @date 2022/12/2 16:52:21
 */
@Service
@Slf4j
public class SurveyRandomResultService extends BaseService<SurveyRandomResult, SurveyRandomResultDto, SurveyRandomResultRepository> {

    @Autowired
    private SurveyRandomResultRepository randomResultRepository;
    @Autowired
    private SurveyResponseRepository surveyResponseRepository;
    @Autowired
    private SurveyQuestionItemRepository surveyQuestionItemRepository;

    /**
     * 保存随机到的问题
     *
     * @param surveyId
     * @param responseId
     * @param data
     * @return
     */
    @Transactional
    public Boolean saveRandomResult(Long surveyId, Long responseId, List<SurveyRandomResultRequest> data) {
//        boolean results = randomResultRepository.existsBySurveyIdAndResponseId(surveyId, responseId);
//        if (results) {
//            log.info("答卷{}已随机过了", responseId);
//            return Boolean.FALSE;
//        }
        surveyResponseRepository.findOneBySurveyIdAndId(surveyId, responseId).orElseThrow(() -> new BadRequestException("答卷不存在"));
        List<SurveyRandomResult> list = new ArrayList<>();
        for (SurveyRandomResultRequest request : data) {
            addHiddenItem(surveyId, responseId, request, list);
            SurveyRandomResult result = getSurveyRandomResult(surveyId, responseId, request);
            list.add(result);
        }
        randomResultRepository.saveAll(list);
        return true;
    }

    /**
     * 保存隐藏的题目
     */
    public void addHiddenItem(Long surveyId, Long responseId, SurveyRandomResultRequest request, List<SurveyRandomResult> list) {
        //问题被隐藏，选项下载显示0
        if (QuestionRandomType.QUESTION.equals(request.getType()) && request.getIsShow() == false) {
            surveyQuestionItemRepository.findByQuestionId(request.getQuestionId()).ifPresent(items -> {
                items.forEach(x -> {
                    //选项随机是随机排序
                    if (ItemsOrderType.RANDOM == x.getQuestion().getItemsOrder()) {
                        SurveyRandomResult result = new SurveyRandomResult();
                        result.setSurveyId(surveyId);
                        result.setResponseId(responseId);
                        result.setQuestionId(request.getQuestionId());
                        result.setItemId(x.getId());
                        result.setType(QuestionRandomType.ITEM);
                        result.setIsShow(false);
                        result.setItemConfig("");
                        list.add(result);
                    }
                });
            });
        }
    }

    public SurveyRandomResult getSurveyRandomResult(Long surveyId, Long responseId, SurveyRandomResultRequest request) {
        SurveyRandomResult result = new SurveyRandomResult();
        result.setSurveyId(surveyId);
        result.setResponseId(responseId);
        result.setQuestionId(request.getQuestionId());
        result.setItemId(request.getItemId());
        result.setType(request.getType());
        result.setIsShow(request.getIsShow());
        result.setItemConfig(request.getItemConfig());
        return result;
    }

    /**
     * 随机结果表标题
     *
     * @param surveyId
     * @param questions
     * @return
     */
    public List getRandomResultHeader(Long surveyId, List<SurveyQuestion> questions) {
        List<SurveyRandomResult> randomResults = randomResultRepository.findBySurveyId(surveyId);
        List<String> randomResultHeader = new ArrayList<>();
        randomResultHeader.add("id");
        if (existRandomGroup(randomResults)) {
            //添加题组标题
            List<SurveyQuestion> groups = questionsGroup(questions);
            for (SurveyQuestion group : groups) {
                randomResultHeader.add(group.getCode() + "_display");
            }
        }
        if (existRandomQuestion(randomResults)) {
            //添加问题标题
            List<SurveyQuestion> questionList = questionsFilterGroup(questions);
            for (SurveyQuestion question : questionList) {
                randomResultHeader.add(question.getCode() + "_display");
            }
        }
        return randomResultHeader;
    }

    public Boolean existRandomGroup(List<SurveyRandomResult> randomResults) {
        return randomResults.stream().anyMatch(x -> QuestionRandomType.GROUP == x.getType());
    }

    public Boolean existRandomQuestion(List<SurveyRandomResult> randomResults) {
        return randomResults.stream().anyMatch(x -> QuestionRandomType.QUESTION == x.getType());
    }

    /**
     * 随机结果下载数据
     *
     * @param surveyId
     * @param questions
     * @return
     */
    public List<List> getRandomResultData(Long surveyId, List<SurveyQuestion> questions, List<SurveyResponse> surveyResponse) {
        List<SurveyRandomResult> randomResults = randomResultRepository.findBySurveyId(surveyId);
        //唯一key = questionId:responseId
        List<String> Ids = randomResults.stream().map(x -> x.getQuestionId() + ":" + x.getResponseId()).collect(Collectors.toList());
        Map<String, SurveyRandomResult> groupMap = randomResults.stream().collect(Collectors.toMap(x -> x.getQuestionId() + ":" + x.getResponseId(), Function.identity()));
        //是否显示题组或题目
        Boolean existRandomGroup = existRandomGroup(randomResults);
        Boolean existRandomQuestion = existRandomQuestion(randomResults);

        //分别获取题组活题目
        List<SurveyQuestion> questionsGroupList = questionsGroup(questions);
        List<SurveyQuestion> questionList = questionsFilterGroup(questions);

        List<List> result = new ArrayList<>();
        for (SurveyResponse response : surveyResponse) {
            List row = new ArrayList();
            row.add(response.getId() + "");
            //是否存在题组随机
            if (existRandomGroup) {
                //添加题组
                for (SurveyQuestion question : questionsGroupList) {
                    String key = question.getId() + ":" + response.getId();
                    SurveyRandomResult randomResult = groupMap.get(key);
                    if (randomResult != null) {
                        row.add(randomResult.getIsShow() ? 1 : 0);
                    } else {
                        //未添加随机的都显示
                        row.add(1);
                    }
                }
            }
            //是否存在题目随机
            if (existRandomQuestion) {
                //添加问题
                for (SurveyQuestion question : questionList) {
                    String key = question.getId() + ":" + response.getId();
                    SurveyRandomResult randomResult = groupMap.get(key);
                    if (randomResult != null) {
                        row.add(randomResult.getIsShow() ? 1 : 0);
                    } else {
                        //未添加随机的都显示
                        row.add(1);
                    }
                }
            }
            result.add(row);
        }
        return result;
    }

}

