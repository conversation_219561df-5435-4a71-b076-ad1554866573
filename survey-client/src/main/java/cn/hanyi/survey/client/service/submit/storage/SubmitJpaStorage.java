package cn.hanyi.survey.client.service.submit.storage;

import cn.hanyi.survey.client.dto.ClientChannel;
import cn.hanyi.survey.client.dto.ClientQuestion;
import cn.hanyi.survey.client.dto.ClientSurvey;
import cn.hanyi.survey.client.service.ClientCommonService;
import cn.hanyi.survey.client.service.submit.ISubmitStorage;
import cn.hanyi.survey.client.service.submit.SubmitContext;
import cn.hanyi.survey.core.entity.*;
import cn.hanyi.survey.core.repository.SurveyResponseCellRepository;
import cn.hanyi.survey.core.repository.SurveyResponseRepository;
import cn.hanyi.survey.core.repository.SurveySendRecordRepository;
import cn.hanyi.survey.core.service.SurveyBaseEntityService;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.core.service.MapperService;
import org.befun.extension.service.NativeSqlHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
public class SubmitJpaStorage implements ISubmitStorage {

    @Autowired
    private NativeSqlHelper nativeSqlHelper;
    @Autowired
    private SurveyBaseEntityService baseEntityService;
    @Autowired
    private SurveyResponseRepository surveyResponseRepository;
    @Autowired
    private SurveyResponseCellRepository surveyResponseCellRepository;
    @Autowired
    private SurveySendRecordRepository surveySendRecordRepository;
    @Autowired
    private MapperService mapperService;
    @Autowired
    private ClientCommonService clientCommonService;

    @Override
    public <T> List<T> nativeQueryList(String sql, Class<T> tClass) {
        return nativeSqlHelper.queryListObject(sql, tClass);
    }

    @Override
    public ClientSurvey getSurvey(Long surveyId) {
        Survey entity = baseEntityService.get(Survey.class, surveyId);
        if (entity != null) {
            return mapperService.map(entity, ClientSurvey.class);
        }
        return null;
    }

    @Override
    public SurveyResponse getResponse(Long responseId) {
        return baseEntityService.get(SurveyResponse.class, responseId);
    }

    @Override
    public SurveyResponse getResponse(Long surveyId, String clientId) {
        return surveyResponseRepository.findOneBySurveyIdAndClientIdOrderByCreateTimeAsc(surveyId, clientId).orElse(null);
    }

    @Override
    public SurveyResponse addResponse(SurveyResponse entity) {
        surveyResponseRepository.save(entity);
        return entity;
    }

    @Override
    public List<SurveyResponseCell> getCells(Set<Long> cellIds, Map<Long, ClientQuestion> idMap) {
        List<SurveyResponseCell> cells = surveyResponseCellRepository.findAllById(cellIds);
        return wrapCells(idMap, cells);
    }

    private List<SurveyResponseCell> wrapCells(Map<Long, ClientQuestion> idMap, List<SurveyResponseCell> cells) {
        if (CollectionUtils.isNotEmpty(cells)) {
            // 设置cell的qName
            cells.forEach(cell -> {
                ClientQuestion question = idMap.get(cell.getQuestionId());
                if (question != null) {
                    cell.setQName(question.getName());
                }
            });
            return cells;
        }
        return new ArrayList<>();
    }

    @Override
    public List<SurveyResponseCell> getCells(Long surveyId, Long responseId, Map<Long, ClientQuestion> idMap) {
        List<SurveyResponseCell> cells = surveyResponseCellRepository.findAllBySurveyIdAndResponseId(surveyId, responseId);
        return wrapCells(idMap, cells);
    }

    @Override
    public ClientChannel getChannel(Long channelId) {
        SurveyChannel entity = baseEntityService.get(SurveyChannel.class, channelId);
        if (entity != null) {
            return mapperService.map(entity, ClientChannel.class);
        }
        return null;
    }

    @Override
    public SurveySendRecord getChannelRecord(Long surveyId, Long channelId, String clientId) {
        return surveySendRecordRepository.findByClientIdAndChannelId(clientId, channelId).orElse(null);
    }

    @Override
    public void updateChannelRecord(SurveySendRecord record) {
        surveySendRecordRepository.save(record);
    }

    @Override
    @Transactional
    public void save(SubmitContext context) {
        updateResponse(context);
        updateCells(context);
        updateChannelRecord(context);
    }

    private void updateResponse(SubmitContext context) {
        if (context.isNeedUpdateResponse()) {
            surveyResponseRepository.save(context.getResponse());
        }
    }

    private void updateCells(SubmitContext context) {
        if (!context.getAddCells().isEmpty()) {
            surveyResponseCellRepository.saveAll(context.getAddCells());
        }
        if (!context.getUpdateCells().isEmpty()) {
            surveyResponseCellRepository.saveAll(context.getUpdateCells());
        }
        if (!context.getDeleteCells().isEmpty()) {
            surveyResponseCellRepository.deleteAll(context.getDeleteCells());
        }
    }

    private void updateChannelRecord(SubmitContext context) {
        if (context.isNeedUpdateChannelRecord()) {
            clientCommonService.updateRecodeReplyStatus(context.getChannelRecord(), context.getResponse().getId(), context.getData().getDurationSeconds());
        }
    }
}
