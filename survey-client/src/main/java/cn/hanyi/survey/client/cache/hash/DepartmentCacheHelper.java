package cn.hanyi.survey.client.cache.hash;

import cn.hanyi.survey.client.cache.BaseCacheHelper;
import cn.hanyi.survey.client.cache.bean.CacheDepartment;
import cn.hanyi.survey.client.cache.bean.ParamDepartment;
import cn.hanyi.survey.client.service.submit.ISubmitStorage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 所有数据都存在一个hash结构中，通过hashKey获取数据
 * hash
 * key                          hashKey             value
 * cache:department:{orgId}     {id}                {department}
 * #code 获取 id                 {code}              {id}
 */
@Slf4j
@Service
@Validated
public class DepartmentCacheHelper extends BaseCacheHelper<ParamDepartment, CacheDepartment> {

    private static final String K = "cache:department:%d";
    private static final String K_LOCK = "cache:department:%d:%s-lock";

    @Autowired
    protected ISubmitStorage storage;

    public DepartmentCacheHelper() {
        super("部门", false);
    }

    public CacheDepartment get(Long id, @NotNull Long orgId, String code) {
        if (!enableCache) {
            return getDbValue(new ParamDepartment(id, orgId, code));
        }
        CacheDepartment department = getOrSync(new ParamDepartment(id, orgId, code));
        if (department != null && !orgId.equals(department.getOrgId())) {
            department = null; // 不是当前企业的部门
        }
        return department;
    }

    public void remove(Long id, @NotNull Long orgId, String code) {
        ParamDepartment param = new ParamDepartment(id, orgId, code);
        String key = key(param);
        Set<Object> deleteKeys = new HashSet<>();
        if (param.hasCode()) {
            deleteKeys.add(param.hkCode());
        }
        if (param.hasId()) {
            deleteKeys.add(param.hkId());
            // 需要把之前存的code也查出来，一起删除
            CacheDepartment old = getCacheById(key, param.hkId(), false);
            if (old != null && StringUtils.isNotEmpty(old.hkCode())) {
                deleteKeys.add(old.hkCode());
            }
        }
        if (!deleteKeys.isEmpty()) {
            hashOpt().delete(key, deleteKeys.toArray(Object[]::new));
        }
    }

    @Override
    protected String key(ParamDepartment param) {
        return String.format(K, param.orgId);
    }

    @Override
    protected String lockKey(String key, ParamDepartment param) {
        return String.format(K_LOCK, param.orgId, param.value());
    }

    @Override
    protected CacheDepartment getCacheValue(String key, ParamDepartment param) {
        if (param.hasId()) {
            return getCacheById(key, param.hkId(), true);
        } else if (param.hasCode()) {
            return getCacheByCode(key, param.hkCode(), param.code);
        }
        return null;
    }

    private CacheDepartment getCacheById(String key, String hkId, boolean throwable) {
        String cache = hashOpt().get(key, hkId);
        if (cache != null) {
            if (cache.equals(NOT_EXIST_FLAG)) { // 标记了这个数据不存在
                if (throwable) {
                    throw new RuntimeException(String.format("%s 不存在 %s", logText, hkId));
                }
            } else {
                return JsonHelper.toObject(cache, CacheDepartment.class);
            }
        }
        return null;
    }

    private CacheDepartment getCacheByCode(String key, String hkCode, String code) {
        String cache = hashOpt().get(key, hkCode);
        if (cache != null) {
            if (cache.equals(NOT_EXIST_FLAG)) { // 标记了这个数据不存在
                throw new RuntimeException(String.format("%s 不存在 %s", logText, hkCode));
            } else {
                CacheDepartment department = null;
                try {
                    department = getCacheById(key, cache, true);
                } finally {
                    if (department == null || !code.equals(department.getCode())) {
                        // 如果这次还没有查到，或者查到的部门code不相同（code被修改了），则删除这个 hashKey
                        hashOpt().delete(key, hkCode);
                        department = null;
                    }
                }
                return department;
            }
        }
        return null;
    }

    @Override
    protected CacheDepartment syncValue(String key, ParamDepartment param) {
        CacheDepartment value = getDbValue(param);
        if (value != null) {
            Map<String, String> cache;
            String hkId = value.hkId();
            String khCode;
            if ((khCode = value.hkCode()) != null) {
                // 同时缓存 id 和 code
                // code -> id
                // id -> department
                cache = Map.of(khCode, hkId, hkId, JsonHelper.toJson(value));
            } else {
                // 缓存 id
                // id -> department
                cache = Map.of(hkId, JsonHelper.toJson(value));
            }
            hashOpt().putAll(key, cache);
            return value;
        } else {
            hashOpt().put(key, param.value(), NOT_EXIST_FLAG);
            log.info("{} 不存在，并标记 {}", logText, param.log);
            return null;
        }
    }

    @Override
    protected CacheDepartment getDbValue(ParamDepartment param) {
        List<CacheDepartment> list = storage.nativeQueryList(getValueSql(param), CacheDepartment.class);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    private String getValueSql(ParamDepartment param) {
        String where;
        if (param.hasId()) {
            where = String.format("id=%d", param.id);
        } else {
            where = String.format("org_id=%d and code='%s'", param.orgId, param.code);
        }
        return String.format("SELECT id,org_id orgId,title name, code FROM department where %s", where);
    }
}
