package cn.hanyi.survey.client.dto;

import cn.hanyi.survey.core.entity.BaseQuestion;
import cn.hanyi.survey.core.entity.SurveyQuestionColumn;
import cn.hanyi.survey.core.entity.SurveyQuestionItem;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class ClientQuestion extends BaseQuestion {

    private List<SurveyQuestionItem> items = new ArrayList<>();

    private List<SurveyQuestionColumn> columns = new ArrayList<>();
}
