package cn.hanyi.survey.client.service;

import cn.hanyi.common.ip.resolver.IpResolverService;
import cn.hanyi.common.ip.resolver.RegionInfo;
import cn.hanyi.survey.core.dto.SurveyTrackingDataDto;
import cn.hanyi.survey.core.utilis.RestUtils;
import com.google.common.base.Strings;
import org.befun.core.utils.IpHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import ua_parser.Client;
import ua_parser.Parser;

import javax.servlet.http.HttpServletRequest;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Service
public class TrackingService {
    private final Parser uaParser = new Parser();

    @Autowired
    private IpResolverService ipResolverService;

    public SurveyTrackingDataDto parseRequest(HttpServletRequest request) {
        SurveyTrackingDataDto result = new SurveyTrackingDataDto();

        String ipAddress = RestUtils.getClientIpAddress(request);
        String userAgent = request.getHeader("User-Agent");

        result.setUserAgent(userAgent);
        result.setIp(IpHelper.isIpv6(ipAddress) ? null : ipAddress);
        // parse UA
        if (!Strings.isNullOrEmpty(userAgent)) {
            Client c = uaParser.parse(userAgent);
            result.setDevice(c.device != null ? c.device.family : null);
            result.setOs(c.os != null ? c.os.family : null);
            result.setBrowser(c.userAgent != null ? c.userAgent.family : null);
        }

        RegionInfo regionInfo = ipResolverService.resolveIpToRegion(ipAddress);
        if (regionInfo != null) {
            result.setCountry(regionInfo.getCountry());
            result.setCity(regionInfo.getCity());
            result.setProvince(regionInfo.getProvince());
        }
        return result;
    }

    public SurveyTrackingDataDto parseIpAddress(String ipAddress) {
        SurveyTrackingDataDto result = new SurveyTrackingDataDto();

        result.setIp(ipAddress);

        RegionInfo regionInfo = ipResolverService.resolveIpToRegion(ipAddress);
        if (regionInfo != null) {
            result.setCountry(regionInfo.getCountry());
            result.setCity(regionInfo.getCity());
            result.setProvince(regionInfo.getProvince());
        }
        return result;
    }

}


