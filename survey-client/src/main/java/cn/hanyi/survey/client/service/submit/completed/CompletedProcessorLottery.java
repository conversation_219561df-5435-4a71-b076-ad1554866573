package cn.hanyi.survey.client.service.submit.completed;

import cn.hanyi.survey.client.service.submit.ICompletedProcessor;
import cn.hanyi.survey.client.service.submit.SubmitContext;
import cn.hanyi.survey.core.constant.lottery.LotteryType;
import cn.hanyi.survey.core.constant.lottery.SurveyLotteryStatus;
import cn.hanyi.survey.core.entity.SurveyLottery;
import cn.hanyi.survey.core.entity.SurveyLotteryDto;
import cn.hanyi.survey.core.entity.SurveyLotteryPrize;
import cn.hanyi.survey.core.repository.SurveyLotteryRepository;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.exception.BadRequestException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.annotation.Order;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Order(200)
@Component
@ConditionalOnProperty(value = "survey.client.enabled", havingValue = "true")
@Slf4j
public class CompletedProcessorLottery implements ICompletedProcessor {

    @Autowired
    private SurveyLotteryRepository surveyLotteryRepository;

    @Override
    public Boolean skip(SubmitContext context) {
        return context.getException() != null && context.getResponse() == null;
    }

    @Override
    public void onCompleted(SubmitContext context) {
        if (context.isSubmit() && context.getData().getIsCompleted()) {
            isShowLottery(context.getSurveyId(), context);
        }
    }

    /**
     * 是否展示抽奖活动
     *
     * @param surveyId
     * @return
     */
    public void isShowLottery(Long surveyId, SubmitContext context) {
        //问卷是否设置奖励渠道
        List<SurveyLottery> lotteries = surveyLotteryRepository.findBySid(surveyId, Sort.by("createTime").ascending());
        if (lotteries.isEmpty()) {
            log.info("问卷未设置奖励渠道");
            return;
        }
        List<SurveyLottery> openLottery = lotteries.stream().filter(x -> x.getStatus().equals(SurveyLotteryStatus.OPENING)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(openLottery)) {
            log.info("抽奖活动未启用");
            return;
        }
        //有奖品的活动
        SurveyLottery validLottery = this.getOneLottery(openLottery);
        //是否再活动期限内
        Date now = new Date();
        if (validLottery.getIsTimeLimit()) {
            if (((validLottery.getStartTime() != null && validLottery.getStartTime().after(now)) || (validLottery.getEndTime() != null && validLottery.getEndTime().before(now)))) {
                log.info("抽奖活动不在期限内");
                return;
            }
        }
        if (context.getData().getIsEarlyCompleted() && validLottery.getIsComplete()) {
            context.getResponseResultDto().setShowLottery(false);
        } else {
            context.getResponseResultDto().setShowLottery(true);
        }
        context.getResponseResultDto().setResponseId(context.getResponse().getId());
        context.getResponseResultDto().setLotteryNum(context.getResponse().getLotteryNum());
        context.getResponseResultDto().setIsComplete(validLottery.getIsComplete());
        context.getResponseResultDto().setLotteryType(validLottery.getLotteryType());

        SurveyLotteryDto dto = new SurveyLotteryDto();
        if (LotteryType.NINE_PRIZE.equals(validLottery.getLotteryType())) {
            validLottery.copyProperties(validLottery, dto);
            context.getResponseResultDto().setSurveyLottery(dto);
        } else if (LotteryType.WECHAT.equals(validLottery.getLotteryType())) {
            dto.setId(validLottery.getId());
            dto.setIsComplete(validLottery.getIsComplete());
            context.getResponseResultDto().setSurveyLottery(dto);
        }
    }

    /**
     * 返回一个有奖品的抽奖活动
     *
     * @param lotteries
     * @return
     */
    public SurveyLottery getOneLottery(List<SurveyLottery> lotteries) {
        for (SurveyLottery surveyLottery : lotteries) {
            Boolean existPrize = existPrize(surveyLottery);
            if (existPrize) {
                return surveyLottery;
            }
        }
        //所有启用中的活动都没有奖品，则返回最后一个启用中的抽奖活动
        return lotteries.get(lotteries.size() - 1);
    }

    /**
     * 判断抽奖活动奖品是否抽完
     *
     * @param lottery
     * @return
     */
    public Boolean existPrize(SurveyLottery lottery) {
        List<SurveyLotteryPrize> prizes = lottery.getPrizes();
        List<SurveyLotteryPrize> res;
        if (lottery.getLotteryType() == LotteryType.WECHAT) {
            SurveyLotteryPrize prize = prizes.get(0);
            return prize.getNumber() > prize.getWinnerNum();
        } else if (lottery.getLotteryType() == LotteryType.NINE_PRIZE) {
            res = prizes.stream().filter(x -> x.getIsPrize()).filter(x -> x.getNumber() > x.getWinnerNum()).collect(Collectors.toList());
            return res.size() > 0;
        } else {
            throw new BadRequestException("活动类型不支持");
        }
    }

}
