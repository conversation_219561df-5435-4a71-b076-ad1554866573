package cn.hanyi.survey.client.cache.bean;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

@Getter
@Setter
public class CacheDepartment {

    private Long id;
    private Long orgId;
    private String name;
    private String code;


    public String hkId() {
        return id.toString();
    }

    public String hkCode() {
        return StringUtils.isEmpty(code) ? null : ("code-" + code);
    }
}
