package cn.hanyi.survey.client.service.submit.completed;

import cn.hanyi.survey.client.dto.ClientSurvey;
import cn.hanyi.survey.client.service.submit.ICompletedProcessor;
import cn.hanyi.survey.client.service.submit.SubmitContext;
import cn.hanyi.survey.client.utils.EncryptDecryptHelper;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Map;

@Order(300)
@Component
public class CompletedProcessorEncrypt implements ICompletedProcessor {

    @Autowired
    private EncryptDecryptHelper encryptDecryptHelper;

    @Override
    public Boolean skip(SubmitContext context) {
        return context.getException() != null;
    }

    @Override
    public void onCompleted(SubmitContext context) {
        ClientSurvey survey;
        if (context != null && context.isEncrypted() && (survey = context.getSurvey()) != null && !context.isSubmit()) {
            Map<String, Object> data = survey.getCellData();
            if (data != null && !data.isEmpty()) {
                String originData = JsonHelper.toJson(data);
                String encryptedData = encryptDecryptHelper.encrypt(context.getData().getEncryptType(), context.getData().getBase64Key(), originData);
                survey.setEncryptedCellData(encryptedData);
                data.clear();
            }
        }
    }

}
