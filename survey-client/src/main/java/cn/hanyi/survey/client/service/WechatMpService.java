package cn.hanyi.survey.client.service;

import cn.hanyi.survey.client.dto.WechatAuthorizeDto;
import cn.hanyi.survey.core.constant.WechatProvider;
import cn.hanyi.survey.core.constant.error.SurveyErrorCode;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyChannel;
import cn.hanyi.survey.core.exception.CommonException;
import cn.hanyi.survey.core.exception.SurveyErrorException;
import cn.hanyi.survey.core.repository.SurveyChannelRepository;
import cn.hanyi.survey.core.repository.SurveyRepository;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.exception.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.util.Optional;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/9/29 10:25
 */
@Slf4j
@Service
@ConditionalOnProperty(value = "survey.client.enabled", havingValue = "true")
public class WechatMpService {

    @Autowired
    private WechatConfigureService wechatConfigureService;
    @Autowired
    private SurveyChannelRepository channelRepository;
    @Autowired
    private SurveyRepository surveyRepository;

    /**
     * 获取微信授权跳转地址
     *
     * @param surveyId
     * @param channelId
     * @return
     */
    public ResourceResponseDto<WechatAuthorizeDto> getWechatCode(Long surveyId, Long channelId, WechatAuthorizeDto params) {
        //获取问卷信息
        Survey survey = requireSurvey(surveyId);
        //获取渠道信息
        SurveyChannel surveyChannel = requireChannel(channelId);
        //校验
        if (!surveyChannel.getSid().equals(surveyId)) throw new SurveyErrorException(SurveyErrorCode.CHANNEL_TYPE_COMPLETE);

        //用户授权方式 scope
        String scope = "snsapi_userinfo";

        //使用体验家服务号 还是代公众号方式
        String redirectUrl = "";
        if (surveyChannel.getWechatProvider() == WechatProvider.BAND_WECHAT) {
            redirectUrl = wechatConfigureService.buildOpenQrConnectUrl(wechatConfigureService.getAppId(survey.getOrgId()), params.getUrl(), scope, params.getState());
        } else {
            redirectUrl = wechatConfigureService.buildQrConnectUrl(params.getUrl(), scope, params.getState());
        }

        WechatAuthorizeDto wechatAuthorizeDto = new WechatAuthorizeDto();
        wechatAuthorizeDto.setUrl(redirectUrl);
        return new ResourceResponseDto(wechatAuthorizeDto);
    }

    /**
     * 通过code 获取openId
     *
     * @param surveyId
     * @param channelId
     * @param params
     * @return
     */
    public ResourceResponseDto<WechatAuthorizeDto> getWechatOpenId(Long surveyId, Long channelId, WechatAuthorizeDto params) {
        //获取问卷信息
        Survey survey = requireSurvey(surveyId);
        //获取渠道信息
        SurveyChannel surveyChannel = requireChannel(channelId);
        //校验
        if (!surveyChannel.getSid().equals(surveyId)) throw new SurveyErrorException(SurveyErrorCode.CHANNEL_TYPE_COMPLETE);

        //使用体验家服务号 还是代公众号方式
        String openId = "";
        if (surveyChannel.getWechatProvider() == WechatProvider.BAND_WECHAT) {
            openId = wechatConfigureService.getOpenAccessToken(wechatConfigureService.getAppId(survey.getOrgId()), params.getCode());
        } else {
            openId = wechatConfigureService.getAccessToken(params.getCode());
        }


        WechatAuthorizeDto wechatAuthorizeDto = new WechatAuthorizeDto();
        wechatAuthorizeDto.setOpenId(openId);
        return new ResourceResponseDto(wechatAuthorizeDto);
    }

    /**
     * 获取渠道信息
     *
     * @param channelId
     * @return
     */
    public SurveyChannel requireChannel(@NotNull Long channelId) {
        Optional<SurveyChannel> surveyChannelOptional = channelRepository.findById(channelId);
        if (surveyChannelOptional.isEmpty()) {
            throw new EntityNotFoundException();
        }
        return surveyChannelOptional.get();
    }

    /**
     * 获取问卷-不存在报错
     *
     * @param surveyId
     * @return
     */
    public Survey requireSurvey(@NotNull Long surveyId) {
        Optional<Survey> surveyOptional = surveyRepository.findById(surveyId);
        Survey survey = surveyOptional.orElse(null);

        if (survey == null) {
            throw new SurveyErrorException(SurveyErrorCode.SURVEY_DELETE);
        }

        return survey;
    }
}
