package cn.hanyi.survey.client.dto;

import cn.hanyi.survey.core.dto.SubmitAdditionDataDto;
import cn.hanyi.survey.core.entity.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class ClientSurvey extends BaseSurvey {

    private Long groupId;
    private Long editorId;
    private String surveyCode;
    private Boolean enableDateLimit = false;
    private Date startTime;
    private Date endTime;
    private List<ClientQuestion> questions;
    private List<ClientQuota> quotas;
    private List<SurveyTag> tags = new ArrayList<>();
    private List<SurveyQuestionRandom> randoms = new ArrayList<>();
    private List<SurveyLogic> logics = new ArrayList<>();
    private List<SurveyLanguage> languages = new ArrayList<>();
    private List<SurveyPersonalizedRemark> personalizedRemarks = new ArrayList<>();
    private Map<String, Object> cellData;
    private SubmitAdditionDataDto additionData;
    @Schema(description = "已加密的问卷提交数据")
    private String encryptedCellData;
    private ClientChannel channel;
    private Long responseId;
    private Boolean doNotDisturb = false;
    private Boolean hasSmsBalance = false;
    private String orgVersion = "FREE";
    private Integer responseFinishNum = 0;

}
