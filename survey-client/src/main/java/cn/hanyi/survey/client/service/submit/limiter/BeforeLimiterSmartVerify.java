package cn.hanyi.survey.client.service.submit.limiter;

import cn.hanyi.survey.client.dto.ClientSurvey;
import cn.hanyi.survey.client.service.submit.IBeforeLimiter;
import cn.hanyi.survey.client.service.submit.ISubmitLoader;
import cn.hanyi.survey.client.service.submit.SubmitContext;
import cn.hanyi.survey.core.constant.error.SurveyErrorCode;
import cn.hanyi.survey.core.exception.SurveyErrorException;
import org.befun.extension.dto.SmartVerifyRequestDto;
import org.befun.extension.service.SmartVerifyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

@Order(1000)
@Component
@ConditionalOnProperty(value = "survey.client.enabled", havingValue = "true")
public class BeforeLimiterSmartVerify implements IBeforeLimiter {

    @Autowired
    private HttpServletRequest request;
    @Autowired
    private ISubmitLoader loader;
    @Autowired
    private SmartVerifyService smartVerifyService;

    @Override
    public void check(SubmitContext context) {
        ClientSurvey survey = loader.requireSurvey(context);
        //问卷如果开启了智能验证 智能验证一下
        if (unboxBool(survey.getEnableSmartVerify())
                && unboxBool(context.getData().getIsCompleted())
                && !unboxBool(context.getData().getIsEarlyCompleted())) {
            if (!checkSmartVerify(context.getData().getSmartVerify())) {
                throw new SurveyErrorException(SurveyErrorCode.SMART_VERIFY_FAIL);
            }
        }
    }

    public boolean checkSmartVerify(SmartVerifyRequestDto smartVerify) {
        return smartVerify != null && smartVerifyService.isVerify(smartVerify, request);
    }
}
