package cn.hanyi.survey.client.service.submit.completed;

import cn.hanyi.survey.client.service.submit.ICompletedProcessor;
import cn.hanyi.survey.client.service.submit.SubmitContext;
import cn.hanyi.survey.core.constant.ResponseStatus;
import cn.hanyi.survey.core.constant.channel.ChannelType;
import cn.hanyi.survey.core.constant.disturb.DisturbMethod;
import cn.hanyi.survey.core.entity.SurveyDisturbRecord;
import cn.hanyi.survey.core.repository.SurveyDisturbRecordRepository;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.List;

@Order(100)
@Component
@ConditionalOnProperty(value = "survey.client.enabled", havingValue = "true")
public class CompletedProcessorDisturbRecord implements ICompletedProcessor {

    @Autowired
    private SurveyDisturbRecordRepository surveyDisturbRecordRepository;

    @Override
    public Boolean skip(SubmitContext context) {
        return context.getException() != null && context.getResponse() != null;
    }

    @Override
    public void onCompleted(SubmitContext context) {
        if (context.getChannel() == null || !List.of(ChannelType.INJECT_WEB, ChannelType.APP, ChannelType.MP).contains(context.getChannel().getType())
                || context.getResponse() == null || StringUtils.isEmpty(context.getResponse().getExternalUserId()))
            return;
        if (context.isSubmit()) {
            if (context.getResponse().getStatus() == ResponseStatus.INIT) return;
            surveyDisturbRecordRepository.save(new SurveyDisturbRecord(context.getOrgId(), context.getSurveyId(), context.getResponse().getExternalUserId(),
                    DisturbMethod.SUBMIT, context.getResponse().getChannelId(), context.getResponse().getClientId()));
        }
//        else if (!context.isSubmit() && context.getChannel().getType() == ChannelType.INJECT_WEB) {
//            surveyDisturbRecordRepository.save(new SurveyDisturbRecord(context.getOrgId(), context.getSurveyId(), context.getResponse().getExternalUserId(),
//                    DisturbMethod.OPEN, context.getResponse().getChannelId(), context.getResponse().getClientId()));
//        }
    }

}
