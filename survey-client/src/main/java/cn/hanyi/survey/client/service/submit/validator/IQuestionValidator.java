package cn.hanyi.survey.client.service.submit.validator;

import cn.hanyi.survey.client.dto.ClientQuestion;
import cn.hanyi.survey.core.constant.question.QuestionType;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;

public interface IQuestionValidator {

    QuestionType type();

    List<String> validate(ClientQuestion question, Object value, Integer score, String comment, Object tags);

    default List<String> validate(Consumer<List<String>> addMessages) {
        List<String> messages = new ArrayList<>();
        addMessages.accept(messages);
        return messages;
    }

    @Getter
    @Setter
    class ErrorInfo {
        private ClientQuestion question;
        private List<String> message;

        public ErrorInfo(ClientQuestion question, List<String> message) {
            this.question = question;
            this.message = message;
        }

        public String print() {
            return (question == null ? "" : question.getCode()) + " " + (message == null ? "" : String.join(",", message));
        }
    }
}
