package cn.hanyi.survey.client.cache;

import cn.hanyi.survey.client.dto.ClientQuestion;
import cn.hanyi.survey.client.service.submit.ISubmitStorage;
import cn.hanyi.survey.core.entity.SurveyResponse;
import cn.hanyi.survey.core.entity.SurveyResponseCell;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.befun.extension.nativesql.SqlBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ResponseCacheHelper extends CacheHelper {
    private static final String K = "cache:survey-response:%d:%s";          // surveyId clientId
    private static final String HK_ID = "id";
    private static final String HK_CELL_IDS = "cellIds";

    @Autowired
    private ISubmitStorage storage;

    private String key(Long surveyId, String clientId) {
        return String.format(K, surveyId, clientId);
    }

    @Getter
    @Setter
    public static class ResponseClientId {
        private Long id;
        private String clientId;
    }

    private void foreachResponses(Long surveyId, Consumer<List<ResponseClientId>> consumer) {
        SqlBuilder sqlBuilder = SqlBuilder.select("select id, client_id clientId from survey_response")
                .where("s_id=%d", surveyId).alwaysTrue();
        boolean hasNext = true;
        int page = 1;
        while (hasNext) {
            sqlBuilder.limit(page, 200);
            List<ResponseClientId> list = nativeSqlHelper.queryListObject(sqlBuilder.buildSelectSql(), ResponseClientId.class);
            if (CollectionUtils.isNotEmpty(list)) {
                consumer.accept(list);
            } else {
                hasNext = false;
            }
            page++;
        }
    }

    public void removeAllSurveyResponse(Long surveyId) {
        foreachResponses(surveyId, list -> {
            List<String> keys = list.stream().filter(i -> StringUtils.isNotEmpty(i.clientId)).map(i -> key(surveyId, i.clientId)).collect(Collectors.toList());
            if (!keys.isEmpty()) {
                delete(keys);
                log.info("删除问卷{}的所有答卷缓存:{}", surveyId, String.join(", ", keys));
            }
        });
    }

    public void removeResponse(Long surveyId, String clientId) {
        if (StringUtils.isNotEmpty(clientId)) {
            String key = key(surveyId, clientId);
            delete(key);
            log.info("删除问卷{}的答卷{}缓存:{}", surveyId, clientId, key);
        }
    }

    public void setCache(Long surveyId, String clientId, SurveyResponse response, List<SurveyResponseCell> cells) {
        String k = key(surveyId, clientId);
        String cellIds = cells.stream().map(i -> i.getId().toString()).collect(Collectors.joining(","));
        if (StringUtils.isEmpty(cellIds)) {
            cellIds = NOT_EXIST_FLAG; // 还没有答题数据，标记不存在
        }
        hashOpt().putAll(k, Map.of(HK_ID, response.getId().toString(), HK_CELL_IDS, cellIds));
        expireKey(k);
    }

    public SurveyResponse getResponse(Long surveyId, String clientId) {
        String k = key(surveyId, clientId);
        if (enableCache) {
            String cacheId = hashOpt().get(k, HK_ID);
            if (NumberUtils.isDigits(cacheId)) {
                return storage.getResponse(Long.parseLong(cacheId));
            }
        } else {
            // 如果缓存关闭了，则清除历史缓存，防止下次重新启用缓存时，获取到了旧数据
            delete(k);
        }
        return storage.getResponse(surveyId, clientId);
    }

    public List<SurveyResponseCell> getCells(Long surveyId, String clientId, Long responseId, Map<Long, ClientQuestion> idMap) {
        // 调用 getCells 方法之前一定会调用 getResponse, 上面的方法在没有启用缓存的时候已经清除了缓存，所以这里就不用再次清除
        if (enableCache) {
            String k = key(surveyId, clientId);
            String cacheCellIds = hashOpt().get(k, HK_CELL_IDS);
            if (cacheCellIds != null) {
                if (NOT_EXIST_FLAG.equals(cacheCellIds)) { // 如果标记了不存在答题数据，直接返回空列表
                    return new ArrayList<>();
                } else {
                    Set<Long> cellIds = Arrays.stream(cacheCellIds.split(",")).filter(NumberUtils::isDigits).map(Long::parseLong).collect(Collectors.toSet());
                    return storage.getCells(cellIds, idMap);
                }
            }
        }
        return storage.getCells(surveyId, responseId, idMap);
    }
}
