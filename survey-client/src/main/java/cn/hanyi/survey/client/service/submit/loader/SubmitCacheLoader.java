package cn.hanyi.survey.client.service.submit.loader;

import cn.hanyi.survey.client.cache.ResponseCacheHelper;
import cn.hanyi.survey.client.cache.SurveyCacheHelper;
import cn.hanyi.survey.client.service.submit.ISubmitLoader;
import cn.hanyi.survey.client.service.submit.ISubmitStorage;
import cn.hanyi.survey.client.service.submit.SubmitContext;
import cn.hanyi.survey.core.constant.channel.SurveyCollectorMethod;
import cn.hanyi.survey.core.dto.task.AdminXResponseDto;
import cn.hanyi.survey.core.entity.SurveyResponse;
import cn.hanyi.survey.core.entity.SurveyResponseCell;
import org.befun.task.mq.ITaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import java.util.List;

@Primary
@Component
@ConditionalOnProperty(value = "survey.cache.enabled", havingValue = "true")
public class SubmitCacheLoader extends SubmitDbLoader implements ISubmitLoader {

    private static final String ADMINX_RESPONSE_VIEW_KEY = "adminx-response-view";

    @Autowired
    private SurveyCacheHelper surveyCacheHelper;
    @Autowired
    private ResponseCacheHelper responseCacheHelper;
    @Autowired
    private ISubmitStorage storage;
    @Autowired
    private ITaskService taskService;

//    @Override
//    public ClientSurvey requireSurvey(SubmitContext context) {
//        return context.requireProperty("survey", context::getSurvey, context::setSurvey,
//                () -> surveyCacheHelper.getSurvey(context.getSurveyId()));
//    }

    @Override
    public SurveyResponse requireResponse(SubmitContext context) {
        return context.getProperty("response", context::getResponse, context::setResponse,
                () -> {
                    Long orgId = requireOrgId(context);
                    Long surveyId = requireSurveyId(context);
                    SurveyResponse response = responseCacheHelper.getResponse(surveyId, context.getClientId());
                    if (response == null) {
                        if (context.getData().getResponseId() == null) {
                            response = storage.addResponse(buildResponse(context));
                            surveyEventTrigger.responseCreate(orgId, surveyId, response.getId());
                        } else {
                            response = storage.getResponse(context.getData().getResponseId());
                        }
                    }
                    surveyEventTrigger.responseView(orgId, surveyId, response.getId());
                    if (context.getData().getCollectorMethod() == SurveyCollectorMethod.SURVEY_PLUS && !context.isSubmit()) {
                        Long channelId = context.getData().getChannelId();
                        taskService.addTask(ADMINX_RESPONSE_VIEW_KEY, new AdminXResponseDto(context.getSurveyId(), response.getId(), channelId, false));
                    }
                    return response;
                });
    }

    @Override
    public List<SurveyResponseCell> requireOldCells(SubmitContext context) {
        return context.requireProperty("oldCells", context::getOldCells, context::setOldCells,
                () -> {
                    SurveyResponse response = requireResponse(context);
                    return responseCacheHelper.getCells(context.getSurveyId(), context.getClientId(), response.getId(), requireQuestionIdMap(context));
                });
    }
}
