package cn.hanyi.survey.client.service.submit.limiter;

import cn.hanyi.survey.client.dto.ClientQuota;
import cn.hanyi.survey.client.dto.ClientSurvey;
import cn.hanyi.survey.client.service.QuestionResponseHelper;
import cn.hanyi.survey.client.service.submit.IAfterLimiter;
import cn.hanyi.survey.client.service.submit.ISubmitLoader;
import cn.hanyi.survey.client.service.submit.SubmitContext;
import cn.hanyi.survey.core.constant.QuotaChannelType;
import cn.hanyi.survey.core.constant.channel.SurveyCollectorMethod;
import cn.hanyi.survey.core.service.expression.ExpressionService;
import cn.hanyi.survey.service.quota.QuotaLimit;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Order(1010)
@Component
@ConditionalOnProperty(value = "survey.client.enabled", havingValue = "true")
public class AfterLimiterQuota implements IAfterLimiter {

    @Autowired
    private ISubmitLoader loader;
    @Autowired
    private QuestionResponseHelper questionResponseHelper;
    @Autowired
    private ExpressionService expressionService;
    @Autowired
    private QuotaLimit quotaLimit;

    @Override
    public void check(SubmitContext context) {
        ClientSurvey survey = loader.requireSurvey(context);
        if (!context.isSubmit()) {
            return;
        }
        List<ClientQuota> quotas = loader.getQuotas(context);
        // 如果开启了配额，但是配额列表为空，怎么办？ todo
        if (CollectionUtils.isEmpty(quotas)) {
            return; // 这里直接跳过
        }
        List<Long> matchQuotaIds = new ArrayList<>();
        if (unboxBool(survey.getEnableQuota()) && SurveyCollectorMethod.SURVEY_PLUS != context.getData().getCollectorMethod()) {
            List<ClientQuota> filteredQuotas = quotas.stream().filter(x -> QuotaChannelType.SURVEY_PLUS != x.getChannelType()).collect(Collectors.toList());
            matchQuotaIds = computeMatchQuotaIds(context, filteredQuotas);
        } else if (unboxBool(survey.getEnableAdminxQuota()) && SurveyCollectorMethod.SURVEY_PLUS == context.getData().getCollectorMethod()) {
            //社区渠道
            List<ClientQuota> filteredQuotas = quotas.stream().filter(x -> QuotaChannelType.SURVEY_PLUS == x.getChannelType()).collect(Collectors.toList());
            matchQuotaIds = computeMatchQuotaIds(context, filteredQuotas);
        }
        if (!matchQuotaIds.isEmpty()) {
            checkQuota(context, survey, matchQuotaIds);
        }
    }

    /*  public void check2(SubmitContext context) {
		  ClientSurvey survey = loader.requireSurvey(context);
		  if (context.isSubmit() && unboxBool(survey.getEnableQuota())) {
			  List<ClientQuota> quotas = loader.getQuotas(context);
			  // 如果开启了配额，但是配额列表为空，怎么办？ todo
			  if (CollectionUtils.isEmpty(quotas)) {
				  return; // 这里直接跳过
			  }
			  Predicate<ClientQuota> filterPredicate = SurveyCollectorMethod.SURVEY_PLUS == context.getData().getCollectorMethod() ?
					  x -> QuotaChannelType.SURVEY_PLUS == x.getChannelType() :
					  x -> QuotaChannelType.SURVEY_PLUS != x.getChannelType();
			  List<ClientQuota> filteredQuotas = quotas.stream().filter(filterPredicate).collect(Collectors.toList());

			  List<Long> matchQuotaIds = computeMatchQuotaIds(context, filteredQuotas);
			  if (!matchQuotaIds.isEmpty()) {
				  checkQuota(context, survey, matchQuotaIds);
			  }
		  }
	  }
  */
    private List<Long> computeMatchQuotaIds(SubmitContext context, List<ClientQuota> quotas) {
        List<Long> matchQuotaIds = new ArrayList<>();
        Map<String, Object> content = questionResponseHelper.getExpressionData(loader.requireNewCells(context));
        if (!content.isEmpty()) {
            quotas.forEach(i -> {
                if (expressionService.triggerExpression(i.getExpression(), content)) {
                    matchQuotaIds.add(i.getId());
                }
            });
        }
        return matchQuotaIds;
    }

    private void checkQuota(SubmitContext context, ClientSurvey survey, List<Long> matchQuotaIds) {
        boolean hasQuota = quotaLimit.hasQuota(survey.getId(), matchQuotaIds);
        if (hasQuota) {  // todo 语义错误，等以后修改过来
            context.getData().setIsCompleted(true);
//            throw new SurveyErrorException(SurveyErrorCode.QUOTA_OVER_LIMIT);
        }
        context.setMatchQuotaIds(matchQuotaIds);
    }
}
