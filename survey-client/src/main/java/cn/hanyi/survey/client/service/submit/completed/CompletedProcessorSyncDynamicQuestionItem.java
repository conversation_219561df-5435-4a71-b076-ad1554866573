package cn.hanyi.survey.client.service.submit.completed;

import cn.hanyi.survey.client.service.submit.ICompletedProcessor;
import cn.hanyi.survey.client.service.submit.ISubmitLoader;
import cn.hanyi.survey.client.service.submit.SubmitContext;
import cn.hanyi.survey.core.dto.question.DynamicQuestionDto;
import cn.hanyi.survey.core.dto.question.DynamicSyncQuestionDto;
import cn.hanyi.survey.core.entity.SurveyQuestionItem;
import cn.hanyi.survey.workertrigger.ISurveyEventTrigger;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Order(400)
@Component
public class CompletedProcessorSyncDynamicQuestionItem implements ICompletedProcessor {

    @Autowired
    private ISubmitLoader loader;
    @Autowired
    private ISurveyEventTrigger surveyEventTrigger;

    @Override
    public Boolean skip(SubmitContext context) {
        return context.getException() != null;
    }

    @Override
    public void onCompleted(SubmitContext context) {
        if (!context.isSubmit()) {
            syncDynamicQuestionItems(context);
        }
    }

    private void syncDynamicQuestionItems(SubmitContext context) {
        Map<String, DynamicQuestionDto> dynamicQuestionDtoMap = context.getData().convertToDynamicQuestionMap();
        // 待同步的动态选项
        List<DynamicSyncQuestionDto> syncDynamicQuestions = new ArrayList<>();
        loader.requireQuestions(context).forEach(question -> {
            if (question.getIsDynamicItem() != null && question.getIsDynamicItem()) {
                DynamicQuestionDto dynamicQuestion = dynamicQuestionDtoMap.get(question.getCode());
                if (dynamicQuestion != null) {
                    List<SurveyQuestionItem> mockItems = dynamicQuestion.convertToItems();
                    if (!mockItems.isEmpty()) {
                        question.setItems(mockItems);
                        syncDynamicQuestions.add(new DynamicSyncQuestionDto(question.getId(), dynamicQuestion.getItems()));
                    }
                }
            }
        });
        if (!syncDynamicQuestions.isEmpty()) {
            surveyEventTrigger.responseSyncDynamicItem(loader.requireOrgId(context),
                    loader.requireSurveyId(context),
                    loader.requireResponse(context).getId(),
                    JsonHelper.toJson(syncDynamicQuestions)
            );
        }
    }

}
