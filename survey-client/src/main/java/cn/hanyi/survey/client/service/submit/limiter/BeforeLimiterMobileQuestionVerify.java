package cn.hanyi.survey.client.service.submit.limiter;

import cn.hanyi.survey.client.cache.LimitMobileCacheHelper;
import cn.hanyi.survey.client.dto.ClientQuestion;
import cn.hanyi.survey.client.service.submit.IBeforeLimiter;
import cn.hanyi.survey.client.service.submit.ISubmitLoader;
import cn.hanyi.survey.client.service.submit.SubmitContext;
import cn.hanyi.survey.core.constant.ResponseStatus;
import cn.hanyi.survey.core.constant.error.SurveyErrorCode;
import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.constant.survey.SurveyStatus;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyResponse;
import cn.hanyi.survey.core.exception.SurveyErrorException;
import cn.hanyi.survey.core.projection.SimpleQuestion2;
import cn.hanyi.survey.core.projection.SimpleResponse2;
import cn.hanyi.survey.core.projection.SimpleSurvey2;
import cn.hanyi.survey.core.repository.SurveyQuestionRepository;
import cn.hanyi.survey.core.repository.SurveyRepository;
import cn.hanyi.survey.core.repository.SurveyResponseRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.befun.auth.configuration.AuthProperties;
import org.befun.auth.constant.VerifyCodeUseFor;
import org.befun.auth.dto.SendVerifyCodeRequestDto;
import org.befun.auth.dto.VerifyCodeStatusRequestDto;
import org.befun.auth.provider.local.MobileVerifyCodeProvider;
import org.befun.auth.service.AuthService;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.property.SmsTemplateProperty;
import org.befun.extension.service.SmsService;
import org.befun.extension.sms.ISmsAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Consumer;

@Slf4j
@Order(1050)
@Component
@ConditionalOnProperty(value = "survey.client.enabled", havingValue = "true")
public class BeforeLimiterMobileQuestionVerify implements IBeforeLimiter {

    @Autowired
    private ISubmitLoader loader;
    @Autowired
    private AuthService authService;
    @Autowired
    private AuthProperties authProperties;
    @Autowired
    private ISmsAccountService smsAccountService;
    @Autowired
    private SmsService smsService;
    @Autowired
    private SurveyResponseRepository surveyResponseRepository;
    @Autowired
    private LimitMobileCacheHelper limitMobileCacheHelper;
    @Autowired
    private SurveyRepository surveyRepository;
    @Autowired
    private SurveyQuestionRepository surveyQuestionRepository;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private MobileVerifyCodeProvider mobileVerifyCodeProvider;

    @Override
    public void check(SubmitContext context) {
        List<ClientQuestion> questions = loader.requireQuestions(context);
        Map<String, Object> cellData = context.getData().getData();
        // 开启了去重的手机题
        if (questions.stream().filter(q -> q.getType() == QuestionType.MOBILE && q.getIsDeduplication() != null && q.getIsDeduplication()).anyMatch(q -> {
            Object value = cellData.get(q.getName());
            if (value != null) {
                // 有这个手机题的答题数据
                String mobile = value.toString();
                Long responseId = limitMobileCacheHelper.getByMobile(context.getSurveyId(), q.getId(), mobile);
                if (responseId != null) {
                    SurveyResponse response = loader.requireResponse(context);
                    // 这个手机号有答卷id，并且不是自己，返回 true 校验不通过
                    return !response.getId().equals(responseId);
                }
            }
            return false;
        })) {
            throw new SurveyErrorException(SurveyErrorCode.QUESTION_MOBILE_EXIST_SUBMIT);
        }
    }

    public boolean checkMobileDeduplication(long surveyId, long questionId, String mobile) {
        // 校验问卷的问题是否支持发送验证码
        Pair<SimpleSurvey2, SimpleQuestion2> pair = checkSurveyMobileQuestionIsDeduplication(surveyId, questionId);
        // 如果如果开启了去重，手机号已经有答卷id，抛出手机号已存在异常
        return checkMobileDeduplication(surveyId, questionId, mobile, pair.getRight(), false);
    }

    private boolean checkMobileDeduplication(long surveyId, long questionId, String mobile, SimpleQuestion2 question, boolean throwable) {
        if (question.getIsDeduplication() != null && question.getIsDeduplication()) {
            if (limitMobileCacheHelper.getByMobile(surveyId, questionId, mobile) != null) {
                if (throwable) {
                    throw new SurveyErrorException(SurveyErrorCode.QUESTION_MOBILE_EXIST_SUBMIT);
                } else {
                    return false;
                }
            }
        }
        return true;
    }

    public boolean sendVerifyCode(long surveyId, long questionId, long responseId, String mobile) {
        // 校验问卷的问题是否支持发送验证码
        Pair<SimpleSurvey2, SimpleQuestion2> pair = checkSurveyMobileQuestionIsVerifyMobile(surveyId, questionId, responseId);
        SimpleSurvey2 survey = pair.getLeft();
        SimpleQuestion2 question = pair.getRight();
        // 如果如果开启了去重，手机号已经有答卷id，抛出手机号已存在异常
        checkMobileDeduplication(surveyId, questionId, mobile, question, true);
        // 如果没有短信额度了，抛出无额度异常
        if (!smsAccountService.hasBalance(survey.getOrgId(), 1)) {
            throw new SurveyErrorException(SurveyErrorCode.QUESTION_MOBILE_NO_BALANCE);
        }
        String uniqueKey = getUniqueKey(surveyId, questionId, mobile);
        boolean success = false;
        try {
            String useFor = VerifyCodeUseFor.SURVEY_QUESTION_MOBILE.getValue();
            String templateName = authProperties.getVerifyCode().getSmsTemplateName().get(useFor);
            SmsTemplateProperty template = smsService.getTemplatePropertyMap().get(templateName);
            if (template != null) {
                String mockKey = String.format("verify_code_mock:%s:%s:%s-%s", useFor, "mobile", surveyId, questionId);
                if (Optional.ofNullable(stringRedisTemplate.hasKey(mockKey)).orElse(false)) {
                    success = mobileVerifyCodeProvider.mockSendVerifyCode(VerifyCodeUseFor.SURVEY_QUESTION_MOBILE, mobile, uniqueKey);
                } else {
                    success = authService.sendVerifyCode("mobile", "cem", useFor, new SendVerifyCodeRequestDto(mobile, uniqueKey));
                }
                if (success) {
                    int balance = smsAccountService.consumer(survey.getOrgId(), 1);
                    Map<String, Object> source = new HashMap<>();
                    source.put("surveyId", surveyId);
                    source.put("surveyTitle", survey.getTitle());
                    source.put("questionId", questionId);
                    source.put("questionTitle", question.getTitle());
                    String templateContent = template.getRealSignature() + template.getContent();
                    smsAccountService.addSmsRecordBySource(survey.getOrgId(), null, JsonHelper.toJson(source), templateContent, 1, balance);
                }
            }
        } catch (Throwable e) {
            log.warn("sendVerifyCode：{}", e.getMessage());
        }
        return success;
    }


    public boolean checkVerifyCode(long surveyId, long questionId, long responseId, String mobile, String code) {
        // 校验问卷的问题是否支持发送验证码
        checkSurveyMobileQuestionIsVerifyMobile(surveyId, questionId, responseId);
        String uniqueKey = getUniqueKey(surveyId, questionId, mobile);
        boolean success = false;
        try {
            success = authService.verifyCodeStatus("mobile", "cem", VerifyCodeUseFor.SURVEY_QUESTION_MOBILE.getValue(), new VerifyCodeStatusRequestDto(mobile, code, uniqueKey));
        } catch (Throwable e) {
            log.warn("checkVerifyCode：{}", e.getMessage());
        }
        return success;
    }

    /**
     * 短信唯一标识
     */
    private String getUniqueKey(long surveyId, long questionId, String mobile) {
        return String.format("%d-%d-%s", surveyId, questionId, mobile);
    }

    /**
     * 校验问卷的问题是否支持发送验证码
     */
    private Pair<SimpleSurvey2, SimpleQuestion2> checkSurveyMobileQuestionIsVerifyMobile(long surveyId, long questionId, long responseId) {
        return checkSurveyMobileQuestion(surveyId, questionId, question -> {
            SimpleResponse2 response;
            if (responseId <= 0 || (response = surveyResponseRepository.findSimple2ById(responseId)) == null || response.getSurveyId() != surveyId) {
                throw new SurveyErrorException(SurveyErrorCode.SURVEY_RESPONSE_DELETE);
            }
            if (response.getStatus() != ResponseStatus.INIT) {
                throw new SurveyErrorException(SurveyErrorCode.ALREADY_SUBMIT);
            }
            if (question.getIsVerifyMobile() == null || !question.getIsVerifyMobile()) {
                throw new SurveyErrorException(SurveyErrorCode.QUESTION_MOBILE_NOT_OPEN_VERIFY);
            }
        });
    }

    /**
     * 校验问卷的问题是否支持手机号去重
     */
    private Pair<SimpleSurvey2, SimpleQuestion2> checkSurveyMobileQuestionIsDeduplication(long surveyId, long questionId) {
        return checkSurveyMobileQuestion(surveyId, questionId, question -> {
            if (question.getIsDeduplication() == null || !question.getIsDeduplication()) {
                throw new SurveyErrorException(SurveyErrorCode.QUESTION_MOBILE_NOT_OPEN_DEDUPLICATION);
            }
        });
    }

    /**
     * 校验问卷和问题
     */
    private Pair<SimpleSurvey2, SimpleQuestion2> checkSurveyMobileQuestion(long surveyId, long questionId, Consumer<SimpleQuestion2> checkQuestion) {
        SimpleSurvey2 survey;
        if (surveyId <= 0 || (survey = surveyRepository.findSimple2ById(surveyId)) == null) {
            throw new SurveyErrorException(SurveyErrorCode.SURVEY_DELETE);
        } else if (survey.getStatus() == SurveyStatus.STOPPED) {
            throw new SurveyErrorException(SurveyErrorCode.CHANNEL_TYPE_PAUSE);
        } else if (survey.getStatus() != SurveyStatus.COLLECTING) {
            throw new SurveyErrorException(SurveyErrorCode.CHANNEL_TYPE_COMPLETE);
        }
        Survey s = new Survey();
        s.setId(surveyId);
        SimpleQuestion2 question;
        if (questionId <= 0 || (question = surveyQuestionRepository.findSimple2ByIdAndSurvey(questionId, s)) == null) {
            throw new SurveyErrorException(SurveyErrorCode.QUESTION_NOT_EXIST);
        } else if (question.getType() != QuestionType.MOBILE) {
            throw new SurveyErrorException(SurveyErrorCode.QUESTION_TYPE_ERROR);
        } else {
            checkQuestion.accept(question);
        }
        return Pair.of(survey, question);
    }
}
