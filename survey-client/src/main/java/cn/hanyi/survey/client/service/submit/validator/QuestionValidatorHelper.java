package cn.hanyi.survey.client.service.submit.validator;

import cn.hanyi.survey.client.dto.ClientQuestion;
import cn.hanyi.survey.core.constant.error.SurveyErrorCode;
import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.exception.SurveyErrorException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
@SuppressWarnings("MismatchedQueryAndUpdateOfCollection")
public class QuestionValidatorHelper implements ApplicationRunner {

    @Autowired
    private final List<IQuestionValidator> questionValidators = new ArrayList<>();
    private final Map<QuestionType, IQuestionValidator> questionValidatorMap = new HashMap<>();

    @Override
    public void run(ApplicationArguments args) {
        questionValidators.forEach(i -> questionValidatorMap.put(i.type(), i));
    }

    public IQuestionValidator.ErrorInfo validate(ClientQuestion question, Object value, Integer score, String comment, Object tags) {
        List<String> message = null;
        IQuestionValidator validator = questionValidatorMap.get(question.getType());
        if (validator != null) {
            message = validator.validate(question, value, score, comment, tags);
        }
        if (CollectionUtils.isEmpty(message)) {
            return null;
        }
        return new IQuestionValidator.ErrorInfo(question, message);
    }

    public void throwIfHasError(List<IQuestionValidator.ErrorInfo> errorMessages) {
        if (CollectionUtils.isNotEmpty(errorMessages)) {
            String codes = errorMessages.stream().map(i -> i.getQuestion().getCode()).collect(Collectors.joining(","));
            String messages = errorMessages.stream().map(IQuestionValidator.ErrorInfo::print).collect(Collectors.joining(","));
            log.warn("response validate error {}", messages);
            throw new SurveyErrorException(false, SurveyErrorCode.RESPONSE_VALIDATOR_ERROR, codes);
        }
    }
}
