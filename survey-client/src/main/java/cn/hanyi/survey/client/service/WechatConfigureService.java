package cn.hanyi.survey.client.service;


import cn.hanyi.common.file.storage.FileInfo;
import cn.hanyi.common.file.storage.FileStorageService;
import cn.hanyi.ctm.entity.Connector;
import cn.hanyi.ctm.service.ConnectorService;
import cn.hanyi.survey.core.constant.error.SurveyErrorCode;
import cn.hanyi.survey.core.exception.SurveyErrorException;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.open.api.WxOpenComponentService;
import org.befun.core.exception.BadRequestException;
import org.befun.extension.service.WeChatMpService;
import org.befun.extension.service.WeChatOpenService;
import org.befun.extension.service.WeChatMiniProgramService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.Optional;


/**
 * <AUTHOR>
 * @Description
 */
@Service
@Slf4j
@Getter
@Setter
@ConditionalOnProperty(value = "survey.client.enabled", havingValue = "true")
public class WechatConfigureService {

    @Autowired
    WeChatOpenService weChatOpenService;

    @Autowired
    private ConnectorService connectorService;

    @Autowired
    private WeChatMpService weChatMpService;

    @Autowired
    private WeChatMiniProgramService weChatMiniProgramService;

    @Autowired
    private FileStorageService fileStorageService;

    @Value("${befun.extension.wechat-miniprogram.version}")
    private String Version;

    /**
     * 生成微信授权跳转地址 公众号
     *
     * @param redirectUri
     * @param scope
     * @param state
     * @return
     */
    public String buildQrConnectUrl(String redirectUri, String scope, String state) {
        if (redirectUri.isEmpty() || scope.isEmpty())
            throw new BadRequestException("参数错误");
        return weChatMpService.getOAuth2Service().buildAuthorizationUrl(redirectUri, scope, state);
    }

    /**
     * 生成微信授权跳转地址 微信开放平台 代公众号
     *
     * @param redirectUri
     * @param scope
     * @param state
     * @return
     */
    public String buildOpenQrConnectUrl(String appId, String redirectUri, String scope, String state) {
        if (redirectUri.isEmpty() || scope.isEmpty())
            throw new BadRequestException("参数错误");
        try {
            return getWxOpenService().oauth2buildAuthorizationUrl(appId, redirectUri, scope, state);
        } catch (Exception e) {
            throw new BadRequestException(e.getMessage());
        }
    }

    /**
     * 通过code获取openid 公众号授权
     *
     * @param code
     * @return
     */
    public String getAccessToken(String code) {
        if (code.isEmpty())
            throw new BadRequestException("参数错误");
        try {
            WxOAuth2AccessToken accessToken = weChatMpService.getOAuth2Service().getAccessToken(code);
            return accessToken.getOpenId();
        } catch (Exception e) {
            throw new BadRequestException(e.getMessage());
        }
    }

    /**
     * 通过code获取openid  代公众号授权
     *
     * @param code
     * @return
     */
    public String getOpenAccessToken(String appId, String code) {
        if (code.isEmpty() || appId.isEmpty())
            throw new BadRequestException("参数错误");
        try {
            WxOAuth2AccessToken accessToken = getWxOpenService().oauth2getAccessToken(appId, code);
            return accessToken.getOpenId();
        } catch (Throwable e) {
            throw new BadRequestException(e.getMessage());
        }
    }

    /**
     * 通过orgId 获取微信appid
     *
     * @param orgId
     * @return
     */
    public String getAppId(Long orgId) {
        if (orgId != null && orgId != 0L) {
            Optional<Connector> connectorOptional = connectorService.getWechatConnectorByOrgId(orgId);
            if (connectorOptional.isPresent() && !connectorOptional.get().getAppId().isEmpty()) {
                return connectorOptional.get().getAppId();
            }
        }
        return null;
    }

    /**
     * 微信开放平台
     *
     * @return
     */
    public WxOpenComponentService getWxOpenService() {
        return weChatOpenService.getWxOpenComponentService();
    }

    /**
     * 获取小程序二维码(不限制)
     * @param scene
     * @return
     * @throws WxErrorException
     */
    public String getUnlimitedQRCode(String scene) {
        try {
            File file =  weChatMiniProgramService.getQrcodeService().createWxaCodeUnlimit(scene, "pages/index/index",
                    "/tmp/",
                    false, Version, 430, false, null, false);
            if(!file.exists()) throw new SurveyErrorException(SurveyErrorCode.CREATE_QRCODE_FAIL);
            //文件上传到oss
            FileInfo fileInfo = fileStorageService.of(file).upload();
            file.delete();
            return fileInfo.getUrl();
        } catch (Exception e) {
            e.printStackTrace();
            throw new BadRequestException(e.getMessage());
        }
    }
}
