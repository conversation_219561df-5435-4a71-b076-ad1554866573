package cn.hanyi.survey.client.service.submit.validator.question;

import cn.hanyi.survey.client.dto.ClientQuestion;
import cn.hanyi.survey.client.service.submit.validator.IQuestionValidator;
import cn.hanyi.survey.client.service.submit.validator.action.ValidatorMinMax;
import cn.hanyi.survey.core.constant.question.QuestionType;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Component
public class QuestionValidator7Number implements IQuestionValidator, ValidatorMinMax {
    @Override
    public QuestionType type() {
        return QuestionType.NUMBER;
    }

    @Override
    public List<String> validate(ClientQuestion question, Object value, Integer score, String comment, Object tags) {
        return validate(messages -> {
            if (question.getEnableValidator() != null && question.getEnableValidator()) {
                Optional.ofNullable(validateMinMax(question, value)).ifPresent(messages::add);
            }
        });
    }
}
