package cn.hanyi.survey.client.service.submit.completed;

import cn.hanyi.survey.client.cache.SurveyCacheHelper;
import cn.hanyi.survey.client.dto.ClientSurvey;
import cn.hanyi.survey.client.service.submit.ICompletedProcessor;
import cn.hanyi.survey.client.service.submit.ISubmitLoader;
import cn.hanyi.survey.client.service.submit.SubmitContext;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;

@Order(50)
@Component
public class CompletedProcessStart implements ICompletedProcessor {
    @Autowired
    private SurveyCacheHelper surveyCacheHelper;
    @Autowired
    private HttpServletResponse response;
    @Autowired
    private ISubmitLoader loader;

    @Override
    public void onCompleted(SubmitContext context) {
        ClientSurvey survey = context.getSurvey();
        var language = "zh-cn";

        if (survey != null) {
            language = survey.getLanguage();
            response.setHeader("survey-language-setting", JsonHelper.toJson(survey.getLanguageSetting()));
        }
        // 设置跨域响应头
        response.setHeader("Access-Control-Expose-Headers", "survey-language, survey-language-setting");
        response.setHeader("survey-language", language);
    }

    @Override
    public Boolean skip(SubmitContext context) {
        return false;
    }

}
