package cn.hanyi.survey.client.service.submit.completed;

import cn.hanyi.ctm.constant.SendManageRecordStatus;
import cn.hanyi.ctm.entity.SendManageRecord;
import cn.hanyi.ctm.repository.SendManageRecordRepository;
import cn.hanyi.ctm.repository.SendManageRepository;
import cn.hanyi.survey.client.cache.SurveyCacheHelper;
import cn.hanyi.survey.client.dto.ClientChannel;
import cn.hanyi.survey.client.service.submit.ICompletedProcessor;
import cn.hanyi.survey.client.service.submit.SubmitContext;
import cn.hanyi.survey.core.constant.ReplyStatus;
import cn.hanyi.survey.core.constant.survey.ReceiveStatus;
import cn.hanyi.survey.core.constant.survey.SendStatus;
import cn.hanyi.survey.core.entity.SurveyResponse;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;

@Order(50)
@Component
public class CompletedProcessSendToken implements ICompletedProcessor {
    @Autowired
    private SurveyCacheHelper surveyCacheHelper;

    @Autowired
    private SendManageRecordRepository sendManageRecordRepository;

    @Autowired
    private SendManageRepository sendManageRepository;

    @Override
    public Boolean skip(SubmitContext context) {
        return context.getException() != null;
    }

    @Override
    @Async
    public void onCompleted(SubmitContext context) {
        Optional.ofNullable(context.getData().getAdditionData())
                .flatMap(additionData -> Optional.ofNullable(additionData.getSendToken()))
                .ifPresent(sendToken -> {
                    if (StringUtils.isEmpty(sendToken)) {
                        return;
                    }

                    String clientId = context.getClientId();
                    sendManageRecordRepository.findAll(
                            (r, q, c) -> c.and(c.equal(r.get("clientId"), clientId), c.equal(r.get("sendUrl"), sendToken)),
                            PageRequest.of(0, 1, Sort.by(Sort.Direction.DESC, "id"))
                    ).stream().findAny().ifPresentOrElse(
                            sendManageRecord -> {
                                if (context.getResponse().getIsCompleted()) {
                                    sendManageRecord.setStatus(SendManageRecordStatus.COMPLETE);
                                    sendManageRecord.setReplyStatus(ReplyStatus.SUBMIT);
                                    sendManageRecordRepository.save(sendManageRecord);
                                }
                            },
                            () -> {
                                ClientChannel channel = context.getChannel();
                                SurveyResponse response = context.getResponse();
                                Long orgId = response.getOrgId();
                                SendManageRecord sendManageRecord = new SendManageRecord();
                                Optional.ofNullable(sendManageRepository.findFirstByOrgIdAndSendToken(orgId, sendToken)).ifPresent(sendManage -> {
                                    sendManageRecord.setSendManageId(sendManage.getId());
                                });
                                sendManageRecord.setOrgId(orgId);
                                sendManageRecord.setClientId(clientId);
                                sendManageRecord.setSendUrl(sendToken);
                                sendManageRecord.setSendStatus(SendStatus.SEND_SUCCESS);
                                sendManageRecord.setReceiveStatus(ReceiveStatus.SUCCESS);
                                sendManageRecord.setStatus(SendManageRecordStatus.UN_COMPLETE);
                                sendManageRecord.setReplyStatus(ReplyStatus.UN_SUBMIT);
                                sendManageRecord.setSurveyId(context.getSurveyId());
                                sendManageRecord.setCustomerId(response.getCustomerId());
                                sendManageRecord.setSourceId(channel.getId());
                                sendManageRecord.setResponseId(response.getId());
                                sendManageRecord.setSendChannelStatus(JsonHelper.toJson(Map.of(
                                        String.format("%s-%s", channel.getType().name(), channel.getId()),
                                        true
                                )));
                                sendManageRecordRepository.save(sendManageRecord);
                            });
                });
    }


}
