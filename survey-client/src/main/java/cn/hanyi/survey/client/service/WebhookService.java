package cn.hanyi.survey.client.service;

import cn.hanyi.ctm.constant.connector.ConnectorPushCondition;
import cn.hanyi.ctm.constant.connector.ConnectorPushType;
import cn.hanyi.ctm.entity.Connector;
import cn.hanyi.ctm.repository.PushRepository;
import cn.hanyi.ctm.service.ConnectorConsumerService;
import cn.hanyi.survey.core.dto.webhook.PushSurvey;
import cn.hanyi.survey.core.entity.Survey;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.UserService;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class WebhookService {

    @Autowired
    ConnectorConsumerService connectorConsumerService;

    @Autowired
    UserService userService;

    @Autowired
    PushRepository pushRepository;


    /**
     * 推送问卷状态
     * 启用、停用、删除
     */
    public void pushSurveyStatus(Long orgId, Survey survey, ConnectorPushCondition status) {
        try {
            log.info("push survey status: surveyId={}, status={}", survey.getId(), status);
            HashSet<Long> s = new HashSet<>();

            Optional.ofNullable(survey.getUserId()).ifPresent(s::add);
            Optional.ofNullable(survey.getEditorId()).ifPresent(s::add);
            List<SimpleUser> users = Optional.ofNullable(userService.getSimpleByIds(s)).orElse(List.of());

            Optional<SimpleUser> creator = users.stream().filter(u -> u.getId().equals(survey.getUserId())).findFirst();
            Optional<SimpleUser> editor = users.stream().filter(u -> u.getId().equals(survey.getEditorId())).findFirst();

            PushSurvey pushSurvey = PushSurvey.builder()
                    .surveyId(survey.getId())
                    .surveyTitle(survey.getTitle())
                    .surveyCode(survey.getSurveyCode())
                    .surveyStatus(status)
                    .createTime(survey.getCreateTime())
                    .modifyTime(survey.modifyTime)
                    .creator(creator.isEmpty() ? null : creator.get().getTruename())
                    .editor(editor.isEmpty() ? null : editor.get().getTruename())
                    .build();

            connectorConsumerService.webhookConnector(
                    Stream.of(survey.getId()).collect(Collectors.toSet()),
                    ConnectorPushType.SURVEY,
                    status
            ).forEach(connector -> webhookSend(orgId, connector, JsonHelper.toJson(pushSurvey)));

        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * 推送答卷
     * 下一页，提交
     */
    public void pushResponse(Long orgId, Long surveyId, ConnectorPushCondition status, String body) {
        connectorConsumerService.webhookConnector(
                new HashSet<>(Set.of(surveyId)),
                ConnectorPushType.RESPONSE,
                status
        ).forEach(connector -> webhookSend(orgId, connector, body));

    }


    /**
     * 使用task发布任务
     *
     * @param orgId
     * @param connector
     */
    private void webhookSend(Long orgId, Connector connector, String JsonData) {
//        WebhookBaseTaskDto taskDto = WebhookBaseTaskDto.builder()
//                .orgId(orgId)
//                .connectorId(connector.getId())
//                .url(connector.getGateway())
//                .method(connector.getHttpMethod())
//                .body(JsonData)
//                .build();
//
//        Push push = new Push();
//        push.setOrgId(orgId);
//        push.setName(connector.getName());
//        push.setConnector(connector);
//        push.setType(ConnectorType.WEBHOOK);
//        push.setAddress(taskDto.getUrl());
//        push.setContent(taskDto.getBody());
//        push.setStatus(PushStatus.FAILED);
//        pushRepository.save(push);
//
//        log.info("webhook send data pushId:{} orgId:{} connectorId:{}", push.getId(), taskDto.getOrgId(), taskDto.getConnectorId());
//        taskDto.setPushId(push.getId());
//        webhookSendExecutor.performAsync(taskDto);
    }


}
