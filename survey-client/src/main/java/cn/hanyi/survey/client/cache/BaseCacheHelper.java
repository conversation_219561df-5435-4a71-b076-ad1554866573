package cn.hanyi.survey.client.cache;

import cn.hanyi.survey.client.cache.bean.Param;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.time.Duration;
import java.util.concurrent.ExecutorService;

/**
 * 缓存时，会加锁
 */
@Slf4j
public abstract class BaseCacheHelper<P extends Param, V> extends CacheHelper {

    /**
     * 日志描述
     */
    protected final String logText;
    protected final boolean notExistValueThrowable;

    public BaseCacheHelper(String logText, boolean notExistValueThrowable) {
        this.logText = logText;
        this.notExistValueThrowable = notExistValueThrowable;
    }

    /**
     * 1 从缓存获取数据
     * 2 成功从缓存获取，重置过期时间
     * 3 开始缓存
     * 3.1 尝试获取同步锁
     * 3.2 查询数据库并缓存，设置过期时间，释放同步锁
     */
    protected V getOrSync(P param) {
        String logParam = param.log;
        String key = key(param);
        V cache;
        try {
            cache = getCacheValue(key, param);
        } catch (RuntimeException e) {
            if (notExistValueThrowable) {
                throw e;
            } else {
                return null;
            }
        }
        if (cache != null) {
            log.info("已获取缓存{} {}", logText, logParam);
            expireKey(key);
            return cache;
        }
        String lockKey = lockKey(key, param);
        Boolean lock = valueOpt().setIfAbsent(lockKey, SYNC_PROGRESS, Duration.ofSeconds(10));
        if (lock != null && lock) {
            return syncCache(param, logParam, key, lockKey);
        }
        log.info("未从缓存获取到{}，查询数据库 {}", logText, logParam);
        return getDbValue(param);
    }

    private V syncCache(P param, String logParam, String key, String lockKey) {
        V syncValue;
        try {
            log.info("已获取缓存{}锁，开始缓存{} {}", logText, logText, logParam);
            syncValue = syncValue(key, param);
            log.info("缓存{}成功 {}", logText, logParam);
        } finally {
            delete(lockKey);
            expireKey(key);
            log.info("{}缓存结束 {}", logText, logParam);
        }
        return syncValue;
    }

    protected abstract String key(P param);

    protected abstract String lockKey(String key, P param);

    /**
     * 查询缓存
     * 如果缓存中标记了不存在，则抛出异常
     */
    protected abstract V getCacheValue(String key, P param);

    /**
     * 查询数据库
     */
    protected abstract V getDbValue(P param);

    /**
     * 同步缓存
     */
    protected abstract V syncValue(String key, P param);


}
