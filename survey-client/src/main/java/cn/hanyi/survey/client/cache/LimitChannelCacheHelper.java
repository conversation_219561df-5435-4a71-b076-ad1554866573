package cn.hanyi.survey.client.cache;

import cn.hanyi.survey.client.dto.ClientChannel;
import cn.hanyi.survey.core.projection.SimpleResponse;
import cn.hanyi.survey.core.repository.SurveyResponseRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.utils.DateHelper;
import org.befun.extension.nativesql.SqlBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.redis.core.DefaultTypedTuple;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ForkJoinPool;
import java.util.function.Function;

@Slf4j
@Service
public class LimitChannelCacheHelper extends CacheHelper {
    private static final String K_IP = "cache:channel-limit:%d:%d:ip";             // surveyId channelId  zset value=ip, score=finishTime
    private static final String K_OPEN_ID = "cache:channel-limit:%d:%d:openid";    // surveyId channelId  zset value=openid, score=finishTime
    private static final String K_SYNC = "cache:channel-limit:%d:%d:sync";         // surveyId channelId
    private static final int SIZE_PER_PAGE = 200;

    @Autowired
    private SurveyResponseRepository surveyResponseRepository;
    private final ForkJoinPool executorService = ForkJoinPool.commonPool();

    public void add(ClientChannel channel, String ip, String openId, double score) {
        if (isEnableLimit(channel)) {
            Long surveyId = channel.getSid();
            Long channelId = channel.getId();
            String ipKey = ipKey(surveyId, channelId);
            String openIdKey = openIdKey(surveyId, channelId);
            if (StringUtils.isNotEmpty(ip)) {
                zSetOpt().add(ipKey(surveyId, channelId), ip, score);
            }
            if (StringUtils.isNotEmpty(openId)) {
                zSetOpt().add(openIdKey(surveyId, channelId), openId, score);
            }
            expireKey(ipKey);
            expireKey(openIdKey);
        }
    }

    public void removeAllSurveyResponse(Long surveyId) {
        SqlBuilder sqlBuilder = SqlBuilder.select("select id, 1 type from survey_channel")
                .where("s_id=%d", surveyId).alwaysTrue();
        nativeSqlHelper.queryList(sqlBuilder, Function.identity(), (ignore, ids) -> {
            List<String> keys = new ArrayList<>();
            ids.forEach(id -> {
                keys.add(ipKey(surveyId, id));
                keys.add(openIdKey(surveyId, id));
                keys.add(syncKey(surveyId, id));
            });
            if (!keys.isEmpty()) {
                delete(keys);
                log.info("删除问卷{}的所有渠道的所有答卷缓存:{}", surveyId, String.join(", ", keys));
            }
            return ids;
        });
    }

    public void removeAllChannelResponse(Long surveyId, Long channelId) {
        Optional.ofNullable(ipKey(surveyId, channelId)).ifPresent(key -> {
            delete(key);
            log.info("删除问卷{}的渠道{}的ip列表缓存:{}", surveyId, channelId, key);
        });
        Optional.ofNullable(openIdKey(surveyId, channelId)).ifPresent(key -> {
            delete(key);
            log.info("删除问卷{}的渠道{}的openid列表缓存:{}", surveyId, channelId, key);
        });
        Optional.ofNullable(syncKey(surveyId, channelId)).ifPresent(key -> {
            delete(key);
            log.info("删除问卷{}的渠道{}的同步状态缓存:{}", surveyId, channelId, key);
        });
    }

    public void removeResponse(Long surveyId, Long channelId, String ip, String openId) {
        if (channelId != null) {
            if (StringUtils.isNotEmpty(ip)) {
                Date lastFinishTime = lastFinishTimeByIpInDb(surveyId, channelId, ip);
                String key = ipKey(surveyId, channelId);
                removeCache(key, ip, lastFinishTime);
                if (lastFinishTime == null) {
                    log.info("删除问卷{}的渠道{}的ip{}缓存:{}", surveyId, channelId, ip, key);
                } else {
                    log.info("更新问卷{}的渠道{}的ip{}缓存:{}", surveyId, channelId, ip, key);
                }
            }
            if (StringUtils.isNotEmpty(openId)) {
                Date lastFinishTime = lastFinishTimeByOpenIdInDb(surveyId, channelId, openId);
                String key = openIdKey(surveyId, channelId);
                removeCache(key, openId, lastFinishTime);
                if (lastFinishTime == null) {
                    log.info("删除问卷{}的渠道{}的openid{}缓存:{}", surveyId, channelId, openId, key);
                } else {
                    log.info("更新问卷{}的渠道{}的openid{}缓存:{}", surveyId, channelId, openId, key);
                }
            }
        }
    }

    private void removeCache(String key, String value, Date lastFinishTime) {
        if (lastFinishTime == null) {
            // 已经没有这个value的数据了，直接删除这个value
            zSetOpt().remove(key, value);
        } else {
            // 还有这个value的数据，找到最近的一个提交时间，并更新
            zSetOpt().add(key, value, lastFinishTime.getTime());
        }
    }

    private boolean isEnableLimit(ClientChannel channel) {
        boolean ip = unboxBool(channel.getEnableIpLimit());
        boolean openId = unboxBool(channel.getEnableWechatReplyOnly());
        return ip || openId;
    }

    /**
     * 查询指定openId,最后一次填答指定 surveyId channelId 的时间
     */
    public LocalDateTime lastTimeByOpenId(Long surveyId, Long channelId, String openId) {
        if (!enableCache) {
            return DateHelper.toLocalDateTime(lastFinishTimeByOpenIdInDb(surveyId, channelId, openId));
        }
        if (isSyncCompleted(surveyId, channelId)) {
            Double score = zSetOpt().score(openIdKey(surveyId, channelId), openId);
            if (score != null) {
                return DateHelper.toLocalDateTime(new Date(score.longValue()));
            }
        } else {
            // 开始异步缓存
            async(surveyId, channelId);
            // 提交时间倒序，第一条记录
            return DateHelper.toLocalDateTime(lastFinishTimeByOpenIdInDb(surveyId, channelId, openId));
        }
        return null;
    }

    private Date lastFinishTimeByOpenIdInDb(Long surveyId, Long channelId, String openId) {
        List<SimpleResponse> list = surveyResponseRepository.findSimpleBySurveyIdAndChannelIdAndIsCompletedIsTrueAndOpenid(surveyId, channelId, openId, lastOne());
        if (CollectionUtils.isNotEmpty(list)) {
            return Optional.ofNullable(list.get(0)).map(SimpleResponse::getFinishTime).orElse(null);
        }
        return null;
    }

    /**
     * 查询指定ip,最后一次填答指定 surveyId channelId 的时间
     */
    public LocalDateTime lastTimeByIp(Long surveyId, Long channelId, String ip) {
        if (!enableCache) {
            return DateHelper.toLocalDateTime(lastFinishTimeByIpInDb(surveyId, channelId, ip));
        }
        if (isSyncCompleted(surveyId, channelId)) {
            Double score = zSetOpt().score(ipKey(surveyId, channelId), ip);
            if (score != null) {
                return DateHelper.toLocalDateTime(new Date(score.longValue()));
            }
        } else {
            // 开始异步缓存
            async(surveyId, channelId);
            // 提交时间倒序，第一条记录
            return DateHelper.toLocalDateTime(lastFinishTimeByIpInDb(surveyId, channelId, ip));
        }
        return null;
    }

    private Date lastFinishTimeByIpInDb(Long surveyId, Long channelId, String ip) {
        List<SimpleResponse> list = surveyResponseRepository.findSimpleBySurveyIdAndChannelIdAndIsCompletedIsTrueAndIp(surveyId, channelId, ip, lastOne());
        if (CollectionUtils.isNotEmpty(list)) {
            return Optional.ofNullable(list.get(0)).map(SimpleResponse::getFinishTime).orElse(null);
        }
        return null;
    }

    private boolean isSyncCompleted(Long surveyId, Long channelId) {
        String syncKey = syncKey(surveyId, channelId);
        String sync = valueOpt().get(syncKey);
        return sync != null && sync.equals(SYNC_COMPLETE);
    }

    private void async(Long surveyId, Long channelId) {
        executorService.execute(() -> sync(surveyId, channelId));
    }

    private void sync(Long surveyId, Long channelId) {
        String syncKey = syncKey(surveyId, channelId);
        Boolean sync = valueOpt().setIfAbsent(syncKey, SYNC_PROGRESS);
        if (sync != null && sync) {
            String ipKey = ipKey(surveyId, channelId);
            String openIdKey = openIdKey(surveyId, channelId);
            try {
                int page = 0;
                boolean hasNext = true;
                while (hasNext) {
                    List<SimpleResponse> list = surveyResponseRepository.findSimpleBySurveyIdAndChannelIdAndIsCompletedIsTrue(surveyId, channelId, PageRequest.of(page, SIZE_PER_PAGE));
                    if (CollectionUtils.isNotEmpty(list)) {
                        cacheItems(ipKey, openIdKey, list);
                        page++;
                    } else {
                        hasNext = false;
                    }
                }
            } finally {
                expireKey(ipKey);
                expireKey(openIdKey);
                syncCompleted(syncKey);
            }
        }
    }

    private void cacheItems(String ipKey, String openIdKey, List<SimpleResponse> list) {
        Set<ZSetOperations.TypedTuple<String>> ipSet = new HashSet<>();
        Set<ZSetOperations.TypedTuple<String>> openIdSet = new HashSet<>();
        list.forEach(i -> {
            if (StringUtils.isNotEmpty(i.getIp())) {
                double score = Optional.ofNullable(i.getFinishTime()).orElse(new Date()).getTime();
                ipSet.add(new DefaultTypedTuple<>(i.getIp(), score));
            }
            if (StringUtils.isNotEmpty(i.getOpenid())) {
                double score = Optional.ofNullable(i.getFinishTime()).orElse(new Date()).getTime();
                openIdSet.add(new DefaultTypedTuple<>(i.getOpenid(), score));
            }
        });
        if (!ipSet.isEmpty()) {
            zSetOpt().add(ipKey, ipSet);
        }
        if (!openIdSet.isEmpty()) {
            zSetOpt().add(openIdKey, openIdSet);
        }
    }

    private String syncKey(Long surveyId, Long channelId) {
        return String.format(K_SYNC, surveyId, channelId);
    }

    private String ipKey(Long surveyId, Long channelId) {
        return String.format(K_IP, surveyId, channelId);
    }

    private String openIdKey(Long surveyId, Long channelId) {
        return String.format(K_OPEN_ID, surveyId, channelId);
    }
}
