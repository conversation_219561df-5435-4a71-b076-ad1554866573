package cn.hanyi.survey.client.service.submit;

import cn.hanyi.survey.client.cache.bean.CacheApiKey;
import cn.hanyi.survey.client.cache.bean.CacheDepartment;
import cn.hanyi.survey.client.dto.*;
import cn.hanyi.survey.core.dto.SurveySubmitRequestDto;
import cn.hanyi.survey.core.entity.SurveyResponse;
import cn.hanyi.survey.core.entity.SurveyResponseCell;
import cn.hanyi.survey.core.entity.SurveySendRecord;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.exception.BadRequestException;

import java.util.*;
import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * 包含提交答卷时的所有数据
 * 不要直接使用 get 方法获取数据
 * 使用 {@link ISubmitLoader}获取数据
 */
@Getter
@Setter
public class SubmitContext {
    // 客户端提交的数据
    private final boolean submit;                           // notnull, false 获取问卷 true 提交答案
    private Long surveyId;                                  // notnull,
    private String clientId;                                // notnull,
    private SurveySubmitRequestDto data;                    // notnull，提交的数据
    private boolean encrypted;

    // 请求处理时加载的数据 不要格式化这里的代码
    private Long orgId;                                                 //** notnull, 问卷 {@link ISubmitLoader#requireOrgId(SubmitContext)} */
    private ClientSurvey survey;                                        //** notnull, 问卷 {@link ISubmitLoader#requireSurvey(SubmitContext)} */
    private List<ClientQuota> quotas;                                   //** nullable, 所有配额 {@link ISubmitLoader#getQuotas(SubmitContext)}  */
    private List<ClientQuestion> questions;                             //** notnull, 所有问题 {@link ISubmitLoader#requireQuestions(SubmitContext)}  */
    private Map<String, ClientQuestion> questionNameMap;                //** notnull, 问题name作为key的所有问题 {@link ISubmitLoader#requireQuestionNameMap(SubmitContext)}  */
    private Map<Long, ClientQuestion> questionIdMap;                    //** notnull, 问题id作为key的所有问题 {@link ISubmitLoader#requireQuestionIdMap(SubmitContext)}  */
    private SurveyResponse response;                                    //** notnull, 第一次时，自动创建，之后直接使用 {@link ISubmitLoader#requireResponse(SubmitContext)}  */
    private ClientChannel channel;                                      //** notnull, 如果有渠道id，则需要校验渠道 {@link ISubmitLoader#getChannel(SubmitContext)}  */
    private SurveySendRecord channelRecord;                             //** notnull, 如果有渠道id，则必须存在渠道发送记录 {@link ISubmitLoader# */
    private CacheApiKey apiKey;                                         //** nullable, 加密aesKey {@link ISubmitLoader# */
    private CacheDepartment department;                                 //** nullable, 部门 {@link ISubmitLoader# */
    private String ip;                                                  //** nullable, ip {@link ISubmitLoader#getIp(SubmitContext)}  */
    private List<Long> matchQuotaIds;                                   //** nullable, 匹配的配额id {@link AfterLimiterQuota#checkQuota(SubmitContext, ClientSurvey, List)}  */
    private Boolean successUseQuotas;                                   //** nullable, 是否成功使用配额 {@link SubmitDbLoader#useQuota(SubmitContext)} */
    private List<SurveyResponseCell> oldCells;                          //** notnull, 上次提交的回答 {@link ISubmitLoader#requireOldCells(SubmitContext)} */
    private List<SurveyResponseCell> newCells;                          //** notnull, 本次提交的回答 {@link ISubmitLoader#requireNewCells(SubmitContext)}  */
    private boolean needUpdateResponse;                                 //** nullable, 标记最终是否需要更新 response */
    private boolean needUpdateChannelRecord;                            //** nullable, 标记最终是否需要更新 response */
    private String externalUserId;
    // 待更新数据
    private List<SurveyResponseCell> addCells = new ArrayList<>();      //** notnull, 待新增的回答 {@link ISubmitLoader#compareCells(SubmitContext)}  */
    private List<SurveyResponseCell> updateCells = new ArrayList<>();   //** notnull, 待更新的回答 {@link ISubmitLoader#compareCells(SubmitContext)}  */
    private List<SurveyResponseCell> deleteCells = new ArrayList<>();   //** notnull, 待删除的回答 {@link ISubmitLoader#compareCells(SubmitContext)}  */

    // 提交答卷返回的数据
    private ResponseResultDto responseResultDto = new ResponseResultDto();

    //问卷免打扰
    private Boolean doNotDisturb = false;

    private Throwable exception;

    // 标记加载过的数据，下次获取直接返回，不再加载
    private final Set<String> loaded = new HashSet<>();

    public SubmitContext(boolean submit) {
        this.submit = submit;
    }

    public void setData(SurveySubmitRequestDto data) {
        if (data == null) {
            throw new BadRequestException("请求参数错误");
        }
        this.data = data;
        this.clientId = data.getClientId();
    }

    public void checkParams() {
        if (surveyId == null || data == null || (StringUtils.isEmpty(data.getClientId()) && data.getResponseId() == null) || !data.isValid()) {
            throw new BadRequestException("请求参数错误");
        }
    }

    public <T> T requireProperty(String flag, Supplier<T> get, Consumer<T> set, Supplier<T> load) {
        T property = getProperty(flag, get, set, load);
        if (property == null) {
            throw new BadRequestException(flag + "属性不能为null");
        }
        return property;
    }

    public <T> T getProperty(String flag, Supplier<T> get, Consumer<T> set, Supplier<T> load) {
        Objects.requireNonNull(flag, "属性标记符号不能为null");
        Objects.requireNonNull(get, "属性get方法不能为null");
        Objects.requireNonNull(set, "属性set方法不能为null");
        Objects.requireNonNull(load, "属性load方法不能为null");
        if (loaded.contains(flag)) {
            return get.get();
        } else {
            loaded.add(flag);
            T v = load.get();
            set.accept(v);
            return v;
        }
    }
}
