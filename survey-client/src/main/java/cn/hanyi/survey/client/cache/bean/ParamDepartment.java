package cn.hanyi.survey.client.cache.bean;

import org.apache.commons.lang3.StringUtils;

public class ParamDepartment extends Param {

    public Long id;
    public Long orgId;
    public String code;

    public ParamDepartment(Long id) {
        this(id, null, null);
    }

    public ParamDepartment(Long orgId, String code) {
        this(null, orgId, code);
    }

    public ParamDepartment(Long id, Long orgId, String code) {
        super(id != null ? id.toString() : (orgId + "-" + code));
        this.id = id;
        this.orgId = orgId;
        this.code = code;
    }

    public boolean hasId() {
        return id != null;
    }

    public boolean hasCode() {
        return orgId != null && StringUtils.isNotEmpty(code);
    }

    @Override
    public String value() {
        if (hasId()) {
            return hkId();
        } else if (hasCode()) {
            return hkCode();
        }
        return null;
    }

    public String hkId() {
        return id.toString();
    }

    public String hkCode() {
        return "code-"+code;
    }
}
