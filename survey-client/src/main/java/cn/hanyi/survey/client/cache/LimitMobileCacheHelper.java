package cn.hanyi.survey.client.cache;

import cn.hanyi.survey.client.dto.ClientQuestion;
import cn.hanyi.survey.core.constant.question.QuestionType;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.extension.nativesql.SqlBuilder;
import org.springframework.data.redis.core.DefaultTypedTuple;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ForkJoinPool;
import java.util.function.Function;

@Slf4j
@Service
public class LimitMobileCacheHelper extends CacheHelper {
    private static final String K_MOBILE = "cache:mobile-limit:%d:%d:mobile";         // surveyId questionId  zset value=mobile score=responseId
    private static final String K_SYNC = "cache:mobile-limit:%d:%d:sync";             // surveyId questionId
    private static final int SIZE_PER_PAGE = 500;

    private final ForkJoinPool executorService = ForkJoinPool.commonPool();

    public void add(Long surveyId, Long responseId, List<ClientQuestion> allQuestions, Map<String, Object> data) {
        if (surveyId != null && responseId != null && CollectionUtils.isNotEmpty(allQuestions) && MapUtils.isNotEmpty(data)) {
            allQuestions.forEach(q -> {
                if (isEnableLimit(q)) {
                    // 如果手机题开启了去重，则把提交了的答案的手机号，写入缓存
                    Object mobile = data.get(q.getName());
                    if (mobile != null) {
                        String mobileKey = mobileKey(surveyId, q.getId());
                        zSetOpt().add(mobileKey, mobile.toString(), responseId);
                        expireKey(mobileKey);
                    }
                }
            });
        }
    }

    /**
     * 每次重新打开问卷时，都要清空缓存，下次重新开始缓存
     * 批量删除答卷时，也直接清空缓存
     */
    public void removeAllSurveyResponse(Long surveyId) {
        List<Long> questionIds = getOpenDeduplicationQuestionIds(surveyId);
        if (CollectionUtils.isNotEmpty(questionIds)) {
            List<String> keys = new ArrayList<>();
            questionIds.forEach(id -> {
                keys.add(mobileKey(surveyId, id));
                keys.add(syncKey(surveyId, id));
            });
            if (!keys.isEmpty()) {
                delete(keys);
                log.info("删除问卷{}的所有开启了手机号去重的所有答卷缓存:{}", surveyId, String.join(", ", keys));
            }
        }
    }

    /**
     * 删除答卷后，如果这个答卷包含手机题，则清空这个缓存
     */
    public void removeResponse(Long surveyId, Long responseId) {
        if (surveyId != null && responseId != null) {
            List<Long> questionIds = getOpenDeduplicationQuestionIds(surveyId);
            if (CollectionUtils.isNotEmpty(questionIds)) {
                questionIds.forEach(questionId -> {
                    String mobileKey = mobileKey(surveyId, questionId);
                    Set<String> values = zSetOpt().rangeByScore(mobileKey, responseId, responseId);
                    if (CollectionUtils.isNotEmpty(values)) {
                        zSetOpt().remove(mobileKey, values.toArray());
                        log.info("删除问卷{}的问题{}的缓存:{}", surveyId, questionId, String.join(",", values));
                    }
                });
            }
        }
    }

    /**
     * 查询指定mobile,是否有填答记录
     */
    public Long getByMobile(Long surveyId, Long questionId, String mobile) {
        if (!enableCache) {
            return getByMobileInDb(surveyId, questionId, mobile);
        }
        if (isSyncCompleted(surveyId, questionId)) {
            Double score = zSetOpt().score(mobileKey(surveyId, questionId), mobile);
            if (score != null) {
                return score.longValue();
            }
        } else {
            // 开始异步缓存
            async(surveyId, questionId);
            return getByMobileInDb(surveyId, questionId, mobile);
        }
        return null;
    }

    private Long getByMobileInDb(Long surveyId, Long questionId, String mobile) {
        ResponseCellMobile responseCellMobile = getResponseCellMobileByMobile(surveyId, questionId, mobile);
        if (responseCellMobile != null) {
            return responseCellMobile.getResponseId();
        }
        return null;
    }

    private boolean isSyncCompleted(Long surveyId, Long questionId) {
        String syncKey = syncKey(surveyId, questionId);
        String sync = valueOpt().get(syncKey);
        return sync != null && sync.equals(SYNC_COMPLETE);
    }

    private void async(Long surveyId, Long questionId) {
        executorService.execute(() -> sync(surveyId, questionId));
    }

    private boolean isEnableLimit(ClientQuestion question) {
        return question.getIsDeduplication() != null && question.getIsDeduplication();
    }

    /**
     * 找到打开了手机号去重的问题id
     */
    private List<Long> getOpenDeduplicationQuestionIds(Long surveyId) {
        SqlBuilder sqlBuilder = SqlBuilder.select(
                        "SELECT id, type FROM survey_question")
                .where("s_id=%d", surveyId).alwaysTrue()
                .where("type=%d", QuestionType.MOBILE.ordinal()).alwaysTrue()
                .where("is_deduplication=%d", 1).alwaysTrue();
        return nativeSqlHelper.queryList(sqlBuilder, Function.identity(), (ignore, ids) -> ids);
    }

    /**
     * 通过手机题id，手机号，找到答卷id
     */
    private ResponseCellMobile getResponseCellMobileByMobile(Long surveyId, Long questionId, String mobile) {
        if (surveyId == null || questionId == null || StringUtils.isEmpty(mobile)) {
            return null;
        }
        String sql = String.format("SELECT sr.id as responseId, src.s_val as mobile FROM survey_response sr " +
                "inner join survey_response_cell src on sr.id = src.r_id " +
                "where sr.s_id = %d and sr.is_completed=1 and sr.deleted=0 and src.q_id = %d and src.s_val = '%s' limit 1", surveyId, questionId, mobile);
        return nativeSqlHelper.queryObject(sql, ResponseCellMobile.class);
    }

    /**
     * 通过问卷，手机题id，找到所有的答卷的手机号
     */
    private List<ResponseCellMobile> getResponseCellMobileByPage(Long surveyId, Long questionId, int page) {
        if (surveyId == null || questionId == null) {
            return null;
        }
        int start = (page - 1) * SIZE_PER_PAGE;
        String sql = String.format("SELECT sr.id as responseId, src.s_val as mobile FROM survey_response sr " +
                "inner join survey_response_cell src on sr.id = src.r_id " +
                "where sr.s_id = %d and sr.is_completed=1 and sr.deleted=0 and src.q_id = %d limit %d, %d", surveyId, questionId, start, SIZE_PER_PAGE);
        return nativeSqlHelper.queryListObject(sql, ResponseCellMobile.class);
    }

    private void sync(Long surveyId, Long questionId) {
        String syncKey = syncKey(surveyId, questionId);
        Boolean sync = valueOpt().setIfAbsent(syncKey, SYNC_PROGRESS);
        if (sync != null && sync) {
            String mobileKey = mobileKey(surveyId, questionId);
            try {
                int page = 1;
                boolean hasNext = true;
                while (hasNext) {
                    List<ResponseCellMobile> list = getResponseCellMobileByPage(surveyId, questionId, page);
                    if (CollectionUtils.isNotEmpty(list)) {
                        cacheItems(mobileKey, list);
                        page++;
                    } else {
                        hasNext = false;
                    }
                }
            } finally {
                expireKey(mobileKey);
                syncCompleted(syncKey);
            }
        }
    }

    private void cacheItems(String mobileKey, List<ResponseCellMobile> list) {
        Set<ZSetOperations.TypedTuple<String>> mobileSet = new HashSet<>();
        list.forEach(i -> {
            if (i.getResponseId() != null && StringUtils.isNotEmpty(i.getMobile())) {
                double score = i.getResponseId();
                mobileSet.add(new DefaultTypedTuple<>(i.getMobile(), score));
            }
        });
        if (!mobileSet.isEmpty()) {
            zSetOpt().add(mobileKey, mobileSet);
        }
    }

    private String syncKey(Long surveyId, Long questionId) {
        return String.format(K_SYNC, surveyId, questionId);
    }

    private String mobileKey(Long surveyId, Long questionId) {
        return String.format(K_MOBILE, surveyId, questionId);
    }

    @Getter
    @Setter
    public static class ResponseCellMobile {
        private Long responseId;
        private String mobile;
    }
}
