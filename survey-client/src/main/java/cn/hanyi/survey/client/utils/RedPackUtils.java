package cn.hanyi.survey.client.utils;

import cn.hanyi.survey.client.exception.LotteryException;
import cn.hutool.core.util.RandomUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/9 18:00:38
 */
public class RedPackUtils {

    /**
     * 拼手气红包: 单位元
     * <p>
     * 将（amount-min*num = x）剩余的钱用于计算，防止最后得到的数据小于min
     * 如果金额100元,剩余金额（100-x），个数10个，每人平均就是(100-x)/10 = y元
     * 然后再(0,2y)=z中取，y+z等于抽到的红包，最后一个等于：总-已抽
     *
     * @param amount 金额
     * @param min    最小金额 单位分
     * @param max    最大金额 单位分
     * @param num    红包个数
     */
    public static List<BigDecimal> RandomRedPack(BigDecimal amount, BigDecimal min, BigDecimal max, BigDecimal num) {
        if (min.multiply(num).compareTo(amount) > 0) {
            throw new LotteryException("你的钱不够" + num + "个人分啊");
        }
        if (amount.compareTo(max.multiply(num)) > 0) {
            throw new LotteryException("你的钱平均每个人超过最大金额" + num + "了");
        }
        BigDecimal sum = BigDecimal.ZERO;
        //保留小数位
        int decimal = 2;
        //最大红包金额
        BigDecimal maxMoney = max.subtract(min);

        List<BigDecimal> list = new ArrayList<>();
        //每人最低金额之和
        BigDecimal minTotalMoney = amount.subtract(min.multiply(num));
        for (int i = 0; i < num.intValue(); i++) {
            //最后一个红包
            if (i == num.intValue() - 1) {
                BigDecimal last = amount.subtract(sum);
                list.add(last);
                sum = sum.add(last);
            } else if (minTotalMoney.compareTo(BigDecimal.ZERO) == 0) {
                list.add(min);
                sum = sum.add(min);
            } else {
                //红包金额/红包个数*2
                BigDecimal money = minTotalMoney.divide(num.subtract(new BigDecimal(i)), decimal, RoundingMode.FLOOR).multiply(new BigDecimal(2));
                double random = RandomUtil.randomDouble(0, money.doubleValue());
                BigDecimal winnerMoney = new BigDecimal(random).setScale(decimal, RoundingMode.FLOOR);
                if (winnerMoney.compareTo(maxMoney) > 0) {
                    //最大金额只能是200
                    winnerMoney = maxMoney;
                }
                //抽完一个之后，剩余的金额/剩余的个数<=200 ,保证任何一个红包都小于等于200
                //(minTotalMoney) / (num - (i + 1)) > max
                if (minTotalMoney.divide(new BigDecimal(num.intValue() - (i + 1)), 2, RoundingMode.FLOOR).compareTo(max) > 0) {
                    winnerMoney = maxMoney;
                }
                //剩余金额：
                minTotalMoney = minTotalMoney.subtract(winnerMoney);

                //红包金额：加上最低中奖金额
                winnerMoney = winnerMoney.add(min);

                if (winnerMoney.compareTo(new BigDecimal("200")) > 0) {
                    throw new LotteryException("单个红包不能大于200");
                }
                list.add(winnerMoney);
                sum = sum.add(winnerMoney);
            }
        }
        if (sum.compareTo(amount) != 0) {
            throw new RuntimeException("最终中奖金额和输入金额不一致");
        }
        return list;
    }


    /**
     * 拼手气红包:单位分
     * <p>
     * 将（amount-min*num = x）剩余的钱用于计算，防止最后得到的数据小于min
     * 如果金额100元,剩余金额（100-x），个数10个，每人平均就是(100-x)/10 = y元
     * 然后再(0,2y)=z中取，y+z等于抽到的红包，最后一个等于：总-已抽
     *
     * @param amount 金额 单位分
     * @param min    最小金额 单位分
     * @param max    最大金额 单位分
     * @param num    红包个数
     */
    public static List<Integer> RandomRedPack(int amount, int min, int max, int num) {
        if (min * num > amount) {
            throw new LotteryException("你的钱不够" + num + "个人分啊");
        }
        if (amount / num > max) {
            throw new LotteryException("你的钱平均每个人超过最大金额" + num + "了");
        }
        int sum = 0;
        //最大红包金额
        int maxMoney = max - min;

        List<Integer> list = new ArrayList<>();
        //每人最低金额之和
        int minTotalMoney = amount - (min * num);
        for (int i = 0; i < num; i++) {
            //最后一个红包
            if (i == num - 1) {
                int last = amount - sum;
                list.add(last);
                sum = sum + last;
            } else if (minTotalMoney == 0) {
                list.add(min);
                sum = sum + min;
            } else {
                //红包金额/红包个数*2
                int money = minTotalMoney / (num - i) * 2;
                int winnerMoney = RandomUtil.randomInt(0, money);
                if (winnerMoney > maxMoney) {
                    //最大金额只能是200
                    winnerMoney = maxMoney;
                }
                //抽完一个之后，剩余的金额/剩余的个数<=200 ,保证任何一个红包都小于等于200
                if ((minTotalMoney) / (num - (i + 1)) > max) {
                    winnerMoney = maxMoney;
                }
                //剩余金额：
                minTotalMoney = minTotalMoney - winnerMoney;

                //红包金额：加上最低中奖金额
                winnerMoney = winnerMoney + min;

                list.add(winnerMoney);
                sum = sum + winnerMoney;
            }
        }
        if (sum != amount) {
            throw new RuntimeException("最终中奖金额和输入金额不一致");
        }
        return list;
    }

    /**
     * 普通红包：固定金额
     *
     * @param money 单个红包金额 单位元
     * @param num
     * @return
     */
    public static List<BigDecimal> fixedRedPack(BigDecimal money, BigDecimal num) {
        List<BigDecimal> list = new ArrayList<>();
        for (int i = 0; i < num.intValue(); i++) {
            list.add(money);
        }
        return list;
    }

    /**
     * 普通红包：固定金额
     *
     * @param money 单个红包金额 单位分
     * @param num
     * @return
     */
    public static List<Integer> fixedRedPack(Integer money, Integer num) {
        List<Integer> list = new ArrayList<>();
        for (int i = 0; i < num; i++) {
            list.add(money);
        }
        return list;
    }

    public static void testInt() {
        //单位分
        int[][] rrr = {
                {10000, 10},
        };
        int min = 100;
        int max = 2000;
        for (int i = 0; i < 10; i++) {
            System.out.println("----------" + "第" + i + "次循环" + "--------------");
            for (int[] decimals : rrr) {
                //红包金额
                int amount = decimals[0];
                //红包个数
                int num = decimals[1];
                System.out.println("===============" + amount + "元" + num + "个人抢============================");
                RandomRedPack(amount, min, max, num);
            }
        }
    }

    public static void testBigDecimal() {
        BigDecimal[][] rrr = {
                {new BigDecimal("2000"), new BigDecimal("10")},
                //{new BigDecimal("1"), new BigDecimal("10")},
                //{new BigDecimal("10"), new BigDecimal("10")},
                // {new BigDecimal("1000"), new BigDecimal("10")}
        };
        BigDecimal min = new BigDecimal("1");
        BigDecimal max = new BigDecimal("200");
        for (int i = 0; i < 10000; i++) {
            System.out.println("----------" + "第" + i + "次循环" + "--------------");
            for (BigDecimal[] decimals : rrr) {
                final BigDecimal amount = decimals[0];
                final BigDecimal num = decimals[1];
                System.out.println("===============" + amount + "元" + num + "个人抢============================");
                RandomRedPack(amount, min, max, num);
            }
        }
    }

    public static void main(String[] args) {

        //testBigDecimal();
        testInt();
    }

}
