package cn.hanyi.survey.core.entity.template;

import cn.hanyi.survey.core.dto.ext.TemplateSurveyExtDto;
import cn.hanyi.survey.core.entity.BaseSurvey;
import cn.hanyi.survey.core.entity.SurveyQuota;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.rest.view.ResourceViews;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

@Entity
@Getter
@Setter
@Table(name = "template_survey")
@AllArgsConstructor
@NoArgsConstructor
@SQLDelete(sql = "UPDATE template_survey SET deleted = 1 WHERE id=?")
@Where(clause = "deleted=0")
@EntityScopeStrategy
@DtoClass(includeAllFields = true, superClass = TemplateSurveyExtDto.class)
public class TemplateSurvey extends BaseSurvey {

    @ManyToOne
    @JoinColumn(name = "group_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @JsonView(ResourceViews.Basic.class)
    @NotFound(action = NotFoundAction.IGNORE)
    private TemplateGroup group;

    @Schema(description = "可否编辑 0：不能 1：可以")
    @JsonView(ResourceViews.Basic.class)
    private Boolean editable = true;

    @OneToMany(mappedBy = "survey", cascade = {CascadeType.ALL}, orphanRemoval = true, fetch = FetchType.LAZY)
    @OrderBy("sequence")
    @JsonView(ResourceViews.Basic.class)
    private List<TemplateSurveyQuestion> questions = new ArrayList<>();

    @OneToMany(mappedBy = "survey", cascade = {CascadeType.ALL}, orphanRemoval = true, fetch = FetchType.LAZY)
    @JsonView(ResourceViews.Detail.class)
    @OrderBy("id")
    private List<TemplateSurveyLogic> logics = new ArrayList<>();

    @OneToMany(mappedBy = "survey", cascade = {CascadeType.ALL}, orphanRemoval = true, fetch = FetchType.LAZY)
    @JsonView(ResourceViews.Detail.class)
    @OrderBy("id")
    private List<SurveyQuota> quotas = new ArrayList<>();

    @OneToMany(mappedBy = "survey", cascade = {CascadeType.ALL}, orphanRemoval = true, fetch = FetchType.LAZY)
    @JsonView(ResourceViews.Detail.class)
    @OrderBy("id")
    private List<TemplateSurveyQuestionRandom> randoms = new ArrayList<>();

    @OneToMany(mappedBy = "survey", cascade = {CascadeType.ALL}, orphanRemoval = true, fetch = FetchType.LAZY)
    @JsonView(ResourceViews.Detail.class)
    @OrderBy("id")
    private List<TemplateSurveyLanguage> languages = new ArrayList<>();

    @Size(max = 100, message = "模板简介地址长度超过限制")
    @Schema(description = "模板简介地址")
    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(jsonView = ResourceViews.Basic.class, queryable = true)
    private String remark;

}
