package cn.hanyi.survey.core.repository;

import cn.hanyi.survey.core.entity.ResponseSharedConfig;
import cn.hanyi.survey.core.entity.Survey;
import org.befun.core.repository.ResourceRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface ResponseSharedConfigRepository extends ResourceRepository<ResponseSharedConfig, Long> {

    Optional<ResponseSharedConfig> findBySurvey(Survey survey);

    Optional<ResponseSharedConfig> findBySurveyAndEnableAndWithToken(Survey survey, Boolean enable, Boolean withToken);

}
