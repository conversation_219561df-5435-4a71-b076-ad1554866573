package cn.hanyi.survey.core.entity;

import cn.hanyi.survey.core.constant.disturb.DisturbMethod;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/12/30 10:36
 */
@Entity
@Getter
@Setter
@Table(name = "survey_disturb_record")
@DtoClass(includeAllFields = true)
public class SurveyDisturbRecord extends EnterpriseEntity {
    private static final long serialVersionUID = 6529685098267757690L;

    @Schema(description = "survey id", required = true)
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "s_id")
    private Long sid;

    @Schema(description = "外参id", required = true)
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "euid")
    private String externalUserId;

    @Schema(description = "免打扰方式", required = true)
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "disturb_method")
    private DisturbMethod disturbMethod;

    @Schema(description = "渠道id", required = true)
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "channel_id")
    private Long channelId;

    @Schema(description = "clientId", required = true)
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "client_id")
    private String clientId;

    public SurveyDisturbRecord() {

    }

    public SurveyDisturbRecord(Long orgId, Long sid, String externalUserId, DisturbMethod disturbMethod, Long channelId, String clientId) {
        this.orgId = orgId;
        this.sid = sid;
        this.externalUserId = externalUserId;
        this.disturbMethod = disturbMethod;
        this.channelId = channelId;
        this.clientId = clientId;
    }

}
