package cn.hanyi.survey.core.dto.channel;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Setter
@Getter
public class SendStatisticDto extends BaseDTO {
    @Schema(description = "任务状态：0 创建中； 1 发送中；2 发送完成；3 创建失败")
    private int taskStatus = 1;
    @Schema(description = "发送成功")
    private Long sendSuccessCount = 0L;
    @Schema(description = "待发送")
    private Long unSendCount = 0L;
    @Schema(description = "发送失败")
    private Long sendFailCount = 0L;
    @Schema(description = "已提交")
    private Long submitSuccessCount = 0L;
    @Schema(description = "未提交")
    private Long unSubmitCount = 0L;
    @Schema(description = "未访问")
    private Long unVisitCount = 0L;


}