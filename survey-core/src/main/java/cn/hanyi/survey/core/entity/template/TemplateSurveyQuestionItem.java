package cn.hanyi.survey.core.entity.template;

import cn.hanyi.survey.core.entity.BaseQuestionItem;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;

import javax.persistence.*;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "template_survey_question_item")
@DtoClass(includeAllFields = true)
public class TemplateSurveyQuestionItem extends BaseQuestionItem {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "q_id")
    @DtoProperty(ignore = true)
    private TemplateSurveyQuestion question;

}
