package cn.hanyi.survey.core.repository;

import cn.hanyi.survey.core.constant.channel.ChannelType;
import cn.hanyi.survey.core.entity.SurveyChannel;
import cn.hanyi.survey.core.projection.SimpleSurveyChannel;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SurveyChannelRepository extends ResourceRepository<SurveyChannel, Long> {

    SurveyChannel findOneById(Long id);

    Page<SurveyChannel> findAllBySid(Long surveyId, Pageable pageable);

    List<SurveyChannel> findAllBySid(Long surveyId);

    List<SurveyChannel> findBySidAndType(Long sid, ChannelType type);

    List<SurveyChannel> findAllByIdIn(List<Long> ids);

    SimpleSurveyChannel findSimpleById(Long id);


    List<SurveyChannel> findAllBySidAndDeleted(Long surveyId, Boolean deleted);

}
