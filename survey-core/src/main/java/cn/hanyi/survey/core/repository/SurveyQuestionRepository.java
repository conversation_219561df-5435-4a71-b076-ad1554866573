package cn.hanyi.survey.core.repository;

import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.core.projection.SimpleDynamicItemQuestion;
import cn.hanyi.survey.core.projection.SimpleQuestion2;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Repository
public interface SurveyQuestionRepository extends ResourceRepository<SurveyQuestion, Long> {
    @EntityGraph(value = "Question.Graph", type = EntityGraph.EntityGraphType.FETCH)
    List<SurveyQuestion> findAllBySurvey(Survey survey);

    List<SurveyQuestion> findAllBySurveyAndSequenceGreaterThanEqual(Survey survey, int sequence);

    Optional<SurveyQuestion> findBySurveyAndName(Survey survey, String name);

    @Modifying
    @Query(nativeQuery = true,
            value = "update `survey_question` set `sequence` = `sequence`+?1 WHERE `s_id` = ?2 AND `sequence` >= ?3")
    void increaseSequenceBySurveyIdAndSequence(int step, Long surveyId, Number sequence);

    @Query(nativeQuery = true,
            value = "select id,s_id,title,type,is_score from survey_question where type in (?2) AND s_id in (?1) order by sequence ")
    List<Map<String, Object>> findAllSimpleQuestion(List<Long> ids, List<Integer> list);

    List<SurveyQuestion> findBySurveyIn(List<Survey> ids);

    void deleteByIdIn(List<Long> qid);

    List<SurveyQuestion> findBySurveyAndNameIn(Survey survey, List<String> names);

    SimpleQuestion2 findSimple2ByIdAndSurvey(Long id, Survey survey);

    @Transactional
    @Modifying
    @Query("update SurveyQuestion s set s.sequence = ?1 where s.survey = ?2 and s.id = ?3")
    int updateSequenceBySurveyAndId(BigDecimal sequence, Survey survey, Long id);

    List<SimpleDynamicItemQuestion> findBySurveyAndIsDynamicItem(Survey survey, Boolean isDynamicItem);
}
