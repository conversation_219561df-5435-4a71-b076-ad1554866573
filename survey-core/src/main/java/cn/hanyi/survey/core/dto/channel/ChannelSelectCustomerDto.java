package cn.hanyi.survey.core.dto.channel;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.stream.Collectors;

@Getter
@Setter
public class ChannelSelectCustomerDto {

    @JsonIgnore
    @Schema(hidden = true, description = "是否选择全部客户：true/false, 从departmentIds获取  ")
    private Boolean all;

    @JsonIgnore
    @Schema(hidden = true, description = "是否选择无部门客户：true/false, 从departmentIds获取")
    private Boolean noDepartment;

    @Schema(description = "选择的部门id：-1 全部 -2 未分组")
    private List<Long> departmentIds;

    @Schema(description = "选择的组id")
    private List<Long> groupIds;

    @Schema(description = "选择的客户id")
    private List<Long> selectedCustomerIds;

    @Schema(description = "排除的客户id")
    private List<Long> excludeCustomerIds;

    @Schema(description = "发送上传文件里的客户的文件路径（可为空）")
    private String fileUrl;

    @Schema(description = "发送指定信息的客户列表（可为空）")
    private List<UserContactDto> contacts;

    public Boolean getAll() {
        if (all == null) {
            all = departmentIds != null && departmentIds.contains(-1L);
        }
        return all;
    }

    public Boolean getNoDepartment() {
        if (noDepartment == null) {
            noDepartment = departmentIds != null && departmentIds.contains(-2L);
        }
        return noDepartment;
    }

    public List<Long> filterDepartmentIds() {
        if (departmentIds == null) {
            return null;
        }
        return departmentIds.stream().filter(i -> i != -1 && i != -2).collect(Collectors.toList());
    }
}
