package cn.hanyi.survey.core.dto.ext;

import cn.hanyi.survey.core.entity.SurveyQuota;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.rest.view.ResourceViews;

@Getter
@Setter
public class SurveyQuotaExtDto extends BaseEntityDTO<SurveyQuota> {

    public SurveyQuotaExtDto() {
    }

    public SurveyQuotaExtDto(SurveyQuota entity) {
        super(entity);
    }

    @Schema(description = "当前配额")
    @JsonView(ResourceViews.Basic.class)
    private Long current = 0L;
}
