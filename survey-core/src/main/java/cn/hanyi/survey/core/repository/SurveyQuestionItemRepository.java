package cn.hanyi.survey.core.repository;

import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.core.entity.SurveyQuestionItem;
import cn.hanyi.survey.core.projection.SimpleQuestionItem;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Repository
public interface SurveyQuestionItemRepository extends ResourceRepository<SurveyQuestionItem, Long> {
    void deleteAllByQuestion(SurveyQuestion question);

    List<SimpleQuestionItem> findByQuestion(SurveyQuestion question);

    List<SurveyQuestionItem> findByQuestionIdOrderBySequenceAsc(Long id);

    List<SimpleQuestionItem> findByQuestionIn(List<SurveyQuestion> question);

    @Query(nativeQuery = true,
            value = "select id,q_id,text,value from survey_question_item where q_id in (?1)  order by sequence")
    List<Map<String, Object>> findAllSimpleQuestionItem(List<Long> ids);

    void deleteByIdIn(List<Long> ids);

    Optional<List<SurveyQuestionItem>> findByQuestionId(Long questionId);
}
