package cn.hanyi.survey.core.dto.message;

import cn.hanyi.survey.core.constant.question.FormatType;
import cn.hanyi.survey.core.constant.question.InputType;
import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.core.entity.SurveyResponseCell;
import com.fasterxml.jackson.annotation.JsonView;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.*;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.dto.BaseDTO;
import org.befun.core.rest.view.ResourceViews;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static cn.hanyi.ctm.utils.RegularExpressionUtils.replaceHtml;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Setter
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SurveyResponseCellMessageDto extends BaseDTO {

    @JsonView(ResourceViews.Basic.class)
    private String title;
    @JsonView(ResourceViews.Basic.class)
    private Object value;
    @JsonView(ResourceViews.Basic.class)
    private Object comment;
    @JsonView(ResourceViews.Basic.class)
    private String code;
    @JsonView(ResourceViews.Basic.class)
    private Long questionId;
    @JsonView(ResourceViews.Basic.class)
    private List<String> tags;
    @JsonView(ResourceViews.Basic.class)
    private List<String> tagsAll;
    @JsonView(ResourceViews.Basic.class)
    private QuestionType type;
    private Map<String, QuestionsItemsDto> questionsItems;
    @JsonView(ResourceViews.Basic.class)
    private String inapplicableLabel;
    @JsonView(ResourceViews.Basic.class)
    private FormatType formatType;
    @JsonView(ResourceViews.Basic.class)
    private Integer max;

    public SurveyResponseCellMessageDto(SurveyQuestion question, SurveyResponseCell cell) {

        QuestionsItemsDto items = new QuestionsItemsDto();
        items.setValue(cell.getValue());

        if (!question.getItems().isEmpty()) {
            ArrayList itemValue = new ArrayList();
            ArrayList columnValue = new ArrayList();
            question.getItems().forEach(x -> {
                QuestionsItemDto item = new QuestionsItemDto();
                item.setName(x.getValue());
                item.setText(x.getText());
                item.setSequence(x.getSequence());
                itemValue.add(item);
            });

            question.getColumns().forEach(x -> {
                QuestionsItemDto column = new QuestionsItemDto();
                column.setName(x.getValue());
                column.setText(x.getText());
                column.setSequence(x.getSequence());
                columnValue.add(column);
            });
            items.setItems(itemValue);
            items.setColumns(columnValue);
        }

        this.questionsItems = Map.of(question.getName(), items);

        // question
        this.type = question.getType();
        this.title = question.getTitle();
        this.questionId = question.getId();
        this.value = cell.getValue();
        this.comment = cell.getCommentValue();
        this.tags = cell.getTags() == null ? null : Arrays.stream(cell.getTags().split(",")).collect(Collectors.toList());
        Optional.ofNullable(question.getItems()).ifPresent(x -> {
            x.stream().filter(y -> y.getValue().equals(this.value)).anyMatch(z -> {
                var ts = z.getConfigure();
                // 打分评价题特殊推送所有标签
                if (List.of(QuestionType.SCORE_EVALUATION, QuestionType.EVALUATION).contains(this.type) && StringUtils.isNotEmpty(ts)) {
                    this.tagsAll = Arrays.stream(ts.split(",")).collect(Collectors.toList());
                }
                return false;
            });
        });
        this.code = question.getCode();
        this.inapplicableLabel = question.getInapplicableLabel();
        this.formatType = question.getAreaType();
        //量表、矩阵量表  非nps 数字  min max
        //       点赞  maxLength
        //       星星  maxLength
        //       爱心  maxLength
        this.max = question.getMax();
        if (List.of(QuestionType.SCORE, QuestionType.MATRIX_SCORE).contains(question.getType())) {
            if (!question.getIsNps()) {
                if (!InputType.NUMBER.equals(question.getInputType())) {
                    this.max = question.getMaxLength();
                }
            }
        }
    }


    public void convertText() {
        convertText(true);
    }

    public void convertText(Boolean trimHtml) {
        String textTitle = null;

        if (List.of(QuestionType.BLANK, QuestionType.MULTIPLE_BLANK).contains(type)) {
            textTitle = type.getText();
        } else {
            textTitle = trimHtml ? replaceHtml(title) : title;
        }
        setTitle(textTitle);

        Iterator itemsIterator = questionsItems.values().iterator();
        if (itemsIterator.hasNext()) {
            ObjectMapper objectMapper = new ObjectMapper();
            QuestionsItemsDto items = objectMapper.convertValue(itemsIterator.next(), QuestionsItemsDto.class);
            // 日期题
            if (type == QuestionType.DATE && items.getValue() != null && formatType != null && Objects.toString(items.getValue()).matches("^-?[1-9]\\d*$")) {
                value = new SimpleDateFormat(formatType.getFormat()).format(new Date(Long.parseLong(String.valueOf(items.getValue()))));
            }

            if (items.getItems() != null && type != QuestionType.NPS) {

                String otherString = "other";
                HashMap<Object, Object> convertMap = new HashMap<>();
                ArrayList<Object> convertList = new ArrayList<>();
                AtomicReference<String> convertString = new AtomicReference<>();
                Object itemValue = items.getValue();

                List<QuestionsItemDto> itemsList = items.getItems();
                List<String> itemsNameList = itemsList.stream().map(x -> x.getName()).collect(Collectors.toList());

                HashMap<Object, Object> finalConvertMap = convertMap;
                itemsList.forEach(item -> {
                    String name = item.getName();
                    if (itemValue == null) {
                        return;
                    }

                    if (itemValue instanceof Map) {
                        if (((Map) itemValue).containsKey(name)) {
                            Object mapValue = ((Map) itemValue).get(name);
                            Optional<String> columnsTextOptional = items.getColumns() == null ? Optional.empty() : items.getColumns().stream().filter(x -> x.getName().equals(mapValue)).map(x -> x.getText()).findFirst();
                            if (!columnsTextOptional.isEmpty()) {
                                finalConvertMap.put(trimHtml ? replaceHtml(item.getText()) : item.getText(), columnsTextOptional.get());
                            } else {
                                finalConvertMap.put(trimHtml ? replaceHtml(item.getText()) : item.getText(), mapValue);
                            }
                        }
                    } else if (itemValue instanceof List) {
                        if (((List<?>) itemValue).contains(name)) {
                            convertList.add(trimHtml ? replaceHtml(item.getText()) : item.getText());
                        }
                    } else {
                        if (itemValue.equals(name)) {
                            convertString.set(trimHtml ? replaceHtml(item.getText()) : item.getText());
                        } else {
                            // 单选其他
                            if (convertString.get() == null) {
                                // 单选其他为空
                                if (otherString.equals(itemValue) && Objects.toString(comment).isEmpty()) {
                                    convertString.set(comment != null ? comment.toString() : "");
                                } else {
                                    convertString.set(trimHtml ? replaceHtml((String) itemValue) : (String) itemValue);
                                }
                            }
                        }
                    }
                });
                // 多选其他
                if (itemValue instanceof List) {
                    Optional<?> otherValue = ((List<?>) itemValue).stream().filter(i -> !itemsNameList.contains(i)).findFirst();
                    // 有其他值，但是其他值不为空
                    if (otherValue.isPresent() && otherString.equals(Objects.toString(otherValue.get()))) {
                        convertList.add(comment != null ? comment.toString() : "");
                    }
                }

                if (cn.hanyi.ctm.constant.survey.QuestionType.RANKING.equals(type) && !convertMap.isEmpty()) {
                    // 排序题根据选择排序
                    HashMap<Object, Object> convertMapSort = new LinkedHashMap<>();
                    ((Map) convertMap).entrySet().stream().sorted(Map.Entry.comparingByValue()).forEachOrdered(x -> {
                        convertMapSort.put(((Map.Entry<?, ?>) x).getKey(), ((Map.Entry<?, ?>) x).getValue());
                    });
                    convertMap = convertMapSort;
                }

                setValue(convertList.isEmpty() ? (convertMap.isEmpty() ? convertString.get() : convertMap) : convertList);

            }
            // 逐级下拉类似多选，但是没有选项需要单独处理

            if (this.getType() != null && Arrays.asList(cn.hanyi.ctm.constant.survey.QuestionType.DROP_DOWN, cn.hanyi.ctm.constant.survey.QuestionType.ORGANIZE).contains(this.getType())) {
                if (value != null) {
                    value = ((List) value).isEmpty() ? null : value;
                }
            }
        }
    }

}
