package cn.hanyi.survey.core.dto.survey;

import cn.hanyi.survey.core.constant.disturb.DisturbMethod;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;

@Getter
@Setter
public class SurveyDisturbFilterDto {

    @Schema(description = "免打扰方式", required = true)
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "disturb_method")
    private DisturbMethod disturbMethod;

    @Schema(description = "天数", required = true)
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "disturb_days")
    private Integer disturbDays;
}
