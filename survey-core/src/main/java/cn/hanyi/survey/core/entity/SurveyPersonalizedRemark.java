package cn.hanyi.survey.core.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;

/**
 * 个性化结束语
 */
@Entity
@Getter
@Setter
@Table(name = "survey_personalized_remark")
@DtoClass(includeAllFields = true)
public class SurveyPersonalizedRemark extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "s_id")
    @DtoProperty(ignore = true)
    @JsonIgnore
    private Survey survey;

    @Schema(description = "是否正常结束", required = true)
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "normal_finish")
    private Boolean normalFinish;

    @Schema(description = "页面名称", required = true)
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "title")
    private String title;

    @Schema(description = "表达式", required = true)
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "expression")
    private String expression;

    @Schema(description = "个性化类型", required = true)
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "type")
    private String type;

    @Schema(description = "图文样式", required = true)
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "text")
    private String text;

    @Schema(description = "跳转链接", required = true)
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "link")
    private String link;

    @Schema(description = "唯一值", required = true)
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "name")
    private String name;


}
