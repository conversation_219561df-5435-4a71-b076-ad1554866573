package cn.hanyi.survey.core.dto.lottery;

import cn.hanyi.survey.core.constant.lottery.LotteryPrizeType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2022/10/14 17:04:22
 */
@Setter
@Getter
public class SurveyPrizeWinnerParam {

    @Schema(description = "奖品名/中奖信息：奖品名、中奖人、手机号、兑换码")
    private String key;

    @Schema(description = "奖品发放状态")
    private cn.hanyi.survey.core.constant.lottery.PrizeSendStatus PrizeSendStatus;

    @Schema(description = "奖品类型")
    private LotteryPrizeType prizeType;

    @Schema(description = "抽奖活动")
    private Long lotteryId;
}
