package cn.hanyi.survey.core.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.converter.LongListConverter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Table(name = "response_shared_config")
@DtoClass(includeAllFields = true)
@EntityScopeStrategy
public class ResponseSharedConfig extends EnterpriseEntity {

    @OneToMany(mappedBy = "responseSharedConfig", cascade = {CascadeType.ALL})
    @DtoProperty(jsonView = ResourceViews.Basic.class, type = ResponseSharedDto.class)
    @JsonIgnore
    private List<ResponseShared> responseShared = new ArrayList<>();

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "s_id")
    @DtoProperty(ignore = true)
    @JsonIgnore
    private Survey survey;

    @Column(name = "enable")
    private Boolean enable;

    @Column(name = "with_token")
    private Boolean withToken;

    @Column(name = "hide_qid")
    private Boolean hideQid;

    @Convert(converter = LongListConverter.class)
    @Column(name = "qids")
    private List<Long> qids = new ArrayList<>();

    public ResponseSharedConfig(Survey survey, Boolean enable, Boolean withToken, Boolean hideQid) {
        this.survey = survey;
        this.enable = enable;
        this.withToken = withToken;
        this.hideQid = hideQid;
    }

}
