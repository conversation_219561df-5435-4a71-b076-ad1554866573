package cn.hanyi.survey.core.entity;

import cn.hanyi.survey.core.constant.QuotaChannelType;
import cn.hanyi.survey.core.constant.QuotaType;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.converter.StringListConverter;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@MappedSuperclass
public class BaseQuota extends BaseEntity {

    @Size(max = 20, message = "名称超出20长度")
    @Schema(description = "配额名称", required = true)
    @JsonView(ResourceViews.Basic.class)
    private String name = "";

    @Convert(converter = StringListConverter.class)
    @Schema(description = "question name list, 比如如果出现多问题复杂表达式", required = true)
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "q_names")
    private List<String> questionNames = new ArrayList<>();

    @Size(max = 2000, message = "SEL表达式长度超多限制")
    @Schema(description = "SEL表达式", required = true)
    @JsonView(ResourceViews.Basic.class)
    private String expression = "";

    @Size(max = 128, message = "表达式hash")
    @Schema(description = "SEL表达式hash")
    @JsonView(ResourceViews.Detail.class)
    @JoinColumn(name = "expression_hash")
    private String expressionHash = "";

    @Schema(description = "最大限额", required = true)
    @JsonView(ResourceViews.Basic.class)
    private Integer max;

    @Schema(description = "配额渠道类别,XM_PLUS:体验家配额，SURVEY_PLUS:调研家社区配额", required = true)
    @JsonView(ResourceViews.Basic.class)
    @Enumerated(value = EnumType.ORDINAL)
    private QuotaChannelType channelType = QuotaChannelType.XM_PLUS;

    @Schema(description = "配额类型", required = true)
    @JsonView(ResourceViews.Basic.class)
    @Enumerated(value = EnumType.ORDINAL)
    private QuotaType type = QuotaType.COMMON_QUOTA;

    @Size(max = 20, message = "")
    @Schema(description = "分组code", required = true)
    @JsonView(ResourceViews.Basic.class)
    private String groupCode = "";

}
