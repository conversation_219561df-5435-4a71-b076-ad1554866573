package cn.hanyi.survey.core.entity;

import cn.hanyi.survey.core.constant.survey.SurveyVerifyStatus;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Enumerated;
import javax.persistence.Table;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "survey_verify")
@DtoClass(includeAllFields = true)
public class SurveyVerify extends BaseEntity {

    @Column(name = "version")
    private Integer verifyVersion = 0;

    @Column(name = "s_id")
    private Long surveyId;

    @Column(name = "user_id")
    @JsonView(ResourceViews.Basic.class)
    private Long userId;

    @Enumerated
    @Schema(description = "操作: 驳回、通过")
    @Column(name = "status")
    private SurveyVerifyStatus status;

}
