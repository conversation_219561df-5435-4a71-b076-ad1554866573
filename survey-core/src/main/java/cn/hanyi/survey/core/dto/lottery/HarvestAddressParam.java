package cn.hanyi.survey.core.dto.lottery;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2022/10/18 14:20:02
 * 收获地址
 */
@Getter
@Setter
public class HarvestAddressParam {

    private Long id;

    @Schema(description = "收货人")
    private String name;

    @Schema(description = "手机号码")
    private String phone;

    @Schema(description = "收获地址：省")
    private String province;

    @Schema(description = "收获地址：市")
    private String city;

    @Schema(description = "收货地址：区/县")
    private String district;

    @Schema(description = "收货详细地址")
    private String address;
}
