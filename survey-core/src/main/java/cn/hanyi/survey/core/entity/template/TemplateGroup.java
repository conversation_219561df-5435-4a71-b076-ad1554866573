package cn.hanyi.survey.core.entity.template;

import cn.hanyi.survey.core.constant.Template.TemplateType;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.validation.constraints.Size;

@Entity
@Getter
@Setter
@Table(name = "template_group")
@AllArgsConstructor
@NoArgsConstructor
@DtoClass(includeAllFields = true)
@EntityScopeStrategy
public class TemplateGroup extends EnterpriseEntity {

//    @Schema(description = "组织id")
//    @Column(name = "org_id")
//    protected Long orgId;

    @Schema(description = "顺序")
    @JsonView(ResourceViews.Basic.class)
    private int sequence = 0;

    @Size(max = 200, message = "组名长度超过限制")
    @Schema(description = "组名")
    @JsonView(ResourceViews.Basic.class)
    private String title = "";

    @Schema(description = "可否编辑 0：不能 1：可以")
    @JsonView(ResourceViews.Basic.class)
    private Boolean editable = true;

    @Schema(description = "模板类型0：survey 1：question")
    @JsonView(ResourceViews.Basic.class)
    private TemplateType type = TemplateType.SURVEY;

}
