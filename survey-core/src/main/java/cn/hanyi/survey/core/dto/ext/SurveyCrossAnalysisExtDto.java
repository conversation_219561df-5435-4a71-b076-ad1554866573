package cn.hanyi.survey.core.dto.ext;

import cn.hanyi.survey.core.dto.SurveyCrossAnalysisQuestionsDto;
import cn.hanyi.survey.core.entity.SurveyCrossAnalysis;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.rest.view.ResourceViews;

import java.util.List;
import java.util.Map;

@Getter
@Setter
public class SurveyCrossAnalysisExtDto extends BaseEntityDTO<SurveyCrossAnalysis> {

    public SurveyCrossAnalysisExtDto() {
    }

    public SurveyCrossAnalysisExtDto(SurveyCrossAnalysis entity) {
        super(entity);
    }

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "行和列的描述")
    private SurveyCrossAnalysisQuestionsDto questions;

    @Schema(description = "列数据")
    @JsonView(ResourceViews.Basic.class)
    private List<String> columns;

    @Schema(description = "行数据")
    @JsonView(ResourceViews.Basic.class)
    private List<Map<String, Object>> rows;
}
