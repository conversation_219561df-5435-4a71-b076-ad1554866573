package cn.hanyi.survey.core.dto.task;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class UploadFileDto {

    @Schema(description = "客户数")
    private int count;
    @Schema(description = "文件地址")
    private String url;
    @Schema(description = "表头")
    private Map<String, String> headers;
    @Schema(description = "客户列表")
    private List<LinkedHashMap<String, Object>> customers;

    public void setCustomers(List<LinkedHashMap<String, Object>> customers) {
        this.customers = customers;
        if (CollectionUtils.isNotEmpty(customers)) {
            count = customers.size();
        }
    }

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class UploadCustomer {
        private int sort;
        private String username;
        private String mobile;
        private String email;
        private String p1;
        private String p2;
        private String p3;
    }
}
