package cn.hanyi.survey.core;

import cn.hanyi.survey.workertrigger.ISurveyEventTrigger;
import cn.hanyi.survey.workertrigger.ISurveyTaskTrigger;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration(proxyBeanMethods = false)
public class SurveyCoreAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public ISurveyEventTrigger surveyEventTrigger() {
        return new ISurveyEventTrigger() {
        };
    }

    @Bean
    @ConditionalOnMissingBean
    public ISurveyTaskTrigger surveyTaskTrigger() {
        return new ISurveyTaskTrigger() {
        };
    }
}

