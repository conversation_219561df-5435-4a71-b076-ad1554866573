package cn.hanyi.survey.core.utilis;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;


/**
 * <AUTHOR>
 */
@Slf4j
public class RegularExpressionUtils {

    static Pattern pattern = Pattern.compile("(<|\\{\\{).+?(>|\\}}\\}})", Pattern.DOTALL);
    static final Pattern PATTERN_MOBILE = Pattern.compile("^[0-9]{11}$");

    public static String replaceHtml(String html) {
        String result = (html == null) ? null : pattern.matcher(html).replaceAll("");
        return result;
    }

    public static String safeTitle(String title) {
        return (title == null) ? null : replaceHtml(StringUtils.normalizeSpace(title)).replaceAll("[+%&=/#\\\\:*?|\"<>./ +]", "").trim();
    }
}
