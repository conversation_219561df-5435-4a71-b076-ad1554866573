//package cn.hanyi.survey.core.exception;
//
//import lombok.extern.slf4j.Slf4j;
//import org.befun.core.dto.BaseResponseDto;
//import org.befun.core.exception.BadRequestException;
//import org.springframework.http.ResponseEntity;
//import org.springframework.web.bind.annotation.ControllerAdvice;
//import org.springframework.web.bind.annotation.ExceptionHandler;
//import org.springframework.web.context.request.WebRequest;
//
//import javax.validation.ConstraintViolationException;
//import javax.validation.Path;
//import java.util.Map;
//import java.util.stream.Collectors;
//
//@ControllerAdvice
//@Slf4j
//public class ValidationExceptionHandler {
//    @ExceptionHandler(ConstraintViolationException.class)
//    public ResponseEntity<BaseResponseDto> handleValidationException(ConstraintViolationException ex, WebRequest request) {
//        Map<Path, String> detail = ex.getConstraintViolations().stream().collect(Collectors.toMap(e -> e.getPropertyPath(), e -> e.getMessageTemplate()));
//        BaseResponseDto responseDto = new BaseResponseDto(new BadRequestException(ex.getMessage()));
//        responseDto.setDetail(detail.toString());
//        return ResponseEntity.badRequest().body(responseDto);
//    }
//}
