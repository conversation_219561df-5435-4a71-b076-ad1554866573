package cn.hanyi.survey.core.dto;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

import javax.validation.constraints.NotEmpty;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SurveyCrossAnalysisSimpleDto {

    @NotEmpty
    @JsonView(ResourceViews.Basic.class)
    private String name;

    @JsonView(ResourceViews.Basic.class)
    private String matrixValue;
}
