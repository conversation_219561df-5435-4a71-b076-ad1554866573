package cn.hanyi.survey.core.repository;

import cn.hanyi.survey.core.entity.SurveyVerify;
import org.befun.auth.projection.SimpleUser;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SurveyVerifyRepository extends ResourceRepository<SurveyVerify, Long> {

    SurveyVerify findBySurveyIdAndUserIdAndVerifyVersion(Long sid, Long userid, Integer version);

    List<SurveyVerify> findBySurveyIdAndVerifyVersion(Long sid, Integer version);

}
