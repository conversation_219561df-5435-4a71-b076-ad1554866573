package cn.hanyi.survey.core.repository;

import cn.hanyi.survey.core.constant.survey.SurveyVerifyStatus;
import cn.hanyi.survey.core.entity.SurveyVerifyRecord;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface SurveyVerifyRecordRepository extends ResourceRepository<SurveyVerifyRecord, Long> {

    List<SurveyVerifyRecord> findBySurveyId(Long sid, Sort sort);

    Optional<SurveyVerifyRecord> findBySurveyIdAndVerifyVersionAndOperation(Long sid, Integer version, SurveyVerifyStatus operation);
}
