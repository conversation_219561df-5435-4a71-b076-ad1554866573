package cn.hanyi.survey.core.dto.channel;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class CountSendDto {
    @Schema(description = "客户数量")
    private int countCustomer;
    @Schema(description = "单条短信字数")
    private int smsLength;
    @Schema(description = "每个客户需要消费的短信条数")
    private int oneCustomerCost;
    @Schema(description = "总共需要的短信条数")
    private int allCost;
    @Schema(description = "余额")
    private int balance;

    private static final int MAX = 70;
    private static final int LIMIT = 67;

    public CountSendDto(int customerSize, int smsLength, int balance) {
        this.countCustomer = customerSize;
        this.smsLength = smsLength;
        this.balance = balance;
        this.oneCustomerCost = oneCustomerCost(smsLength);
        this.allCost = this.countCustomer * this.oneCustomerCost;
    }

    /**
     * 小于 70 为 1 条
     * 大于 70 则每 67 个字为 1 条
     */
    public static int oneCustomerCost(int smsLength) {
        if (smsLength <= 0) {
            return 0;
        }
        if (smsLength < MAX) {
            return 1;
        }
        int cost = smsLength / LIMIT;
        if (smsLength % LIMIT > 0) {
            cost += 1;
        }
        return cost;
    }
}
