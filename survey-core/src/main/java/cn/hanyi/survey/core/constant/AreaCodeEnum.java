package cn.hanyi.survey.core.constant;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
public enum AreaCodeEnum {
	AD("安道尔", "+376"),
	AE("阿拉伯联合酋长国", "+971"),
	AF("阿富汗", "+93"),
	AG("安提瓜和巴布达", "+1268"),
	AI("安圭拉", "+1264"),
	AL("阿尔巴尼亚", "+355"),
	AM("亚美尼亚", "+374"),
	AO("安哥拉", "+244"),
	AR("阿根廷", "+54"),
	AS("美属萨摩亚","+1684"),
	AT("奥地利","+43"),
	AU("澳大利亚","+61"),
	AW("阿鲁巴","+297"),
	AZ("阿塞拜疆","+994"),
	BA("波斯尼亚和黑塞哥维那","+387"),
	BB("巴巴多斯","+1246"),
	BD("孟加拉国","+880"),
	BE("比利时","+32"),
	BF("布基纳法索","+226"),
	BG("保加利亚","+359"),
	BH("巴林","+973"),
	BI("布隆迪","+257"),
	BJ("贝宁","+229"),
	BM("百慕大群岛","+1441"),
	BN("文莱","+673"),
	BO("玻利维亚","+591"),
	BQ("荷兰加勒比","+599"),
	BR("巴西","+55"),
	BS("巴哈马","+1242"),
	BT("不丹","+975"),
	BW("博茨瓦纳","+267"),
	BY("白俄罗斯","+375"),
	BZ("伯利兹","+501"),
	CA("加拿大","+1"),
	CD("刚果民主共和国","+243"),
	CF("中非共和国","+236"),
	CG("刚果共和国","+242"),
	CH("瑞士","+41"),
	CI("象牙海岸","+225"),
	CK("库克群岛","+682"),
	CL("智利","+56"),
	CM("喀麦隆","+237"),
	CN("中国","+86"),
	CO("哥伦比亚","+57"),
	CR("哥斯达黎加","+506"),
	CU("古巴","+53"),
	CV("开普","+238"),
	CW("库拉索","+599"),
	CY("塞浦路斯","+357"),
	CZ("捷克","+420"),
	DE("德国","+49"),
	DJ("吉布提","+253"),
	DK("丹麦","+45"),
	DM("多米尼加","+1767"),
	DO("多米尼加共和国","+1809"),
	DZ("阿尔及利亚","+213"),
	EC("厄瓜多尔","+593"),
	EE("爱沙尼亚","+372"),
	EG("埃及","+20"),
	ER("厄立特里亚","+291"),
	ES("西班牙","+34"),
	ET("埃塞俄比亚","+251"),
	FI("芬兰","+358"),
	FJ("斐济","+679"),
	FM("密克罗尼西亚","+691"),
	FO("法罗群岛","+298"),
	FR("法国","+33"),
	GA("加蓬","+241"),
	GB("英国","+44"),
	GD("格林纳达","+1473"),
	GE("格鲁吉亚","+995"),
	GF("法属圭亚那","+594"),
	GH("加纳","+233"),
	GI("直布罗陀","+350"),
	GL("格陵兰岛","+299"),
	GM("冈比亚","+220"),
	GN("几内亚","+224"),
	GP("瓜德罗普岛","+590"),
	GQ("赤道几内亚","+240"),
	GR("希腊","+30"),
	GT("瓜地马拉","+502"),
	GU("关岛","+1671"),
	GW("几内亚比绍共和国","+245"),
	GY("圭亚那","+592"),
	HK("中国香港","+852"),
	HN("洪都拉斯","+504"),
	HR("克罗地亚","+385"),
	HT("海地","+509"),
	HU("匈牙利","+36"),
	ID("印度尼西亚","+62"),
	IE("爱尔兰","+353"),
	IL("以色列","+972"),
	IN("印度","+91"),
	IQ("伊拉克","+964"),
	IR("伊朗","+98"),
	IS("冰岛","+354"),
	IT("意大利","+39"),
	JM("牙买加","+1876"),
	JO("约旦","+962"),
	JP("日本","+81"),
	KE("肯尼亚","+254"),
	KG("吉尔吉斯斯坦","+996"),
	KH("柬埔寨","+855"),
	KI("基里巴斯","+686"),
	KM("科摩罗","+269"),
	KN("圣基茨和尼维斯","+1869"),
	KP("朝鲜","+850"),
	KR("韩国","+82"),
	KW("科威特","+965"),
	KY("开曼群岛","+1345"),
	KZ("哈萨克斯坦","+7"),
	LA("老挝","+856"),
	LB("黎巴嫩","+961"),
	LC("圣露西亚","+1758"),
	LI("列支敦士登","+423"),
	LK("斯里兰卡","+94"),
	LR("利比里亚","+231"),
	LS("莱索托","+266"),
	LT("立陶宛","+370"),
	LU("卢森堡","+352"),
	LV("拉脱维亚","+371"),
	LY("利比亚","+218"),
	MA("摩洛哥","+212"),
	MC("摩纳哥","+377"),
	MD("摩尔多瓦","+373"),
	ME("黑山","+382"),
	MG("马达加斯加","+261"),
	MH("马绍尔群岛","+692"),
	MK("马其顿","+389"),
	ML("马里","+223"),
	MM("缅甸","+95"),
	MN("蒙古","+976"),
	MO("中国澳门","+853"),
	MR("毛里塔尼亚","+222"),
	MS("蒙特塞拉特岛","+1664"),
	MT("马耳他","+356"),
	MU("毛里求斯","+230"),
	MV("马尔代夫","+960"),
	MW("马拉维","+265"),
	MX("墨西哥","+52"),
	MY("马来西亚","+60"),
	MZ("莫桑比克","+258"),
	NA("纳米比亚","+264"),
	NC("新喀里多尼亚","+687"),
	NE("尼日尔","+227"),
	NG("尼日利亚","+234"),
	NI("尼加拉瓜","+505"),
	NL("荷兰","+31"),
	NO("挪威","+47"),
	NP("尼泊尔","+977"),
	NR("拿鲁岛","+674"),
	NZ("新西兰","+64"),
	OM("阿曼","+968"),
	PA("巴拿马","+507"),
	PE("秘鲁","+51"),
	PF("法属波利尼西亚","+689"),
	PG("巴布亚新几内亚","+675"),
	PH("菲律宾","+63"),
	PK("巴基斯坦","+92"),
	PL("波兰","+48"),
	PM("圣彼埃尔和密克隆岛","+508"),
	PR("波多黎各","+1787"),
	PT("葡萄牙","+351"),
	PW("帕劳","+680"),
	PY("巴拉圭","+595"),
	QA("卡塔尔", "+974"),
	RE("留尼汪", "+262"),
	RO("罗马尼亚", "+40"),
	RS("塞尔维亚", "+381"),
	RU("俄罗斯", "+7"),
	RW("卢旺达", "+250"),
	SA("沙特阿拉伯", "+966"),
	SB("所罗门群岛", "+677"),
	SC("塞舌尔", "+248"),
	SD("苏丹", "+249"),
	SE("瑞典", "+46"),
	SG("新加坡", "+65"),
	SI("斯洛文尼亚", "+386"),
	SK("斯洛伐克", "+421"),
	SL("塞拉利昂", "+232"),
	SM("圣马力诺", "+378"),
	SN("塞内加尔", "+221"),
	SO("索马里", "+252"),
	SR("苏里南", "+597"),
	ST("圣多美和普林西比", "+239"),
	SV("萨尔瓦多", "+503"),
	SY("叙利亚", "+963"),
	SZ("斯威士兰", "+268"),
	TC("特克斯和凯科斯群岛", "+1649"),
	TD("乍得", "+235"),
	TG("多哥", "+228"),
	TH("泰国", "+66"),
	TJ("塔吉克斯坦", "+992"),
	TL("东帝汶", "+670"),
	TM("土库曼斯坦", "+993"),
	TN("突尼斯", "+216"),
	TO("汤加", "+676"),
	TR("土耳其", "+90"),
	TT("特立尼达和多巴哥", "+1868"),
	TW("中国台湾", "+886"),
	TZ("坦桑尼亚", "+255"),
	UA("乌克兰", "+380"),
	UG("乌干达", "+256"),
	US("美国", "+1"),
	UY("乌拉圭", "+598"),
	UZ("乌兹别克斯坦", "+998"),
	VC("圣文森特和格林纳丁斯", "+1784"),
	VE("委内瑞拉", "+58"),
	VG("英属处女群岛", "+1284"),
	VN("越南", "+84"),
	VU("瓦努阿图", "+678"),
	WS("萨摩亚", "+685"),
	YE("也门", "+967"),
	YT("马约特", "+269"),
	ZA("南非", "+27"),
	ZM("赞比亚", "+260"),
	ZW("津巴布韦", "+263");

	private String name;
	private String code;

	AreaCodeEnum(String name, String code) {
		this.name = name;
		this.code = code;
	}

	public static AreaCodeEnum getByName(String name) {
		if (StringUtils.isEmpty(name)) {
			return null;
		}
		AreaCodeEnum[] values = AreaCodeEnum.values();
		for (AreaCodeEnum value : values) {
			if (value.name.equals(name)) {
				return value;
			}
		}
		return null;
	}

}
