//package cn.hanyi.survey.core.dto.channel;
//
//import lombok.AllArgsConstructor;
//import lombok.Getter;
//import lombok.NoArgsConstructor;
//import lombok.Setter;
//import org.apache.commons.lang3.StringUtils;
//import org.befun.core.template.TemplateEngine;
//
//import java.util.Map;
//
//@Getter
//@Setter
//@AllArgsConstructor
//@NoArgsConstructor
//public class TemplateInfoDto {
//
//    private String smsTemplateId;
//    private String smsSignId;
//    private int smsLength;
//
//    private String weChatAppId;
//    private String weChatTemplateId;
//
//    private Long connectorId;
//    private String example;
//
//    public static TemplateInfoDto createSms(Long connectorId, String smsTemplateId, String smsSignId, String example) {
//        int smsLength = 0;
//        if (StringUtils.isNotEmpty(example)) {
//            String template = TemplateEngine.renderTextTemplate(example, Map.of("customer", Map.of("username", "xxxx"), "url", Map.of("code", "xxxxxx")));
//            smsLength = template.length();
//        }
//        return new TemplateInfoDto(smsTemplateId, smsSignId, smsLength, null, null, connectorId, example);
//    }
//
//    public static TemplateInfoDto createWeChat(Long connectorId, String weChatAppId, String weChatTemplateId, String example) {
//        return new TemplateInfoDto(null, null, 0, weChatAppId, weChatTemplateId, connectorId, example);
//    }
//
//}
