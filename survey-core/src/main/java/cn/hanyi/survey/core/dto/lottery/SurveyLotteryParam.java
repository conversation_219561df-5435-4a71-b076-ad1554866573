package cn.hanyi.survey.core.dto.lottery;

import cn.hanyi.survey.core.constant.lottery.LotteryType;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/9/26 19:19:43
 */
@Setter
@Getter
public class SurveyLotteryParam {

    private LotteryType type;

    @NotNull
    @Min(0)
    private Long responseId;

    private Long surveyId;

    private String redirectUrl;


}












