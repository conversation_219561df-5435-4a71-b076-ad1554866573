package cn.hanyi.survey.core.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2022/9/22 18:24:16
 */
@Setter
@Getter
@Entity
@Table(name = "survey_lottery_prize_code")
@DtoClass(includeAllFields = true)
public class SurveyLotteryPrizeCode extends BaseEntity {

    @Schema(description = "奖品id")
/*    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "prize_id")
    @DtoProperty(ignore = true)*/
    private Long prizeId;

    @Schema(description = "兑换码")
    private String code;

    @Schema(description = "是否被抽中")
    @Column(name = "is_selected", columnDefinition = "bit(1) default 0")
    private Boolean isSelected = false;

    @Schema(description = "是否兑换")
    @Column(name = "is_used", columnDefinition = "bit(1) default 0")
    private Boolean isUsed = false;

}












