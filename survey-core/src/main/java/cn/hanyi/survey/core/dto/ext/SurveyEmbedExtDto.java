package cn.hanyi.survey.core.dto.ext;

import cn.hanyi.survey.core.constant.channel.ChannelStatus;
import cn.hanyi.survey.core.constant.channel.EmbedListType;
import cn.hanyi.survey.core.entity.SurveyEmbed;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

import java.util.ArrayList;
import java.util.List;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/6/29 17:07
 */
@Getter
@Setter
public class SurveyEmbedExtDto extends SurveyEmbed {

    @Schema(description = "谁能看到？string: ALL全部 PART部分")
    @JsonView(ResourceViews.Basic.class)
    private String embedShow = "ALL";

    @Schema(description = "显示概率")
    @JsonView(ResourceViews.Basic.class)
    private Integer embedPercent = 100;

    @Schema(description = "启(禁)用白名单")
    @JsonView(ResourceViews.Basic.class)
    private Boolean embedWhiteList = false;

    @Schema(description = "启(禁)用黑名单 本来用embedWhiteList就可以做总开关，然后通过embedListType就可以区分是使用黑名单还是白名单的  不知道为啥前端还要添加个字段")
    @JsonView(ResourceViews.Basic.class)
    private Boolean embedBlackList = false;

    @Schema(description = "黑/白名单类型")
    @JsonView(ResourceViews.Basic.class)
    private EmbedListType embedListType = EmbedListType.WHITELIST;

    @Schema(description = "白名单用户id")
    @JsonView(ResourceViews.Basic.class)
    private List<Object> embedUserId = new ArrayList<>();

    @Schema(description = "按时间或频次触发 string: TIME时间 RATE频次")
    @JsonView(ResourceViews.Basic.class)
    private String embedShowType = "TIME";

    @Schema(description = "显示(触发)次数")
    @JsonView(ResourceViews.Basic.class)
    private Integer embedCount = 1;

    @Schema(description = "问卷是否填答")
    @JsonView(ResourceViews.Basic.class)
    private Boolean isComplete = false;

    @Schema(description = "按钮左LEFT 中CENTER 右RIGHT")
    @JsonView(ResourceViews.Basic.class)
    private String btnJustifyContent = "RIGHT";

    @Schema(description = "按钮上TOP 中CENTER 下BOTTOM")
    @JsonView(ResourceViews.Basic.class)
    private String btnAlignItems = "BOTTOM";

    @Schema(description = "左、右边距")
    @JsonView(ResourceViews.Basic.class)
    private Integer btnPaddingWidth = 0;

    @Schema(description = "上、下边距")
    @JsonView(ResourceViews.Basic.class)
    private Integer btnPaddingHeight = 20;

    @Schema(description = "按钮样式 CIRCLE圆形 SQUARE贴边")
    @JsonView(ResourceViews.Basic.class)
    private String btnType = "CIRCLE";

    @Schema(description = "圆形按钮背景颜色")
    @JsonView(ResourceViews.Basic.class)
    private String btnCircleColor = "#FFFFFF";

    @Schema(description = "贴边按钮背景颜色")
    @JsonView(ResourceViews.Basic.class)
    private String btnSquareColor = "#FFFFFF";

    @Schema(description = "圆形按钮图标")
    @JsonView(ResourceViews.Basic.class)
    private String btnCircleImage = "https://assets.surveyplus.cn/surveylite/static/icon-hy-questionnaire.svg";

    @Schema(description = "贴边按钮图标")
    @JsonView(ResourceViews.Basic.class)
    private String btnSquareImage = "https://assets.surveyplus.cn/surveylite/static/icon-hy-questionnaire.svg";

    @Schema(description = "按钮文案")
    @JsonView(ResourceViews.Basic.class)
    private String btnText = "问卷反馈";

    @Schema(description = "按钮字体颜色")
    @JsonView(ResourceViews.Basic.class)
    private String btnFontColor = "#333333";

    @Schema(description = "圆形左、右边距")
    @JsonView(ResourceViews.Basic.class)
    private Integer btnCircleWidth = 20;

    @Schema(description = "圆形上、下边距")
    @JsonView(ResourceViews.Basic.class)
    private Integer btnCircleHeight = 20;

    @Schema(description = "关闭图标颜色")
    @JsonView(ResourceViews.Basic.class)
    private String closeBtnColor = "#556976";

    @Schema(description = "小程序二维码")
    @JsonView(ResourceViews.Basic.class)
    private String qrCode = "";

    @Schema(description = "渠道状态")
    @JsonView(ResourceViews.Basic.class)
    private ChannelStatus channelStatus;

    @Schema(description = "按企业ID")
    @JsonView(ResourceViews.Basic.class)
    private List embedCompanyId = new ArrayList<>();

    @Schema(description = "免打扰")
    @JsonView(ResourceViews.Basic.class)
    private Boolean doNotDisturb = false;

    @Schema(description = "上下边距")
    @JsonView(ResourceViews.Basic.class)
    private String appPaddingHeight = "0px";

    @Schema(description = "左右边距")
    @JsonView(ResourceViews.Basic.class)
    private String appPaddingWidth = "0px";

    @Schema(description = "边框圆角")
    @JsonView(ResourceViews.Basic.class)
    private String appBorderRadius = "4px";

    @Schema(description = "背景色透明度")
    @JsonView(ResourceViews.Basic.class)
    private Integer appBgOpacity = 45;

    @Schema(description = "弹窗背景色")
    @JsonView(ResourceViews.Basic.class)
    private String appBackGroundColor = "#000000";

    @Schema(description = "按钮图标大小")
    @JsonView(ResourceViews.Basic.class)
    private String btnImageSize = "DEFAULT";

    @Schema(description = "按钮模式下问卷的宽度")
    @JsonView(ResourceViews.Basic.class)
    private Integer btnModeEmbedPcWidth = 450;

    @Schema(description = "按钮模式下问卷的高度模式")
    @JsonView(ResourceViews.Basic.class)
    private String btnModeEmbedHeightMode = "AUTO";

    @Schema(description = "按钮模式下问卷的高度")
    @JsonView(ResourceViews.Basic.class)
    private String btnModeEmbedHeight = "50%";

    @Schema(description = "按钮模式下问卷的边距宽度")
    @JsonView(ResourceViews.Basic.class)
    private String btnModeEmbedPaddingWidth = "0px";

    @Schema(description = "问卷位置")
    @JsonView(ResourceViews.Basic.class)
    private String embedLocationMode = "DEFAULT";

    @Schema(description = "距离首次访问超过")
    @JsonView(ResourceViews.Basic.class)
    private Integer firstGapTime = 0;

    @Schema(description = "距离首次访问超过 string: DAY天 RATE次")
    @JsonView(ResourceViews.Basic.class)
    private String firstGapType;

    @Schema(description = "自定义条件表达式")
    @JsonView(ResourceViews.Basic.class)
    private String conditionalExpression = "";

    @Schema(description = "用户免打扰选择开关")
    @JsonView(ResourceViews.Basic.class)
    private Boolean doNotDisturbSwitch = false;

    @Schema(description = "几天内不再显示，默认7")
    @JsonView(ResourceViews.Basic.class)
    private Integer doNotDisturbTime = 7;
}
