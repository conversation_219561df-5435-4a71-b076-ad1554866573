package cn.hanyi.survey.core.repository;

import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyTag;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SurveyTagRepository extends ResourceRepository<SurveyTag, Long> {

    List<SurveyTag> findAllByIdIn(Iterable<Long> ids);

    SurveyTag findOneById(Long id);

    Page<SurveyTag> findAllBySurvey(Survey survey, Pageable pageable);

    List<SurveyTag> findAllBySurvey(Survey survey);

}
