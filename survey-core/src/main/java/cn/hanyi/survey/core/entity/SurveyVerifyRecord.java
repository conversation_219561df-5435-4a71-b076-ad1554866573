package cn.hanyi.survey.core.entity;

import cn.hanyi.survey.core.constant.survey.SurveyVerifyStatus;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Enumerated;
import javax.persistence.Table;

@Entity
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "survey_verify_record")
@DtoClass(includeAllFields = true)
public class SurveyVerifyRecord extends BaseEntity {

    @Column(name = "s_id")
    private Long surveyId;

    @Column(name = "user_id")
    @JsonView(ResourceViews.Basic.class)
    private Long userId;

    @Enumerated
    @Schema(description = "操作: 提交、驳回、撤销、通过")
    @JsonView(ResourceViews.Basic.class)
    private SurveyVerifyStatus operation;

    @Schema(description = "问卷审核原因")
    @JsonView(ResourceViews.Basic.class)
    private String comment;

    @Schema(description = "提交版本")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "version")
    private Integer verifyVersion = 0;
}
