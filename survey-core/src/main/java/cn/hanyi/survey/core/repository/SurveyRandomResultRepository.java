package cn.hanyi.survey.core.repository;

import cn.hanyi.survey.core.constant.question.QuestionRandomType;
import cn.hanyi.survey.core.entity.SurveyRandomResult;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository
public interface SurveyRandomResultRepository extends ResourceRepository<SurveyRandomResult, Long> {

    List<SurveyRandomResult> findBySurveyId(Long sid);

    @Query(nativeQuery = true, value = "select 1 from survey_random_result where survey_id=?1 limit 1")
    Long existsRandomResult(Long surveyId);

    boolean existsBySurveyIdAndType(Long surveyId, QuestionRandomType type);

    List<SurveyRandomResult> findBySurveyIdAndResponseIdBetween(Long surveyId, Long responseIdStart, Long responseIdEnd);

    List<SurveyRandomResult> findBySurveyIdAndResponseIdIn(Long surveyId, Collection<Long> responseIds);
    
    boolean existsBySurveyIdAndResponseId(Long surveyId, Long responseId);
}
