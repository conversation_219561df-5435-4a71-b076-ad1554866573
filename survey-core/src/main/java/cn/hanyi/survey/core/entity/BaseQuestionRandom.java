package cn.hanyi.survey.core.entity;

import cn.hanyi.survey.core.constant.question.QuestionRandomSortType;
import cn.hanyi.survey.core.constant.question.QuestionRandomType;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.entity.BaseEntity;
import org.befun.core.hibernate.types.JsonColumn;
import org.befun.core.rest.view.ResourceViews;
import org.hibernate.annotations.Type;

import javax.persistence.Column;
import javax.persistence.Enumerated;
import javax.persistence.MappedSuperclass;
import java.util.ArrayList;
import java.util.List;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/4/18 14:59
 */
@Getter
@Setter
@MappedSuperclass
public class BaseQuestionRandom extends BaseEntity {

    @Schema(description = "随机显示的问题个数", required = true)
    @JsonView(ResourceViews.Detail.class)
    private Integer num = 0;

    @Column(name = "q_names")
    @JsonView(ResourceViews.Detail.class)
    @Type(type = JsonColumn.TYPE)
    private List<String> questionNames = new ArrayList<>();

    @Column(name = "type")
    @Enumerated
    @JsonView(ResourceViews.Detail.class)
    @Schema(description = "随机类型  题组 题目")
    private QuestionRandomType type;

    @Column(name = "sort_type")
    @Enumerated
    @JsonView(ResourceViews.Detail.class)
    @Schema(description = "排序类型：默认，随机")
    private QuestionRandomSortType sortType;
}
