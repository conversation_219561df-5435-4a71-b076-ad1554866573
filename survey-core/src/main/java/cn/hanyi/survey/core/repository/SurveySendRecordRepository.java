package cn.hanyi.survey.core.repository;

import cn.hanyi.survey.core.constant.channel.ChannelSendStatus;
import cn.hanyi.survey.core.constant.ReplyStatus;
import cn.hanyi.survey.core.constant.survey.SendStatus;
import cn.hanyi.survey.core.entity.SurveySendRecord;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;


@Repository
public interface SurveySendRecordRepository extends ResourceRepository<SurveySendRecord, Long> {

    Optional<SurveySendRecord> findOneByClientIdAndReplyStatusNotAndStatus(String clientId, ReplyStatus replyStatus, ChannelSendStatus status);

    Optional<SurveySendRecord> findOneByClientIdAndChannelId(String clientId, Long channelId);

    Optional<SurveySendRecord> findByIdAndChannelId(Long id, Long channelId);

    Optional<SurveySendRecord> findByClientIdAndChannelId(String clientId, Long channelId);

    void deleteByIdIn(Iterable<Long> ids);

    Optional<List<SurveySendRecord>> findByChannelIdAndStatus(Long channelId, ChannelSendStatus status);

    List<SurveySendRecord> findBySurveyIdAndChannelIdAndStatus(Long surveyId, Long channelId, ChannelSendStatus status, Pageable pageable);

    long countBySurveyIdAndChannelIdAndStatus(Long surveyId, Long channelId, ChannelSendStatus status);

    long countByChannelIdAndSendStatus(Long channelId, SendStatus status);
}
