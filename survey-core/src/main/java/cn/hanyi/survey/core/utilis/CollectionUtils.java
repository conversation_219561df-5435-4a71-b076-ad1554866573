package cn.hanyi.survey.core.utilis;

import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.Map;
import java.util.Objects;


/**
 * <AUTHOR>
 */
@Slf4j
public class CollectionUtils {

    public static boolean isCollection(Class<?> c) {
        return Collection.class.isAssignableFrom(c)
                || Map.class.isAssignableFrom(c);
    }

    public static int size(Object obj) {
        if (obj == null) {
            return 0;
        } else if (Collection.class.isAssignableFrom(obj.getClass())) {
            return ((Collection<?>) obj).size();
        } else if (Map.class.isAssignableFrom(obj.getClass())) {
            return ((Map<?, ?>) obj).size();
        } else {
            return Objects.toString(obj).length();
        }
    }
}
