package cn.hanyi.survey.core.utilis;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.handler.WriteHandler;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/29 10:53:03
 */
public class EasyExcelUtils {

    /**
     * excel
     *
     * @param headers
     * @param dataList
     * @param handler
     * @return
     * @throws IOException
     */
    public static ByteArrayOutputStream easyExcel(List headers, List<List> dataList, WriteHandler handler) throws IOException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();

        List<String> header = headers;

        // 获取标题信息
        List<List<String>> headTitleInfo = WriteExcelUtils.excelTitle(header);
        // 获取数据信息
        List<List<Object>> excelDataInfo = WriteExcelUtils.excelData(dataList);

        // 获取合并单元格信息
        ExcelWriterBuilder excelWriterBuilder = EasyExcel.write(byteArrayOutputStream).useDefaultStyle(false);
        if (handler != null) {
            excelWriterBuilder.registerWriteHandler(handler);
        }
        excelWriterBuilder.head(headTitleInfo).sheet("导出")
                .doWrite(excelDataInfo);

        byteArrayOutputStream.flush();
        byteArrayOutputStream.close();
        return byteArrayOutputStream;
    }

    /**
     * csv
     *
     * @param headers
     * @param dataList
     * @param handler
     * @return
     * @throws IOException
     */
    public static ByteArrayOutputStream easyCsv(List headers, List<List> dataList, WriteHandler handler) throws IOException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        // 写入bom, 防止中文乱码
        byte[] bytes = {(byte) 0xEF, (byte) 0xBB, (byte) 0xBF};
        byteArrayOutputStream.write(bytes);

        List<String> header = headers;

        // 获取标题信息
        List<List<String>> headTitleInfo = WriteExcelUtils.excelTitle(header);
        // 获取数据信息
        List<List<Object>> excelDataInfo = WriteExcelUtils.excelData(dataList);

        ExcelWriterBuilder excelWriterBuilder = EasyExcel.write(byteArrayOutputStream).useDefaultStyle(false);
        if (handler != null) {
            excelWriterBuilder.registerWriteHandler(handler);
        }

        excelWriterBuilder.excelType(ExcelTypeEnum.CSV).head(headTitleInfo).sheet("导出").doWrite(excelDataInfo);

        byteArrayOutputStream.flush();
        byteArrayOutputStream.close();
        return byteArrayOutputStream;
    }
}
