package cn.hanyi.survey.core.repository;


import cn.hanyi.survey.core.entity.SurveyLottery;
import cn.hanyi.survey.core.entity.SurveyLotteryPrize;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/21 10:30:15
 */
@Repository
public interface SurveyLotteryPrizeRepository extends ResourceRepository<SurveyLotteryPrize, Long> {

    List<SurveyLotteryPrize> findByLottery(SurveyLottery surveyLottery, Sort sort);

    List<SurveyLotteryPrize> findAllByLotteryIn(List<SurveyLottery> lotteries);

    List<SurveyLotteryPrize> findByLotteryId(Long lotteryId);

    /**
     * 原子性更新奖品中奖统计信息
     *
     * @param prizeId 奖品ID
     * @param amount  中奖金额
     * @return 更新的行数
     */
    @Modifying
    @Query("UPDATE SurveyLotteryPrize p SET p.winnerNum = p.winnerNum + 1, p.winnerMoney = p.winnerMoney + :amount WHERE p.id = :prizeId")
    int updateWinnerStats(@Param("prizeId") Long prizeId, @Param("amount") Integer amount);

}
