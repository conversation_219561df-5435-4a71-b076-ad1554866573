package cn.hanyi.survey.core.dto.ext;

import cn.hanyi.survey.core.entity.SurveyGroup;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.rest.view.ResourceViews;


@Getter
@Setter
public class SurveyQuestionExtDto extends BaseEntityDTO<SurveyQuestion> {

    public SurveyQuestionExtDto() {
    }

    public SurveyQuestionExtDto(SurveyQuestion entity) {
        super(entity);
    }

    @Schema(description = "是否打开选项分值")
    @JsonView(ResourceViews.Basic.class)
    private Boolean isScore;

    @Schema(description = "题组code值")
    @JsonView(ResourceViews.Basic.class)
    private String groupCode;
}
