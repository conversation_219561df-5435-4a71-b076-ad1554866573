package cn.hanyi.survey.core.dto.channel;

import cn.hanyi.survey.core.constant.channel.ChannelResendCondition;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class OpenApiCreateWechatChannelResendRuleDto {

    @Schema(description = "重发时间（yyyy-MM-dd HH:mm:ss）,小于当前时间会忽略")
    private String resendTime;

    @Schema(description = "重发条件，同一组条件是或的关系，不同组条件是且的关系，默认：UNCOMPLETE 未完成")
    private List<ChannelResendCondition> conditions = List.of(ChannelResendCondition.UN_COMPLETE);


}
