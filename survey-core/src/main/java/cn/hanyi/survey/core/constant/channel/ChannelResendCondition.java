package cn.hanyi.survey.core.constant.channel;

import cn.hanyi.survey.core.constant.ReplyStatus;
import cn.hanyi.survey.core.constant.survey.ReceiveStatus;
import cn.hanyi.survey.core.constant.survey.SendStatus;
import lombok.Getter;

@Getter
public enum ChannelResendCondition {
    UN_COMPLETE("未完成", "status", ChannelSendStatus.class),
    COMPLETE("已完成", "status", ChannelSendStatus.class),

    UN_SEND("未发送", "sendStatus", SendStatus.class),
    SEND_SUCCESS("发送成功", "sendStatus", SendStatus.class),
    SEND_FAIL("发送失败", "sendStatus", SendStatus.class),

    UNKNOWN("待接收", "receiveStatus", ReceiveStatus.class),
    SUCCESS("接受成功", "receiveStatus", ReceiveStatus.class),
    FAIL("接受失败", "receiveStatus", ReceiveStatus.class),

    UN_VISIT("未访问", "replyStatus", ReplyStatus.class),
    UN_SUBMIT("未提交", "replyStatus", ReplyStatus.class),
    SUBMIT("已提交", "replyStatus", ReplyStatus.class),
    ;

    private final String text;
    private final String propertyName;
    private final Class<? extends Enum<?>> statusType;

    ChannelResendCondition(String text, String propertyName, Class<? extends Enum<?>> statusType) {
        this.text = text;
        this.propertyName = propertyName;
        this.statusType = statusType;
    }
}
