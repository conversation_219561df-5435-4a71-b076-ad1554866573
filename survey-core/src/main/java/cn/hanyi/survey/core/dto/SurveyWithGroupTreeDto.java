package cn.hanyi.survey.core.dto;

import cn.hanyi.survey.core.projection.SimpleSurveyWithGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/11 14:03:49
 */
@Getter
@Setter
public class SurveyWithGroupTreeDto {

    @Schema(description = "问卷或文件夹id")
    private Long id;
    @Schema(description = "问卷或文件夹标题")
    private String title;
    @Schema(description = "项目类型：1 问卷 2 目录")
    private int itemType = 1;
    @Schema(description = "文件夹下的问卷")
    private List<SimpleSurveyWithGroup> surveyList = new ArrayList<>();
}
