package cn.hanyi.survey.core.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2022/11/16 09:53:19
 */
@Entity
@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "survey_lottery_redpack_send_record")
@DtoClass(includeAllFields = true)
public class SurveyLotteryRedPackSendRecord extends BaseEntity {

    @Schema(description = "中奖id")
    @Column(name = "winner_id")
    private Long winnerId;

    @Schema(description = "请求数据")
    @Column(name = "request_data")
    private String requestData;

    @Schema(description = "是否请求成功")
    @Column(name = "is_success", columnDefinition = "bit(1) default 0")
    private Boolean isSuccess;

    @Schema(description = "错误信息")
    @Column(name = "error_info")
    private String errorInfo;

}