package cn.hanyi.survey.core.entity.template;

import cn.hanyi.survey.core.dto.ext.TemplateSurveyQuestionExtDto;
import cn.hanyi.survey.core.entity.BaseQuestion;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.rest.view.ResourceViews;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "template_survey_question")
@EntityScopeStrategy
@DtoClass(includeAllFields = true, superClass = TemplateSurveyQuestionExtDto.class)
public class TemplateSurveyQuestion extends BaseQuestion {

    @Schema(description = "组织id")
    @Column(name = "org_id")
    protected Long orgId;

    @Schema(description = "用户id")
    @Column(name = "user_id")
    protected Long userId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "s_id")
    @NotFound(action = NotFoundAction.IGNORE)
    @DtoProperty(ignore = true)
    private TemplateSurvey survey;

    @JsonView(ResourceViews.Basic.class)
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "group_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @NotFound(action = NotFoundAction.IGNORE)
    private TemplateGroup group;

    @DtoProperty(jsonView = ResourceViews.Basic.class, description = "可否编辑 0：不能 1：可以")
    private Boolean editable = true;

    @OneToMany(mappedBy = "question", cascade = {CascadeType.ALL})
    @OrderBy("sequence")
    @DtoProperty(jsonView = ResourceViews.Basic.class, type = TemplateSurveyQuestionItemDto.class)
    private List<TemplateSurveyQuestionItem> items = new ArrayList<>();

    @OneToMany(mappedBy = "question", cascade = {CascadeType.ALL})
    @OrderBy("sequence")
    @DtoProperty(jsonView = ResourceViews.Basic.class, type = TemplateSurveyQuestionColumnDto.class)
    private List<TemplateSurveyQuestionColumn> columns = new ArrayList<>();

}
