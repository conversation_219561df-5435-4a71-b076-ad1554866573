package cn.hanyi.survey.core.dto.channel;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Getter
@Setter
public class ChannelCountCustomerDto extends ChannelSelectCustomerDto {

    @Schema(description = "是否计算短信的数量")
    private boolean calcCostSms;

    @Schema(description = "模板id(短信，邮件)")
    private Long thirdPartTemplateId;

    @Schema(description = "模板id(微信)")
    private Long wechatTemplateId;

    @Schema(description = "模板内容：如果是微信渠道，则包含微信模板消息的参数；如果是短信渠道，可以包含content(短信内容，替换模板的默认内容)；如果是邮件渠道，必须包含sender(发送人)，可以包含title(邮件标题，替换模板的默认内容)，可以包含content(邮件内容，替换模板的默认内容)")
    private Map<String, Object> content;
}
