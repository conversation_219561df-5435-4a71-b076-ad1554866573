package cn.hanyi.survey.core.entity;


import cn.hanyi.survey.core.constant.survey.LogicType;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.converter.StringListConverter;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Enumerated;
import javax.persistence.MappedSuperclass;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@MappedSuperclass
public class BaseLogic extends BaseEntity {

    @Convert(converter = StringListConverter.class)
    @Schema(description = "question name list, 比如如果出现多问题复杂表达式", required = true)
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "q_names")
    private List<String> questionNames = new ArrayList<>();

    @Size(max = 2000, message = "SEL表达式长度超多限制")
    @Schema(description = "SEL表达式", required = true)
    @JsonView(ResourceViews.Basic.class)
    private String expression = "";

    @Size(max = 100, message = "target长度超多限制")
    @Schema(description = "目标 question name, 比如跳转", example = "q1")
    @JsonView(ResourceViews.Basic.class)
    private String target;

    @Enumerated
    @Schema(description = "逻辑动作类型", required = true, example = "GOTO")
    @JsonView(ResourceViews.Basic.class)
    private LogicType type;
}
