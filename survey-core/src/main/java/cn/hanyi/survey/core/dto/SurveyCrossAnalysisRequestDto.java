package cn.hanyi.survey.core.dto;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SurveyCrossAnalysisRequestDto {

    @JsonView(ResourceViews.Basic.class)
    private List<SurveyCrossAnalysisSimpleDto> selectRows;

    @JsonView(ResourceViews.Basic.class)
    private List<SurveyCrossAnalysisSimpleDto> selectColumns;
}
