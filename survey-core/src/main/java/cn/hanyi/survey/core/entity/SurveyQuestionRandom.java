package cn.hanyi.survey.core.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.RootAware;

import javax.persistence.*;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/4/18 14:59
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@Table(name = "survey_question_random")
@DtoClass(includeAllFields = true)
public class SurveyQuestionRandom extends BaseQuestionRandom implements RootAware<Survey> {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "s_id")
    @DtoProperty(ignore = true)
    @JsonIgnore
    private Survey survey;

    @Override
    @JsonIgnore
    public Survey getRoot() {
        return survey;
    }
}
