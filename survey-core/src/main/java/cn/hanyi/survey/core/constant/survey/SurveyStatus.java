package cn.hanyi.survey.core.constant.survey;

import cn.hanyi.survey.core.constant.error.SurveyErrorCode;
import cn.hanyi.survey.core.exception.SurveyErrorException;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 *         0 -
 */

@Getter
public enum SurveyStatus {
    STOPPED("停用"),
    COLLECTING("启用"),
    AUDITING("审核中"),
    APPROVAL("已通过"),
    REJECTED("已驳回"),
    CONTENT_AUDITING("内容审核中"),
    CONTENT_APPROVAL("内容审核已通过"),
    CONTENT_REJECTED("内容审核已驳回"),
    DISABLED("已禁用"),
    ;

    private final String text;

    SurveyStatus(String text) {
        this.text = text;
    }

    public static List<SurveyStatus> previousStatus(SurveyStatus status) {
        if (status == null) {
            status = STOPPED;
        }
        return switch (status) {
            case STOPPED -> List.of(COLLECTING, AUDITING, APPROVAL, REJECTED, CONTENT_APPROVAL, CONTENT_REJECTED);
            case COLLECTING -> List.of(STOPPED, AUDITING, APPROVAL, REJECTED, CONTENT_APPROVAL, CONTENT_REJECTED); // AUDITING REJECTED 如果已经关闭了问卷审核，也可以发布问卷
            case AUDITING -> List.of(STOPPED, REJECTED, CONTENT_APPROVAL, CONTENT_REJECTED);
            case APPROVAL, REJECTED -> List.of(AUDITING);
            case CONTENT_AUDITING -> List.of(STOPPED, AUDITING, APPROVAL, REJECTED, CONTENT_APPROVAL, CONTENT_REJECTED); // AUDITING REJECTED 如果已经关闭了问卷审核，也可以发布问卷
            case CONTENT_APPROVAL, CONTENT_REJECTED -> List.of(CONTENT_AUDITING);
            case DISABLED -> List.of(STOPPED, COLLECTING, AUDITING, APPROVAL, REJECTED, CONTENT_AUDITING, CONTENT_APPROVAL, CONTENT_REJECTED);
        };
    }

    public static SurveyErrorCode toStatusError(SurveyStatus status) {
        SurveyErrorCode errorCode = null;
        switch (status) {
            case STOPPED -> errorCode = SurveyErrorCode.SURVEY_CAN_NOT_STOPPED;
            case COLLECTING -> errorCode = SurveyErrorCode.SURVEY_CAN_NOT_PUBLISH;
            case AUDITING -> errorCode = SurveyErrorCode.SURVEY_CAN_NOT_AUDITING;
            case APPROVAL -> errorCode = SurveyErrorCode.SURVEY_CAN_NOT_APPROVAL;
            case REJECTED -> errorCode = SurveyErrorCode.SURVEY_CAN_NOT_REJECTED;
        }
        if (errorCode == null) {
            errorCode = SurveyErrorCode.SURVEY_CAN_NOT_PUBLISH;
        }
        return errorCode;
    }

    public static SurveyStatus checkNotDisabled(SurveyStatus status) {
        if (status == null) {
            status = STOPPED;
        }
        if (status == SurveyStatus.DISABLED) {
            throw new SurveyErrorException(SurveyErrorCode.SURVEY_DISABLED);
        }
        return status;
    }

    /**
     * 校验当前状态是否可以变更为 {toStatus}
     */
    public static void checkToStatus(SurveyStatus current, SurveyStatus toStatus) {
        current = checkNotDisabled(current);
        if (toStatus == null) {
            throw new RuntimeException("系统错误");
        }
        List<SurveyStatus> supportStatus = previousStatus(toStatus);
        if (!supportStatus.contains(current)) {
            throw new SurveyErrorException(toStatusError(toStatus));
        }
    }

    /**
     * 校验当前状态是否可以变更为 COLLECTING
     */
    public static void checkToCollecting(SurveyStatus current) {
        checkToStatus(current, COLLECTING);
    }

    /**
     * 校验当前状态是否可以变更为 STOPPED
     */
    public static void checkToStopped(SurveyStatus current) {
        checkToStatus(current, STOPPED);
    }

    /**
     * 校验当前状态是否可以变更为 AUDITING
     */
    public static void checkToAuditing(SurveyStatus current) {
        checkToStatus(current, AUDITING);
    }

    /**
     * 校验当前状态是否可以变更为 APPROVAL
     */
    public static void checkToApproval(SurveyStatus current) {
        checkToStatus(current, APPROVAL);
    }

    /**
     * 校验当前状态是否可以变更为 REJECTED
     */
    public static void checkToRejected(SurveyStatus current) {
        checkToStatus(current, REJECTED);
    }

    /**
     * 校验当前状态是否可以变更为 CONTENT_AUDITING
     */
    public static boolean needToContentAuditing(SurveyStatus current) {
        current = checkNotDisabled(current);
        List<SurveyStatus> supportStatus = previousStatus(CONTENT_AUDITING);
        return supportStatus.contains(current);
    }
}
