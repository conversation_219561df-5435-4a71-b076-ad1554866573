//package cn.hanyi.survey.core.dto.task;
//
//import cn.hanyi.ctm.constant.ConnectorDataType;
//import cn.hanyi.ctm.constant.ConnectorHttpType;
//import lombok.*;
//import org.befun.task.BaseTaskDetailDto;
//
///**
// * <AUTHOR>
// */
//@Builder
//@NoArgsConstructor
//@AllArgsConstructor
//@Getter
//@Setter
//public class WebhookBaseTaskDto extends BaseTaskDetailDto {
//    private Long pushId;
//    private Long orgId;
//    private Long connectorId;
//    private String url;
//    private String body;
//    private ConnectorHttpType method = ConnectorHttpType.POST;
//    private ConnectorDataType dataType = ConnectorDataType.JSON;
//}
