package cn.hanyi.survey.core.dto.message;

import cn.hanyi.survey.core.constant.survey.SurveyStatus;
import cn.hanyi.survey.core.entity.Survey;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;
import org.befun.core.rest.context.TenantContext;


/**
 * 问卷状态修改通知到kafka
 *
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
public class SurveyChangeDto extends BaseDTO {

    private Long surveyId;
    private Long userId;
    private String surveyName;
    private SurveyStatus status;
    private boolean deleted;

    public SurveyChangeDto(Survey survey) {
        this.surveyId = survey.getId();
        this.surveyName = survey.getTitle();
        this.status = survey.getStatus();
        this.deleted = survey.getDeleted();
        this.userId = TenantContext.getCurrentUserId();
    }
}
