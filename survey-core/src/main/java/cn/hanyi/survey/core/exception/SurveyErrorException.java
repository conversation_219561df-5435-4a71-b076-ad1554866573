package cn.hanyi.survey.core.exception;

import cn.hanyi.survey.core.constant.error.SurveyErrorCode;
import org.befun.core.exception.BaseException;

/**
 * The class description
 *
 * <AUTHOR>
 */
public class SurveyErrorException extends BaseException {

    public SurveyErrorException() {

    }

    public SurveyErrorException(boolean printStack, SurveyErrorCode surveyErrorCode) {
        this(surveyErrorCode);
        setPrintStack(printStack);
    }

    public SurveyErrorException(SurveyErrorCode surveyErrorCode) {
        super(surveyErrorCode.getValue(), surveyErrorCode.getMessage());
    }

    public SurveyErrorException(SurveyErrorCode surveyErrorCode, String detail) {
        super(surveyErrorCode.getValue(), surveyErrorCode.getMessage(), surveyErrorCode.getValue(), detail);
    }

    public SurveyErrorException(boolean printStack, SurveyErrorCode surveyErrorCode, String detail) {
        this(surveyErrorCode, detail);
        setPrintStack(printStack);
    }
}
