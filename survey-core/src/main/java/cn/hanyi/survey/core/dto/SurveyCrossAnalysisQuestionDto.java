package cn.hanyi.survey.core.dto;

import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.entity.BaseQuestionItem;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.core.utilis.RegularExpressionUtils;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.rest.view.ResourceViews;

@Getter
@Setter
public class SurveyCrossAnalysisQuestionDto {
    @JsonView(ResourceViews.Basic.class)
    private Long id;
    @JsonView(ResourceViews.Basic.class)
    private String name;
    @JsonView(ResourceViews.Basic.class)
    private String title;
    @JsonView(ResourceViews.Basic.class)
    private QuestionType type;
    @JsonView(ResourceViews.Basic.class)
    private String typeLabel;
    @JsonView(ResourceViews.Basic.class)
    private String code;
    @JsonView(ResourceViews.Basic.class)
    private String matrixText;
    @JsonView(ResourceViews.Basic.class)
    private String matrixValue;
    @JsonView(ResourceViews.Basic.class)
    private String fullTitle;

    public static SurveyCrossAnalysisQuestionDto mapFromQuestion(SurveyQuestion question, SurveyCrossAnalysisSimpleDto simple) {
        if (question == null || question.getType() == null || simple == null) {
            return null;
        }
        SurveyCrossAnalysisQuestionDto dto = new SurveyCrossAnalysisQuestionDto();
        dto.setId(question.getId());
        dto.setName(question.getName());
        dto.setTitle(RegularExpressionUtils.replaceHtml(question.getTitle()));
        dto.setType(question.getType());
        dto.setTypeLabel(question.getType().getText());
        dto.setCode(question.getCode());
        if (StringUtils.isNotEmpty(simple.getMatrixValue())) {
            dto.setMatrixValue(simple.getMatrixValue());
            dto.setMatrixText(question.getItems().stream().filter(i -> simple.getMatrixValue().equals(i.getValue())).findFirst().map(BaseQuestionItem::getText).orElse(null));
            if (dto.getMatrixText() == null) {
                return null;
            }
            dto.setFullTitle(dto.getCode() + " " + dto.getTitle() + "_" + dto.getMatrixText());
        } else {
            dto.setFullTitle(dto.getCode() + " " + dto.getTitle());
        }
        return dto;
    }

}
