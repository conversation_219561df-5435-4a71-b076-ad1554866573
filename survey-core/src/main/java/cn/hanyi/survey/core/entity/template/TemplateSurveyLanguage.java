package cn.hanyi.survey.core.entity.template;

import cn.hanyi.survey.core.entity.BaseSurveyLanguage;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @date 2023/3/14 10:00:05
 */
@Setter
@Getter
@Entity
@Table(name = "template_survey_language")
@DtoClass(includeAllFields = true)
public class TemplateSurveyLanguage extends BaseSurveyLanguage {
    private static final long serialVersionUID = 6529685098267751234L;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "s_id")
    @DtoProperty(ignore = true)
    @JsonIgnore
    private TemplateSurvey survey;

}
