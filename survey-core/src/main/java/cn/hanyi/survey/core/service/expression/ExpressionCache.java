package cn.hanyi.survey.core.service.expression;

import cn.hanyi.expression.expression.ExpressionEvaluator;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.core.entity.SurveyResponse;

import java.util.List;
import java.util.Map;

public interface ExpressionCache {
    Map getContext(Long responseId, Map<String, Object> extraContext, Boolean forceBuild);
    Map getContextNew(List<SurveyQuestion> questions, SurveyResponse response, Map<String, Object> extraContext, Boolean forceBuild);

    ExpressionEvaluator getExpressionEvaluator(String expression);

    Integer putExpressionEvaluator(String expression, ExpressionEvaluator expressionEvaluator);

    default int hashExpression(String expression) {
        return expression.hashCode();
    }

    // 增加额外值
    default Map append(Long responseId, Map data) {
        var context = getContext(responseId, data, true);
        synchronized (context) {
            context.putAll(data);
        }
        return context;
    }
}
