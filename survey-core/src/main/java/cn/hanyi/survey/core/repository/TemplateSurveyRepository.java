package cn.hanyi.survey.core.repository;

import cn.hanyi.survey.core.entity.template.TemplateGroup;
import cn.hanyi.survey.core.entity.template.TemplateSurvey;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 */
@Repository
public interface TemplateSurveyRepository extends ResourceRepository<TemplateSurvey, Long> {

    @Query(nativeQuery = true,
            value = "select ts.* from template_survey ts where org_id = ?1 and group_id in (SELECT group_id from template_group where title like ?2)")
    Page<TemplateSurvey> getTemplateGroupPage(Long orgId, String title, PageRequest pageRequest);


    @Query(nativeQuery = true,
            value = "select ts.* from template_survey ts where org_id = ?1 and group_id in (SELECT group_id from template_group tg where tg.title like ?2) and ts.title like ?3")
    Page<TemplateSurvey> getTemplateGroupPage(Long orgId, String title, String like, PageRequest pageRequest);

    //    @Query(nativeQuery = true,
//            value = "select ts.* from template_survey ts where org_id = ?1 and group_id in (SELECT group_id from template_group where title like ?2)")
//    List<TemplateSurvey> findSurveysByOrgIdAndTitle(Long orgId, String title);
    List<TemplateSurvey> findAllByGroup(TemplateGroup group);
}
