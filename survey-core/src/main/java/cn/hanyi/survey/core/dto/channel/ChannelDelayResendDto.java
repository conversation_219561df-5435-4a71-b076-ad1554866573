package cn.hanyi.survey.core.dto.channel;

import cn.hanyi.survey.core.constant.channel.ChannelResendCondition;
import cn.hanyi.survey.core.constant.channel.ChannelResendStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.core.utils.DateHelper;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ChannelDelayResendDto {

    @Schema(description = "任务id")
    private String jobId;

    @Schema(description = "重发时间")
    private String resendTime;

    @Schema(description = "重发条件")
    private List<ChannelResendCondition> resendConditions = List.of(ChannelResendCondition.UN_COMPLETE);

    @Schema(description = "任务状态")
    private ChannelResendStatus status = ChannelResendStatus.INIT;

    public ChannelDelayResendDto(String jobId, String resendTime, ChannelResendStatus status) {
        this.jobId = jobId;
        this.resendTime = resendTime;
        this.status = status;
    }

    public static List<ChannelDelayResendDto> parseResendTimes(LocalDateTime sendTime,
                                                               List<String> resendTimes,
                                                               List<OpenApiCreateWechatChannelResendRuleDto> resendRules) {
        LocalDateTime start = Optional.of(LocalDateTime.now()).map(i -> sendTime != null && sendTime.isAfter(i) ? sendTime : i).get();
        List<ChannelDelayResendDto> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(resendRules)) {
            resendRules.forEach(i -> {
                LocalDateTime resendTime = DateHelper.parseDateTime(i.getResendTime());
                if (resendTime != null) {
                    List<ChannelResendCondition> resendConditions = CollectionUtils.isNotEmpty(i.getConditions()) ? i.getConditions() : List.of(ChannelResendCondition.UN_COMPLETE);
                    if (resendTime.isAfter(start)) {
                        list.add(new ChannelDelayResendDto(UUID.randomUUID().toString(), i.getResendTime(), resendConditions, ChannelResendStatus.INIT));
                    } else {
                        list.add(new ChannelDelayResendDto(UUID.randomUUID().toString(), i.getResendTime(), resendConditions, ChannelResendStatus.CANCEL));
                    }
                }
            });
        } else if (CollectionUtils.isNotEmpty(resendTimes)) {
            resendTimes.forEach(i -> {
                LocalDateTime resendTime = DateHelper.parseDateTime(i);
                if (resendTime != null) {
                    if (resendTime.isAfter(start)) {
                        list.add(new ChannelDelayResendDto(UUID.randomUUID().toString(), i, ChannelResendStatus.INIT));
                    } else {
                        list.add(new ChannelDelayResendDto(UUID.randomUUID().toString(), i, ChannelResendStatus.CANCEL));
                    }
                }
            });
        }
        if (list.isEmpty()) {
            return null;
        }
        return list;
    }
}
