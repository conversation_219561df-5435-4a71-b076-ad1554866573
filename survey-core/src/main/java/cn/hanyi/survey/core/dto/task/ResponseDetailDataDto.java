package cn.hanyi.survey.core.dto.task;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.*;
import org.befun.core.dto.BaseDTO;
import org.befun.core.rest.view.ResourceViews;

import java.util.List;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ResponseDetailDataDto extends BaseDTO {
    @JsonView(ResourceViews.Basic.class)
    private String title;
    @JsonView(ResourceViews.Basic.class)
    private Object value;
    @JsonView(ResourceViews.Basic.class)
    private Object comment;
    @JsonView(ResourceViews.Basic.class)
    private List<String> tags;
    @JsonView(ResourceViews.Basic.class)
    private List<String> tagsAll;
    @JsonView(ResourceViews.Basic.class)
    private String code;
    @JsonView(ResourceViews.Basic.class)
    private String type;

}
