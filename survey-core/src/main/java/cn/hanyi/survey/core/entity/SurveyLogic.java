package cn.hanyi.survey.core.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.RootAware;

import javax.persistence.*;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Table(name = "survey_logic")
@DtoClass(includeAllFields = true)
public class SurveyLogic extends BaseLogic implements RootAware<Survey> {
    private static final long serialVersionUID = 6529685098267757690L;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "s_id")
    @DtoProperty(ignore = true)
    @JsonIgnore
    private Survey survey;

    @Override
    @JsonIgnore
    public Survey getRoot() {
        return survey;
    }
}
