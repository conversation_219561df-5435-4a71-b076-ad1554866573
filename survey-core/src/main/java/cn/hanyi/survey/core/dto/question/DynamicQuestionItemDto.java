package cn.hanyi.survey.core.dto.question;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.constraints.Length;

import java.util.regex.Pattern;

@Getter
@Setter
public class DynamicQuestionItemDto {

    public static final String reg = "^\\w+$";

    @Length(min = 1, max = 50)
    @Schema(description = "选项值")
    private String value;

    @Length(min = 1, max = 200)
    @Schema(description = "选项描述")
    private String text;

    public boolean checkIsValid() {
        return StringUtils.isNotEmpty(value)
                && value.length() < 50
                && StringUtils.isNotEmpty(text)
                && text.length() < 200
                && Pattern.compile(reg).matcher(value).matches();
    }
}
