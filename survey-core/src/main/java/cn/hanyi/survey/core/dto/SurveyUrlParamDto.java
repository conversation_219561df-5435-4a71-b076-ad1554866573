package cn.hanyi.survey.core.dto;

import cn.hanyi.survey.core.constant.channel.SurveyCollectorMethod;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Map;

@Getter
@Setter
@Builder
public class SurveyUrlParamDto {

    private Long surveyId;
    private Long channelId;
    private Long departmentId;
    private SurveyCollectorMethod collectorMethod;
    private String clientId;
    private Long customerId;
    private LocalDateTime expireTime;
    private Map<String, Object> externalParams;


}
