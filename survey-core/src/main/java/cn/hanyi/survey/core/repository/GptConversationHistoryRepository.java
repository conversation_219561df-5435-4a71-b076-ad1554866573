package cn.hanyi.survey.core.repository;

import cn.hanyi.survey.core.constant.analysis.GptConversationType;
import cn.hanyi.survey.core.entity.GptConversationHistory;
import cn.hanyi.survey.core.entity.Survey;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface GptConversationHistoryRepository extends ResourceRepository<GptConversationHistory, Long> {

    List<GptConversationHistory> findBySurveyAndType(Survey survey, GptConversationType type, Pageable page);

}
