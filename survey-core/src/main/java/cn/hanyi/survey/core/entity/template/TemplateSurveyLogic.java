package cn.hanyi.survey.core.entity.template;

import cn.hanyi.survey.core.entity.BaseLogic;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;

import javax.persistence.*;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Table(name = "template_survey_logic")
@DtoClass(includeAllFields = true)
public class TemplateSurveyLogic extends BaseLogic {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "s_id")
    @DtoProperty(ignore = true)
    private TemplateSurvey survey;

}
