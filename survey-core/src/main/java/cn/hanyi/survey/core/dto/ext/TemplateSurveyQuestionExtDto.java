package cn.hanyi.survey.core.dto.ext;

import cn.hanyi.survey.core.entity.template.TemplateSurveyQuestion;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.projection.SimpleUser;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.rest.view.ResourceViews;

@Getter
@Setter
public abstract class TemplateSurveyQuestionExtDto extends BaseEntityDTO<TemplateSurveyQuestion> {

    public TemplateSurveyQuestionExtDto(TemplateSurveyQuestion entity) {
        super(entity);
    }

    public TemplateSurveyQuestionExtDto() {
        super();
    }

    @JsonView(ResourceViews.Basic.class)
    private SimpleUser creator;
}
