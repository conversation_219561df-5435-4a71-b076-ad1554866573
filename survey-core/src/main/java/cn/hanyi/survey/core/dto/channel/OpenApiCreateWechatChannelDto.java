package cn.hanyi.survey.core.dto.channel;

import cn.hanyi.survey.core.constant.channel.ChannelType;
import cn.hanyi.survey.core.entity.SurveyChannelDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.utils.DateHelper;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Getter
@Setter
public class OpenApiCreateWechatChannelDto {

    @NotNull
    @Schema(description = "问卷id", required = true)
    private Long surveyId;

    @NotEmpty
    @Schema(description = "渠道名称", required = true)
    private String channelName;

    @Schema(description = "开始时间（yyyy-MM-dd HH:mm:ss）")
    private String startTime;

    @Schema(description = "结束时间（yyyy-MM-dd HH:mm:ss）")
    private String endTime;

    @Schema(description = "发送时间（yyyy-MM-dd HH:mm:ss）,如果为空或者小于当前时间，则立即发送")
    private String sendTime;

    @Schema(description = "重发时间列表，重发所有未完成的记录（yyyy-MM-dd HH:mm:ss），小于当前时间会忽略。（重发时间列表和重发规则列表，是互斥的，重发规则列表优先）")
    private List<String> resendTimes;

    @Schema(description = "重发规则列表（重发时间列表和重发规则列表，是互斥的，重发规则列表优先）")
    private List<OpenApiCreateWechatChannelResendRuleDto> resendRules;

    public SurveyChannelDto transform() {
        SurveyChannelDto dto = new SurveyChannelDto();
        dto.setType(ChannelType.WECHAT_SERVICE);
        dto.setSid(surveyId);
        dto.setName(channelName);
        dto.setStartTime(DateHelper.toDate(DateHelper.parseDateTime(startTime)));
        dto.setEndTime(DateHelper.toDate(DateHelper.parseDateTime(endTime)));
        dto.setSendTime(DateHelper.toDate(DateHelper.parseDateTime(sendTime)));
        dto.setResendTimes(ChannelDelayResendDto.parseResendTimes(DateHelper.toLocalDateTime(dto.getSendTime()), resendTimes, resendRules));
        return dto;
    }


}
