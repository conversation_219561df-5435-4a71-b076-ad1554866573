package cn.hanyi.survey.core.dto.question;

import cn.hanyi.survey.core.entity.SurveyQuestionItem;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class DynamicQuestionDto {
    @Length(min = 1, max = 100)
    @Schema(description = "问题编号")
    private String code;

    @NotEmpty
    @Schema(description = "动态选项")
    private List<DynamicQuestionItemDto> items = new ArrayList<>();

    public List<SurveyQuestionItem> convertToItems() {
        List<SurveyQuestionItem> newItems = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(items)) {
            for (DynamicQuestionItemDto item : this.items) {
                if (item.checkIsValid()) {
                    SurveyQuestionItem surveyQuestionItem = new SurveyQuestionItem();
                    surveyQuestionItem.setValue(item.getValue());
                    surveyQuestionItem.setText(item.getText());
                    newItems.add(surveyQuestionItem);
                }
            }
        }
        return newItems;
    }
}
