package cn.hanyi.survey.core.repository;

import cn.hanyi.survey.core.entity.SurveyResponseCell;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.Collection;
import java.util.List;
import java.util.Map;

@Repository
public interface SurveyResponseCellRepository extends ResourceRepository<SurveyResponseCell, Long> {

    List<SurveyResponseCell> findAllBySurveyIdAndResponseIdIn(Long surveyId, List<Long> responseIds);

    List<SurveyResponseCell> findAllBySurveyIdAndQuestionIdIn(Long surveyId, List<Long> questionIds);

    List<SurveyResponseCell> findAllBySurveyIdAndResponseId(Long surveyId, Long responseId);

    List<SurveyResponseCell> findAllBySurveyIdAndResponseIdAndCellScoreIsNotNull(Long surveyId, Long responseId);

    @Modifying
    @Transactional
    void deleteAllBySurveyIdAndResponseIdIn(Long surveyId, Iterable<Long> ids);

    @Query(nativeQuery = true, value = "SELECT q_id q_id, IFNULL(sum(cell_score)/count(q_id),0) avgScore from survey_response_cell where s_id in (?1) and type in (?2) GROUP BY q_id")
    List<Map<Long, Object>> findAllQuestionAvgScore(Iterable<Long> sid, List<Integer> types);

    List<SurveyResponseCell> findBySurveyIdAndResponseIdBetween(Long surveyId, Long responseIdStart, Long responseIdEnd);

    List<SurveyResponseCell> findBySurveyIdAndQuestionIdInAndResponseIdBetween(Long surveyId, Collection<Long> questionIds, Long responseIdStart, Long responseIdEnd);


    List<SurveyResponseCell> findBySurveyId(long sid);

    List<SurveyResponseCell> findBySurveyIdAndResponseIdAndQuestionIdIn(Long surveyId, Long responseId, Collection<Long> questionIds);
}
