package cn.hanyi.survey.core.entity;

import cn.hanyi.survey.core.constant.question.QuestionRandomType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Enumerated;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2022/12/2 13:43:49
 * @desc 题组、题目随机结果表
 */
@Setter
@Getter
@Entity
@Table(name = "survey_random_result")
@DtoClass(includeAllFields = true)
public class SurveyRandomResult extends BaseEntity {

    @Column(name = "survey_id")
    private Long surveyId;

    @Column(name = "response_id")
    private Long responseId;

    @Column(name = "question_id")
    private Long questionId;

    @Column(name = "item_id")
    private Long itemId;

    @Enumerated
    @Schema(description = "问题类型：题目、题组,选项")
    private QuestionRandomType type;

    @Column(name = "is_show")
    @Schema(description = "该问题是否显示，显示表示随机到")
    private Boolean isShow;

    @Column(name = "item_config")
    @Schema(description = "目前只记录随机到的评价题标签")
    private String itemConfig;
}
