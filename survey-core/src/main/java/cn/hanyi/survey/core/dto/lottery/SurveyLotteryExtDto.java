package cn.hanyi.survey.core.dto.lottery;

import cn.hanyi.survey.core.entity.SurveyLottery;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.projection.SimpleUser;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.rest.view.ResourceViews;

/**
 * <AUTHOR>
 * @date 2022/9/23 16:21:38
 */
@Getter
@Setter
public class SurveyLotteryExtDto extends BaseEntityDTO<SurveyLottery> {

    public SurveyLotteryExtDto() {
    }

    public SurveyLotteryExtDto(SurveyLottery lottery) {
        super(lottery);
    }

    @Schema(description = "总奖品数量")
    private Integer totalNumber;
    @Schema(description = "总中奖数量")
    private Integer totalWinnerNum;
    @Schema(description = "用户")
    private SimpleUser user;
    @Schema(description = "问卷名称")
    @JsonView(ResourceViews.Detail.class)
    private String surveyName;

}
