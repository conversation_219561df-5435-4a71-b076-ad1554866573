package cn.hanyi.survey.core.utilis;

import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.entity.BaseQuestion;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Slf4j
public class QuestionsUtils {


    /**
     * 过滤题目中的题组并排序
     *
     * @param questions
     * @return
     */
    public static List<SurveyQuestion> questionsFilterGroup(List<SurveyQuestion> questions) {
        List<SurveyQuestion> questionsOrderList = new ArrayList<>();
        if (questions.stream().noneMatch(q -> QuestionType.GROUP.equals(q.getType()))) {
            return questions;
        }
        ;
        questions.stream()
                .filter(q -> QuestionType.GROUP.equals(q.getType()))
                .sorted(Comparator.comparing(BaseQuestion::getSequence))
                .forEach(
                        group -> questions.stream()
                                .filter(q -> group.getName().equals(q.getGroupCode()))
                                .sorted(Comparator.comparing(BaseQuestion::getSequence))
                                .forEach(questionsOrderList::add)
                );
        return questionsOrderList;
    }

    /**
     * 只返回题组
     *
     * @param questions
     * @return
     */
    public static List<SurveyQuestion> questionsGroup(List<SurveyQuestion> questions) {
        return questions.stream().filter(q -> QuestionType.GROUP.equals(q.getType())).sorted(Comparator.comparing(BaseQuestion::getSequence)).collect(Collectors.toList());
    }
}





