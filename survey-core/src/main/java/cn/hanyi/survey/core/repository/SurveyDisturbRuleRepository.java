package cn.hanyi.survey.core.repository;

import cn.hanyi.survey.core.entity.SurveyDisturbRule;
import org.befun.core.repository.ResourceRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/9/26 16:09
 */
@Repository
public interface SurveyDisturbRuleRepository extends ResourceRepository<SurveyDisturbRule,Long> {
    List<SurveyDisturbRule> findByOrgIdAndStatus(Long orgId, Boolean status);

    List<SurveyDisturbRule> findByIdInOrderByModifyTimeDesc(Collection<Long> ids);

}
