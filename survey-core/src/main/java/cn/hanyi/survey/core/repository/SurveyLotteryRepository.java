package cn.hanyi.survey.core.repository;


import cn.hanyi.survey.core.constant.lottery.LotteryType;
import cn.hanyi.survey.core.dto.lottery.SimpleSurveyLotteryResult;
import cn.hanyi.survey.core.entity.SurveyLottery;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/20 17:30:15
 */
@Repository
public interface SurveyLotteryRepository extends ResourceRepository<SurveyLottery, Long> {

    List<SurveyLottery> findBySid(Long sid);

    List<SimpleSurveyLotteryResult> findAllBySid(Long sid);

    List<SurveyLottery> findBySid(Long sid, Sort sort);

    Page<SurveyLottery> findBySid(Long sid, Pageable pageable);

    int countBySidAndAndLotteryType(Long sid, LotteryType type);

    /**
     * 原子性更新抽奖总中奖人数
     *
     * @param lotteryId 抽奖ID
     * @return 更新的行数
     */
    @Modifying
    @Query("UPDATE SurveyLottery l SET l.totalWinnerNum = l.totalWinnerNum + 1 WHERE l.id = :lotteryId")
    int updateTotalWinnerNum(@Param("lotteryId") Long lotteryId);

}
