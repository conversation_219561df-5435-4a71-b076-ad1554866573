package cn.hanyi.survey.core.entity;

import cn.hanyi.survey.core.dto.ext.SurveyQuotaExtDto;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.RootAware;

import javax.persistence.*;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Table(name = "survey_quota")
@DtoClass(includeAllFields = true, superClass = SurveyQuotaExtDto.class)
public class SurveyQuota extends BaseQuota implements RootAware<Survey> {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "s_id")
    @DtoProperty(ignore = true)
    @JsonIgnore
    private Survey survey;

    @Override
    @JsonIgnore
    public Survey getRoot() {
        return survey;
    }

}
