package cn.hanyi.survey.core.entity;

import cn.hanyi.survey.core.dto.SurveyCrossAnalysisSimpleDto;
import cn.hanyi.survey.core.dto.ext.SurveyCrossAnalysisExtDto;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.BaseEntity;
import org.befun.core.hibernate.types.JsonColumn;
import org.befun.core.rest.view.ResourceViews;
import org.hibernate.annotations.Type;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/23 16:18:10
 */
@Getter
@Setter
@Entity
@Table(name = "survey_cross_analysis")
@DtoClass(includeAllFields = true, superClass = SurveyCrossAnalysisExtDto.class)
public class SurveyCrossAnalysis extends BaseEntity {

    @Schema(description = "问卷id")
    @Column(name = "s_id")
    @JsonView(ResourceViews.Basic.class)
    private Long sid;

    @Schema(description = "选择的行")
    @Column(name = "select_rows")
    @JsonView(ResourceViews.Basic.class)
    @Type(type = JsonColumn.TYPE)
    private List<SurveyCrossAnalysisSimpleDto> selectRows;

    @Schema(description = "选择的列")
    @Column(name = "select_Columns")
    @JsonView(ResourceViews.Basic.class)
    @Type(type = JsonColumn.TYPE)
    private List<SurveyCrossAnalysisSimpleDto> selectColumns;

    @Schema(description = "数据范围")
    @Column(name = "filter")
    @JsonView(ResourceViews.Basic.class)
    private String filter;

}
