package cn.hanyi.survey.core.entity;

import cn.hanyi.survey.core.constant.analysis.GptConversationStatus;
import cn.hanyi.survey.core.constant.analysis.GptConversationType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.EnterpriseEntity;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.util.UUID;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Table(name = "gpt_conversation_history")
@SQLDelete(sql = "UPDATE gpt_conversation_history SET deleted = 1 WHERE id=?")
@Where(clause = "deleted=0")
@DtoClass(includeAllFields = true)
public class GptConversationHistory extends EnterpriseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "s_id")
    @DtoProperty(ignore = true)
    @JsonIgnore
    private Survey survey;

    @Schema(description = "用户输入")
    @Column(name = "input")
    private String input;

    @Schema(description = "机器人输出")
    @Column(name = "output")
    private String output;

    @Schema(description = "会话顺序")
    @Column(name = "sequence")
    private Integer sequence;

    @Schema(description = "会话类型")
    @Column(name = "type")
    @Enumerated(EnumType.STRING)
    private GptConversationType type;

    @Schema(description = "会话状态")
    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private GptConversationStatus status;

    @Schema(description = "是否折叠")
    @Column(name = "is_fold")
    private Boolean isFold;

    @Schema(description = "是否删除")
    @Column(name = "deleted")
    private Boolean deleted;

    @Schema(description = "会话id")
    @Column(name = "conversation_id")
    private UUID conversationId = UUID.randomUUID();
}
