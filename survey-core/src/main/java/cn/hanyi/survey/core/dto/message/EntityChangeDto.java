package cn.hanyi.survey.core.dto.message;

import cn.hanyi.survey.core.constant.EntityType;
import cn.hanyi.survey.core.constant.EntityUpdateStatus;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;
import org.befun.core.rest.context.TenantContext;


/**
 * 问卷状态修改通知到kafka
 *
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
public class EntityChangeDto extends BaseDTO {

    private Long userId = TenantContext.getCurrentUserId();
    private Long orgId = TenantContext.getCurrentTenant();
    private Long surveyId;
    private Long entityId;
    private String entityName;
    private EntityType entityType;
    private EntityUpdateStatus status;
}
