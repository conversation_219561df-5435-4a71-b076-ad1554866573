//package cn.hanyi.survey.core.dto.task;
//
//import cn.hanyi.survey.core.constant.channel.ChannelType;
//import cn.hanyi.survey.core.dto.channel.TemplateInfoDto;
//import com.fasterxml.jackson.annotation.JsonIgnore;
//import lombok.Getter;
//import lombok.Setter;
//import org.befun.core.utils.DateHelper;
//
//import java.util.Date;
//import java.util.Map;
//import java.util.Optional;
//
//@Getter
//@Setter
//public class SendBaseDto {
//
//    private Long userId;    // 当前操作用户id
//    private boolean isAppend;   // true 追加发送 false 首次发送
//    private String sendTime; // 发送时间，没有此项则立即发送
//    private Long surveyId;
//    private Long channelId;
//    private Long orgId;
//    private Long departmentId;
//    private ChannelType channelType;
//
//    private Map<String, Object> contentTemplate;// 内容模板
//
//    private TemplateInfoDto template;
//
//
//    @JsonIgnore
//    private Date _sendTime;
//
//    public Date transformSendDate() {
//        if (_sendTime == null) {
//            _sendTime = Optional.ofNullable(DateHelper.toDate(DateHelper.parseAdjust(sendTime))).orElse(new Date());
//        }
//        return _sendTime;
//    }
//}
