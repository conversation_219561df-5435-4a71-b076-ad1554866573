package cn.hanyi.survey.core.dto;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;
import org.befun.core.utils.ListHelper;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class SurveyCrossAnalysisQuestionsDto {

    @JsonView(ResourceViews.Basic.class)
    private List<SurveyCrossAnalysisQuestionDto> selectRows;

    @JsonView(ResourceViews.Basic.class)
    private List<SurveyCrossAnalysisQuestionDto> selectColumns;

    public SurveyCrossAnalysisQuestionsDto(
            SurveyCrossAnalysisQuestionDto row1,
            SurveyCrossAnalysisQuestionDto row2,
            SurveyCrossAnalysisQuestionDto column) {
        this.selectRows = ListHelper.arrayList(row1, row2);
        this.selectColumns = ListHelper.arrayList(column);
    }
}
