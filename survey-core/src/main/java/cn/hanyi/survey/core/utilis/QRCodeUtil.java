package cn.hanyi.survey.core.utilis;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.awt.geom.RoundRectangle2D;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.OutputStream;
import java.util.Hashtable;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 二维码生成解析工具类
 */
public class QRCodeUtil {

    //编码格式,采用utf-8
    private String UNICODE = "utf-8";
    //图片格式
    private String FORMAT = "png";
    //二维码宽度,单位：像素pixels
    private int QRCODE_WIDTH = 300;
    //二维码高度,单位：像素pixels
    private int QRCODE_HEIGHT = 300;
    //LOGO宽度,单位：像素pixels
    private int LOGO_WIDTH = 100;
    //LOGO高度,单位：像素pixels
    private int LOGO_HEIGHT = 100;

    public QRCodeUtil() {
    }

    public QRCodeUtil(Integer width, Integer height) {
        this.QRCODE_WIDTH = width;
        this.QRCODE_HEIGHT = height;
    }

    public void setUnicode(String unicode) {
        this.UNICODE = unicode;
    }

    public void setFormat(String format) {
        this.FORMAT = format;
    }

    public void setQrcodeWidth(int qrcodeWidth) {
        this.QRCODE_WIDTH = qrcodeWidth;
    }

    public void setQrcodeHeight(int qrcodeHeight) {
        this.QRCODE_HEIGHT = qrcodeHeight;
    }

    public void setLogoWidth(int logoWidth) {
        this.LOGO_WIDTH = logoWidth;
    }

    public void setLogoHeight(int logoHeight) {
        this.LOGO_HEIGHT = logoHeight;
    }

    /**
     * 下载二维码
     *
     * @param fileName     文件名
     * @param content
     * @param logo
     * @param needCompress
     * @throws Exception
     */
    public void download(HttpServletResponse response, String fileName, String content, String logo, boolean needCompress) throws Exception {
        fileName = new String(fileName.getBytes(), "iso8859-1");
        response.setHeader("content-disposition", "attachment;filename=" + fileName + "." + this.FORMAT);
        response.setHeader("content-type", "image/png");
        BufferedImage image = this.createImage(content, logo, needCompress);
        ImageIO.write(image, this.FORMAT, response.getOutputStream());
    }

    /**
     * 下载二维码
     *
     * @param fileName     文件名
     * @param contentList
     * @param logo
     * @param needCompress
     * @throws Exception
     */
    public void downloadToZip(HttpServletResponse response, String fileName, List<Map<String, String>> contentList,
                              String logo, boolean needCompress) throws Exception {
        response.setContentType("application/zip");
        fileName = new String(fileName.getBytes(), "iso8859-1");
        response.setHeader("Content-disposition", "attachment; filename=" + fileName + ".zip");
        OutputStream outputStream = response.getOutputStream();
        ZipOutputStream zipOutputStream = new ZipOutputStream(outputStream);

        for (int i = 0; i < contentList.size(); i++) {
            BufferedImage image = this.createImage(contentList.get(i).get("content"), logo, needCompress);
            ZipEntry entry = new ZipEntry(contentList.get(i).get("name") + "." + this.FORMAT);
            zipOutputStream.putNextEntry(entry);
            ImageIO.write(image, this.FORMAT, zipOutputStream);
            zipOutputStream.flush();
        }
        zipOutputStream.close();
        outputStream.flush();
        outputStream.close();
    }

    /**
     * 生成二维码图片
     *
     * @param content      二维码内容
     * @param logoPath     图片地址
     * @param needCompress 是否压缩
     * @return
     * @throws Exception
     */
    private BufferedImage createImage(String content, String logoPath, boolean needCompress) throws Exception {
        Hashtable<EncodeHintType, Object> hints = new Hashtable<EncodeHintType, Object>();
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
        hints.put(EncodeHintType.CHARACTER_SET, UNICODE);
        hints.put(EncodeHintType.MARGIN, 1);
        BitMatrix bitMatrix = new MultiFormatWriter().encode(content, BarcodeFormat.QR_CODE, QRCODE_WIDTH, QRCODE_HEIGHT, hints);
        int width = bitMatrix.getWidth();
        int height = bitMatrix.getHeight();
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                image.setRGB(x, y, bitMatrix.get(x, y) ? 0xFF000000 : 0xFFFFFFFF);
            }
        }
        if (logoPath == null || "".equals(logoPath)) {
            return image;
        }
        // 插入图片
        this.insertImage(image, logoPath, needCompress);
        return image;
    }

    /**
     * 插入LOGO
     *
     * @param source       二维码图片
     * @param logoPath     LOGO图片地址
     * @param needCompress 是否压缩
     * @throws Exception
     */
    private void insertImage(BufferedImage source, String logoPath, boolean needCompress) throws Exception {
        File file = new File(logoPath);
        if (!file.exists()) {
            throw new Exception("logo file not found.");
        }
        Image src = ImageIO.read(new File(logoPath));
        int width = src.getWidth(null);
        int height = src.getHeight(null);
        if (needCompress) { // 压缩LOGO
            if (width > LOGO_WIDTH) {
                width = LOGO_WIDTH;
            }
            if (height > LOGO_HEIGHT) {
                height = LOGO_HEIGHT;
            }
            Image image = src.getScaledInstance(width, height, Image.SCALE_SMOOTH);
            BufferedImage tag = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            Graphics g = tag.getGraphics();
            g.drawImage(image, 0, 0, null); // 绘制缩小后的图
            g.dispose();
            src = image;
        }
        // 插入LOGO
        Graphics2D graph = source.createGraphics();
        int x = (QRCODE_WIDTH - width) / 2;
        int y = (QRCODE_HEIGHT - height) / 2;
        graph.drawImage(src, x, y, width, height, null);
        Shape shape = new RoundRectangle2D.Float(x, y, width, width, 6, 6);
        graph.setStroke(new BasicStroke(3f));
        graph.draw(shape);
        graph.dispose();
    }
}

