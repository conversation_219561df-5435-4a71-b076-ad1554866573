package cn.hanyi.survey.core.dto.ext;

import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.dto.SurveyLinkDto;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyChannelDto;
import cn.hanyi.survey.core.entity.SurveyGroupDto;
import cn.hanyi.survey.core.entity.SurveyQuestionDto;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.auth.projection.SimpleUser;
import org.befun.core.dto.BaseResourcePermissionEntityDto;
import org.befun.core.rest.view.ResourceViews;

import java.util.*;
import java.util.stream.Collectors;

@Getter
@Setter
public abstract class SurveyExtDto extends BaseResourcePermissionEntityDto<Survey> {

    public SurveyExtDto() {
    }

    public SurveyExtDto(Survey entity) {
        super(entity);
    }

    @Schema(description = "问题数量")
    @JsonView(ResourceViews.Basic.class)
    private Integer numOfQuestions;

    @Schema(description = "答题数量")
    @JsonView(ResourceViews.Basic.class)
    private Integer numOfResponses;

    @Schema(description = "最新答题时间")
    @JsonView(ResourceViews.Basic.class)
    private Date lastResponseTime;

    @JsonView(ResourceViews.Basic.class)
    private List<SurveyQuestionDto> filterQuestions;

    @Schema(description = "修改人，可能为null")
    @JsonView(ResourceViews.Basic.class)
    private SimpleUser editorUser;

    @Schema(description = "创建人，可能为null")
    @JsonView(ResourceViews.Basic.class)
    private SimpleUser creator;

    @JsonView(ResourceViews.Basic.class)
    private LinkedHashMap<String, Object> cellData;

    @JsonView(ResourceViews.Basic.class)
    private SurveyChannelDto channel;

    @Schema(description = "是否有分享给其他人")
    @JsonView(ResourceViews.Basic.class)
    private Boolean hasShared;

    @Schema(description = "项目类型：1 问卷 2 目录")
    @JsonView(ResourceViews.Basic.class)
    private int itemType = 1;

    @Schema(description = "目录信息")
    @JsonView(ResourceViews.Basic.class)
    private SurveyGroupDto group;

    @Schema(description = "问卷链接，获取问卷详情时，会返回")
    @JsonView(ResourceViews.Basic.class)
    private SurveyLinkDto link;

    @Schema(description = "允许开关logo")
    @JsonView(ResourceViews.Basic.class)
    private Boolean enableLogoChange = false;

    @Schema(description = "允许使用多语言")
    @JsonView(ResourceViews.Basic.class)
    private Boolean enableMultiLanguage = false;

    public abstract Long getGroupId();

    public abstract List<SurveyQuestionDto> getQuestions();

    // 重写 numOfQuestions get
    public Integer getNumOfQuestions() {
        if (numOfQuestions == null) {
            numOfQuestions = Optional.ofNullable(getQuestions().stream().filter(q -> q.getType() != QuestionType.GROUP && q.getType() != QuestionType.SEPARATOR)
                    .collect(Collectors.toList())).map(List::size).orElse(0);
        }
        return numOfQuestions;
    }

    // 重写 filterQuestions get
    public List<SurveyQuestionDto> getFilterQuestions() {
        if (filterQuestions == null) {
            if (getQuestions() == null) {
                filterQuestions = new ArrayList<>();
            } else {
                filterQuestions = getQuestions().stream().filter(x -> x.getType() == QuestionType.SCORE).collect(Collectors.toList());
            }
        }
        return filterQuestions;
    }

    // 重写 hasShared get
    public Boolean getHasShared() {
        return CollectionUtils.isNotEmpty(getPermissionUsers())
                || CollectionUtils.isNotEmpty(getPermissionRoles())
                || CollectionUtils.isNotEmpty(getPermissionDepartments());
    }
}
