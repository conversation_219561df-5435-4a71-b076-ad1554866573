package cn.hanyi.survey.core.repository;

import cn.hanyi.survey.core.constant.QuotaChannelType;
import cn.hanyi.survey.core.constant.QuotaType;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyQuota;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public interface SurveyQuotaRepository extends ResourceRepository<SurveyQuota, Long> {

    List<SurveyQuota> findAllByIdIn(Iterable<Long> ids);

    SurveyQuota findOneById(Long id);

    Page<SurveyQuota> findAllBySurvey(Survey survey, Pageable pageable);

    @Transactional
    @Modifying
    @Query("update SurveyQuota s set s.expressionHash = null where s.survey = ?1")
    void updateExpressionHashBySurvey(Survey survey);

    List<SurveyQuota> findAllBySurveyAndGroupCodeAndTypeAndChannelType(Survey survey, String code, QuotaType type, QuotaChannelType channelType);

    @Query(nativeQuery = true,
            value = "select * from survey_quota where id in (select "
                    + "max(id) as id "
                    + "from survey_quota where s_id = ?1 and channel_type = 0 group by case when group_code <> '' then group_code else id end)",
            countQuery = "select count(id) from survey_quota where s_id = ?1 and channel_type = 0 group by case when group_code <> '' then group_code else id end")
    Page<SurveyQuota> findQuotaList(Long surveyId, Pageable pageable);

}
