package cn.hanyi.survey.core.entity.template;

import cn.hanyi.survey.core.entity.BaseQuestionRandom;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;

import javax.persistence.*;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/4/18 14:59
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "template_survey_question_random")
@DtoClass(includeAllFields = true)
public class TemplateSurveyQuestionRandom extends BaseQuestionRandom {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "s_id")
    @DtoProperty(ignore = true)
    private TemplateSurvey survey;
}
