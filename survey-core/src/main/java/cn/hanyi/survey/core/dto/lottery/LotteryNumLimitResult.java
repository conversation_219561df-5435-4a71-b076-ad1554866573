package cn.hanyi.survey.core.dto.lottery;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2022/10/12 14:51:17
 */
@Getter
@Setter
public class LotteryNumLimitResult {
    @Schema(description = "是否超过中奖数量额度限制")
    private Boolean isPrizeNumLimit = false;
    @Schema(description = "奖品中奖数量增加标志，默认：未增加，大于0：已增加")
    private Long prizeNumLimitStatus = -1l;
}
