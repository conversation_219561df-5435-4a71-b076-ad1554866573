package cn.hanyi.survey.core.dto.lottery;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2022/9/27 11:11:36
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class SurveyClientLotteryResult {

    @Schema(description = "奖品索引")
    private Integer index;

    @Schema(description = "抽奖次数")
    private Integer lotteryNum;

    @Schema(description = "兑换码")
    private String code;

    @Schema(description = "中奖信息id")
    private Long prizeWinnerId;

    public SurveyClientLotteryResult(Integer index, Integer lotteryNum, String code) {
        this.index = index;
        this.lotteryNum = lotteryNum;
        this.code = code;
    }

}
