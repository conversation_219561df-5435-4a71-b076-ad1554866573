package cn.hanyi.survey.core.dto.quota;

import cn.hanyi.survey.core.entity.SurveyQuotaDto;
import com.fasterxml.jackson.annotation.JsonView;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

@Getter
@Setter
public class SurveyQuotaBatchOptionRequestDto {
    @JsonView(ResourceViews.Basic.class)
    private List<SurveyQuotaDto> quotas;

    @JsonView(ResourceViews.Basic.class)
    private String groupCode;
}