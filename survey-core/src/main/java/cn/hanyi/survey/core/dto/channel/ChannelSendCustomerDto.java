package cn.hanyi.survey.core.dto.channel;

import cn.hanyi.survey.core.constant.channel.ChannelType;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;
import org.befun.core.utils.DateHelper;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.Map;
import java.util.Optional;

@Getter
@Setter
public class ChannelSendCustomerDto extends ChannelSelectCustomerDto {

    @JsonView({ResourceViews.Basic.class})
    @Schema(description = "发送时间（yyyy-MM-dd HH:mm:ss）：定时发送有此参数")
    private String sendTime;

    @JsonView({ResourceViews.Basic.class})
    @Schema(description = "模板id(短信，邮件)")
    private Long thirdPartTemplateId;

    @JsonView({ResourceViews.Basic.class})
    @Schema(description = "模板id(微信)")
    private Long wechatTemplateId;

    @JsonView({ResourceViews.Basic.class})
    @Schema(description = "模板内容：如果是微信渠道，则包含微信模板消息的参数；如果是短信渠道，可以包含content(短信内容，替换模板的默认内容)；如果是邮件渠道，必须包含sender(发送人)，可以包含title(邮件标题，替换模板的默认内容)，可以包含content(邮件内容，替换模板的默认内容)")
    private Map<String, Object> content;

    @Schema(hidden = true, description = "")
    private Long surveyId;
    @Schema(hidden = true, description = "")
    private Long channelId;
    @Schema(hidden = true, description = "需要检验channelId对应的类型是指定的类型")
    private ChannelType requireChannelType;

    public Date parseSendTime() {
        return DateHelper.toDate(Optional.ofNullable(DateHelper.parseAdjust(sendTime)).orElse(LocalDateTime.now()));
    }
}
