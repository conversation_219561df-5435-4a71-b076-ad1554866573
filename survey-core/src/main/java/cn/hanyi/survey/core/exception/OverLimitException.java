package cn.hanyi.survey.core.exception;

import org.befun.core.exception.BaseException;
import cn.hanyi.survey.core.constant.error.SurveyErrorCode;

/**
 * The class description
 *
 * <AUTHOR>
 */
public class OverLimitException extends BaseException {

    public OverLimitException() {
        super(SurveyErrorCode.OVER_LIMIT.getValue(), "该设备已经提交，不能重复提交");
    }

    public OverLimitException(int code,String message) {
        super(code, message);
    }
}
