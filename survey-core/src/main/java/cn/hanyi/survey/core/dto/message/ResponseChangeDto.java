package cn.hanyi.survey.core.dto.message;

import cn.hanyi.survey.core.constant.ResponseStatus;
import cn.hanyi.survey.core.entity.SurveyResponse;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;


/**
 * 答卷状态修改通知到kafka
 *
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
public class ResponseChangeDto extends BaseDTO {

    private Long surveyId;
    private Long responseId;
    private ResponseStatus status;

    public ResponseChangeDto(SurveyResponse response) {
        this.surveyId = response.getSurveyId();
        this.responseId = response.getId();
        this.status = response.getStatus();
    }
}
