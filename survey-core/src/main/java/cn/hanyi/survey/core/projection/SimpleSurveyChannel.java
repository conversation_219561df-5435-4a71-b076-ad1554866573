package cn.hanyi.survey.core.projection;

import cn.hanyi.survey.core.constant.channel.ChannelType;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Value;
import org.befun.core.rest.view.ResourceViews;

@Value
public class SimpleSurveyChannel {

    @JsonView(ResourceViews.Basic.class)
    Long id;
    @JsonView(ResourceViews.Basic.class)
    String name;
    @JsonView(ResourceViews.Basic.class)
    ChannelType type;

    public ChannelType getType() {
        return type == null ? ChannelType.COMMON : type;
    }
}
