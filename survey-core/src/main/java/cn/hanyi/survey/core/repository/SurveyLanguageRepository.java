package cn.hanyi.survey.core.repository;

import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyLanguage;
import org.befun.core.repository.ResourceRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface SurveyLanguageRepository extends ResourceRepository<SurveyLanguage, Long> {

    Optional<SurveyLanguage> findBySurveyAndLanguage(Survey survey, String language);

}
