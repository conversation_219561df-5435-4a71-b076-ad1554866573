package cn.hanyi.survey.core.utilis;

import cn.hanyi.survey.core.entity.BaseQuestion;

import java.math.BigDecimal;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 */
public class SurveyUtils {

    public static <Q extends BaseQuestion> Consumer<Q> insertQuestion(Q question, Supplier<List<Q>> getOldQuestions, Consumer<Q> setSurvey) {
        setSurvey.accept(question);
        BigDecimal sequence = question.getSequence();
        List<Q> questions = getOldQuestions.get();
        int index = questions.size();
        // List version, TBD
        for (int i = 0; i < index; i++) {
            Q q = questions.get(i);
            if (q.getSequence().compareTo(sequence) >= 0) {
                index = i;
                break;
            }
        }

        questions.add(index, question);

        for (int i = index + 1; i < questions.size(); i++) {
            Q q = questions.get(i);
            if (q.getSequence().compareTo(sequence) >= 0) {
                q.setSequence(q.getSequence().add(BigDecimal.ONE));
            }
        }
        return setSurvey;
    }
}
