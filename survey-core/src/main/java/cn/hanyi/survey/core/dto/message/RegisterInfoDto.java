package cn.hanyi.survey.core.dto.message;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.*;
import org.befun.core.dto.BaseDTO;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RegisterInfoDto extends BaseDTO {

    private String userName;
    private String trueName;
    private String password;
    private String email;
    @JsonAlias("org_id")
    private Long orgId;
    @JsonAlias("role_id")
    private Object roleId;
    private Long departmentId;
    private String mobile;
    private int status;
    private String availableSystems;

    public List getRoleId() {
        if (roleId instanceof List) {
            return (List) roleId;
        }
        return List.of((Long) roleId);
    }
}
