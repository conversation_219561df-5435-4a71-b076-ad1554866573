package cn.hanyi.survey.core.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;

import javax.persistence.*;
import java.util.Date;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Table(name = "response_shared")
@DtoClass(includeAllFields = true)
@EntityScopeStrategy
public class ResponseShared extends EnterpriseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "config_id")
    @DtoProperty(ignore = true)
    @JsonIgnore
    private ResponseSharedConfig responseSharedConfig;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "s_id")
    @DtoProperty(ignore = true)
    @JsonIgnore
    private Survey survey;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "r_id")
    @DtoProperty(ignore = true)
    @JsonIgnore
    private SurveyResponse response;

    @Column(name = "token")
    private String token;

    @Column(name = "expire_time")
    public Date expireTime;


    public ResponseShared(Survey survey, SurveyResponse response) {
        this.survey = survey;
        this.response = response;
    }

    public ResponseShared(Survey survey, ResponseSharedConfig config, SurveyResponse response, String token, Date expireTime) {
        this.survey = survey;
        this.responseSharedConfig = config;
        this.response = response;
        this.token = token;
        this.expireTime = expireTime;
    }

}
