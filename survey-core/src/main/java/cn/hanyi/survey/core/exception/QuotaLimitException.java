package cn.hanyi.survey.core.exception;

import cn.hanyi.survey.core.constant.error.SurveyErrorCode;
import org.befun.core.exception.BaseException;

/**
 * 只允许配额超限抛出错误 不给回退
 *
 * <AUTHOR>
 */
public class QuotaLimitException extends BaseException {

    public QuotaLimitException() {
        super(SurveyErrorCode.OVER_LIMIT.getValue(), "该设备已经提交，不能重复提交");
    }

    public QuotaLimitException(int code, String message) {
        super(code, message);
    }
}
