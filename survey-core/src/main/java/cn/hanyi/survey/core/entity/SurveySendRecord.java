package cn.hanyi.survey.core.entity;

import cn.hanyi.survey.core.constant.channel.ChannelSendStatus;
import cn.hanyi.survey.core.constant.channel.ChannelType;
import cn.hanyi.survey.core.constant.ReplyStatus;
import cn.hanyi.survey.core.constant.survey.ReceiveStatus;
import cn.hanyi.survey.core.constant.survey.SendStatus;
import cn.hanyi.survey.core.dto.SurveySendRecordExtParamDto;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.annotation.ResourceFieldCloneRule;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;
import java.util.Date;

@Entity
@Getter
@Setter
@NoArgsConstructor
//@EntityQueryable({"name","account"})
@Table(name = "survey_send_record")
@DtoClass(includeAllFields = true)
public class SurveySendRecord extends BaseEntity {
    private static final long serialVersionUID = 6529685098267757690L;

    @Schema(description = "问卷id")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "s_id")
    private Long surveyId;

    @Schema(description = "客户id")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "customer_Id")
    private Long customerId;

    @Schema(description = "渠道id", required = true)
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "channel_id")
    private Long channelId;

    @ResourceFieldCloneRule(ignore = true)
    @Schema(description = "客户端id", required = true)
    @Column(name = "client_id")
    private String clientId;

    @Schema(description = "接收人姓名", required = true)
    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class, description = "接收人姓名")
    private String name;

    @Schema(description = "接收人账号(微信openid或者手机号)", required = true)
    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class, description = "接收人账号(微信openid或者手机号)")
    private String account;

    @Schema(description = "最后一次发送的消息模版")
    @JsonView(ResourceViews.Detail.class)
    @Column(name = "thirdparty_template_id")
    private Long thirdpartyTemplateId;

    @Schema(description = "短信内容", required = true)
    @JsonView(ResourceViews.Detail.class)
    private String content;

    @Schema(description = "问卷地址", required = true)
    @Column(name = "send_url")
    private String sendUrl;

    @Schema(description = "问卷地址短链id")
    @Column(name = "link_id")
    private Long linkId;

    @Schema(description = "发送次数", required = true)
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "send_count")
    private Integer sendCount = 0;

    @Temporal(TemporalType.TIMESTAMP)
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "问卷发送时间")
    @Column(name = "send_time")
    private Date sendTime;

    @Column(name = "send_status")
    @Enumerated
    @Schema(description = "发送状态(0:待发送,1:发送成功,2:发送失败)")
    @JsonView(ResourceViews.Basic.class)
    private SendStatus sendStatus = SendStatus.UN_SEND;

    @Column(name = "receive_status")
    @Enumerated
    @Schema(description = "接受状态(0:待接受,1:接受成功,2:接受失败)")
    @JsonView(ResourceViews.Basic.class)
    private ReceiveStatus receiveStatus = ReceiveStatus.UNKNOWN;

    @Column(name = "reply_status")
    @Enumerated
    @Schema(description = "问卷填答状态(0:未访问,1:未提交,2:已提交)")
    @JsonView(ResourceViews.Basic.class)
    private ReplyStatus replyStatus = ReplyStatus.UN_VISIT;

    @Enumerated
    @Schema(description = "回收状态(0:未完成,1:已完成)")
    @JsonView(ResourceViews.Basic.class)
    private ChannelSendStatus status = ChannelSendStatus.UN_COMPLETE;

    @Enumerated
    @JsonIgnore
    @Schema(description = "渠道类型 0通用类型,1wechat短链接,2短信,3微信服务号,4页面嵌入,5调研家社区,6场景互动")
    private ChannelType type = ChannelType.COMMON;

    @Schema(description = "回执id", required = true)
    @Column(name = "send_id")
    private String sendId = "";

    @Schema(description = "短信发送返回结果", required = true)
    private String result = "";

    /**
     * 发送问卷（上传文件）客户会有一些额外的参数，需要保存在这里，如果重发短信，会用到这些 （json格式的）
     * 可以转换为这个对象{@link cn.hanyi.survey.core.dto.SurveySendRecordExtParamDto}，或者null
     */
    @JsonIgnore
    @Column(name = "ext_params")
    private String extParams;

    @JsonIgnore
    @Transient
    private SurveySendRecordExtParamDto extParamsDto;

}
