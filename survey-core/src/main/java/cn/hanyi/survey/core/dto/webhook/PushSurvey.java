package cn.hanyi.survey.core.dto.webhook;

import cn.hanyi.ctm.constant.connector.ConnectorPushCondition;
import lombok.*;
import org.befun.core.dto.BaseDTO;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PushSurvey extends BaseDTO {
    private Long surveyId;
    private String surveyTitle;
    private String surveyCode;
    private ConnectorPushCondition surveyStatus;
    private Date createTime;
    private Date modifyTime;
    private String creator;
    private String editor;
}
