package cn.hanyi.survey.core.dto.ext;

import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.core.entity.SurveyQuestionItem;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.rest.view.ResourceViews;


@Getter
@Setter
public class SurveyQuestionItemExtDto extends BaseEntityDTO<SurveyQuestionItem> {

    public SurveyQuestionItemExtDto() {
    }

    public SurveyQuestionItemExtDto(SurveyQuestionItem entity) {
        super(entity);
    }

    @Schema(description = "选项分值")
    @JsonView(ResourceViews.Basic.class)
    private Integer score;

}
