package cn.hanyi.survey.core.dto.lottery;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/14 17:34:22
 */
@Setter
@Getter
public class SurveyPrizeWinnerResult {

    @Schema(description = "已中奖品数量")
    private Long winnerNum = 0l;

    @Schema(description = "已发放奖励数量")
    private Long SendPrizeNum = 0l;

    @Schema(description = "未发放奖励数量")
    private Long notSendPrizeNum = 0l;

    @Schema(description = "今日发放奖励")
    private Long todaySendPrizeNum = 0l;

    @Schema(description = "今日未发放奖励")
    private Long todayNotSendPrizeNum = 0l;

    @Schema(description = "奖励列表")
    private List<SurveyLotteryStatisticsDto> prizeWinnerList;

    @Schema(description = "分页信息")
    private MetaDto meta;
}
