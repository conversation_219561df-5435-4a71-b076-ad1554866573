package cn.hanyi.survey.core.constant;

import lombok.Getter;

@Getter
public enum ResponseStatus {
    INIT("未提交", "无效"),
    FINAL_SUBMIT("非社区正常提交/社区答卷审核通过", "有效"),
    EARLY_COMPLETED("提前结束", "无效"),
    INVALID("无效", "无效"),
    DELETED("删除", "无效"),
    WAIT_AUDIT("社区答卷等待审核", "无效"),
    AUDIT_FAIL("社区答卷审核不通过", "无效"),
    QUOTA_FUll("配额已满", "无效"),
    AUDIT_ERROR("审核异常", "无效"),
    ;

    private final String text;
    private final String extText;

    ResponseStatus(String text, String extText) {
        this.text = text;
        this.extText = extText;
    }
}
