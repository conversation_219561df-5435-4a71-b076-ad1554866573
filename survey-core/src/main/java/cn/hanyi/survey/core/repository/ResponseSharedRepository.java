package cn.hanyi.survey.core.repository;

import cn.hanyi.survey.core.entity.ResponseShared;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyResponse;
import org.befun.core.repository.ResourceRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface ResponseSharedRepository extends ResourceRepository<ResponseShared, Long> {

    Optional<ResponseShared> findBySurveyAndResponse(Survey survey, SurveyResponse response);

    Optional<ResponseShared> findBySurvey(Survey survey);

}
