package cn.hanyi.survey.core.dto.channel;

import cn.hanyi.survey.core.constant.channel.ChannelType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.core.exception.BadRequestException;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
@Setter
public class OpenApiSendWechatDto {

    @NotNull
    @Schema(description = "问卷id", required = true)
    private Long surveyId;

    @NotNull
    @Schema(description = "渠道id", required = true)
    private Long channelId;

    @NotNull
    @Schema(description = "消息库模版id", required = true)
    private Long wechatTemplateId;

    @NotEmpty
    @Schema(description = "联系人：不能超过100个", required = true)
    private List<UserContactDto> contacts;

    @Schema(description = "模板内容")
    private Map<String, Object> content = new HashMap<>();

    public ChannelSendCustomerDto transform() {
        if (CollectionUtils.isEmpty(contacts)) {
            throw new BadRequestException("联系人不能为空");
        } else if (contacts.size() > 100) {
            throw new BadRequestException("联系人不能超过100个");
        }
        ChannelSendCustomerDto dto = new ChannelSendCustomerDto();
        dto.setSurveyId(surveyId);
        dto.setChannelId(channelId);
        dto.setRequireChannelType(ChannelType.WECHAT_SERVICE);
        dto.setWechatTemplateId(wechatTemplateId);
        dto.setContacts(contacts);
        dto.setContent(content);
        return dto;
    }

}
