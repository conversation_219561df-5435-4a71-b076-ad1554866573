package cn.hanyi.survey.core.dto;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
public class SurveySendRecordExtParamDto {

    private Long wechatConfigId;
    private String wechatAppId;
    private Map<String, String> customerParams;
    private Map<String, String> emailParams = new HashMap<>();

    public void addEmailParam(String key, String value) {
        emailParams.put(key, value);
    }
}
