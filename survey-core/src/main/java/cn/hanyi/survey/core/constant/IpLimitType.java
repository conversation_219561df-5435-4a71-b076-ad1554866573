package cn.hanyi.survey.core.constant;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;

public enum IpLimitType {
    ONCE,
    EVERY_DAY,
    EVERY_WEEK,
    EVERY_MONTH;

    /**
     * 不能在这个时间之后有记录
     */
    public LocalDateTime limitTime() {
        switch (this) {
            case EVERY_DAY -> {
                return LocalDate.now().atStartOfDay();
            }
            case EVERY_WEEK -> {
                return LocalDate.now().atStartOfDay().with(DayOfWeek.MONDAY);
            }
            case EVERY_MONTH -> {
                return LocalDate.now().atStartOfDay().with(TemporalAdjusters.firstDayOfMonth());
            }
            default -> {
                return LocalDateTime.MIN;
            }
        }
    }
}
