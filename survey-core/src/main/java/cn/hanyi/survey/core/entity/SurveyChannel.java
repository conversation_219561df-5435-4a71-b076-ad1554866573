package cn.hanyi.survey.core.entity;

import cn.hanyi.survey.core.constant.WechatAuthorizeType;
import cn.hanyi.survey.core.constant.WechatProvider;
import cn.hanyi.survey.core.constant.channel.ChannelStatus;
import cn.hanyi.survey.core.constant.channel.ChannelType;
import cn.hanyi.survey.core.dto.channel.ChannelDelayResendDto;
import cn.hanyi.survey.core.dto.ext.SurveyChannelExtDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.BaseEntity;
import org.befun.core.hibernate.types.JsonColumn;
import org.befun.core.rest.annotation.ResourceFieldCloneRule;
import org.befun.core.rest.view.ResourceViews;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Table(name = "survey_channel")
@Where(clause = "deleted=0")
@SQLDelete(sql = "UPDATE survey_channel SET deleted = 1 WHERE id=?")
@DtoClass(includeAllFields = true, superClass = SurveyChannelExtDto.class)
public class SurveyChannel extends BaseEntity {
    private static final long serialVersionUID = 6529685098267757690L;

    @Column(name = "s_id")
    @JsonIgnore
    private Long sid;

    @Size(max = 100, message = "名称超出100长度")
    @Schema(description = "渠道名称", required = true)
    @JsonView(ResourceViews.Basic.class)
    private String name = "";

    @Column(name = "type")
    @Enumerated
    @ResourceFieldCloneRule(ignore = true)
    @Schema(description = "渠道类型 0通用类型,1wechat短链接,2短信,3微信服务号,4页面嵌入,5调研家社区,6场景互动")
    @JsonView(ResourceViews.Basic.class)
    private ChannelType type = ChannelType.COMMON;

    @Column(name = "status")
    @Enumerated
    @ResourceFieldCloneRule(ignore = true)
    @Schema(description = "渠道状态 0:未设置，1:回收中，2:暂停，3:已完成，4:关闭，5:提交，6:驳回")
    @JsonView(ResourceViews.Basic.class)
    private ChannelStatus status = ChannelStatus.UNSET;

    @Column(name = "reject_message")
    @Schema(description = "驳回信息")
    @JsonView(ResourceViews.Basic.class)
    private String rejectMessage;

    @Schema(description = "回收数量", required = true)
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "recovery_amount")
    private Integer recoveryAmount = 0;

    @Column(name = "user_id")
    private Long userId;

    @Size(max = 20, message = "创建人名字超过20长度")
    @Schema(description = "创建人", required = true)
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "create_user")
    private String createUser = "";

    @JsonIgnore
    @Schema(description = "渠道是否删除")
    @Column(columnDefinition = "bit(1) default 0")
    private Boolean deleted = false;

    @Schema(description = "是否开启只能在微信填答限制")
    @Column(name = "enable_wechat_limit",columnDefinition = "bit(1) default 0")
    @JsonView(ResourceViews.Detail.class)
    private Boolean enableWechatLimit = false;

    @Column(name = "enable_ip_limit", columnDefinition = "bit(1) default 0")
    @Schema(description = "是否开启IP限制")
    @JsonView(ResourceViews.Detail.class)
    private Boolean enableIpLimit = false;

    @Column(name = "enable_device_limit", columnDefinition = "bit(1) default 0")
    @Schema(description = "是否开启设备限制")
    @JsonView(ResourceViews.Detail.class)
    private Boolean enableDeviceLimit = false;

    @Column(name = "enable_wechat_reply_only", columnDefinition = "bit(1) default 0")
    @Schema(description = "是否开启每个微信号只填答一次限制")
    @JsonView(ResourceViews.Detail.class)
    private Boolean enableWechatReplyOnly = false;

    @Column(name = "enable_wechat_authorization", columnDefinition = "bit(1) default 0")
    @Schema(description = "是否开启获取微信授权方式")
    @JsonView(ResourceViews.Detail.class)
    private Boolean enableWechatAuthorization = false;

    @Column(name = "wechat_provider")
    @Schema(description = "微信授权-授权方式")
    @JsonView(ResourceViews.Detail.class)
    private WechatProvider wechatProvider = WechatProvider.XM_PLUS;

    @Column(name = "wechat_authorize_type")
    @Schema(description = "微信授权-收集方式")
    @JsonView(ResourceViews.Detail.class)
    private WechatAuthorizeType wechatAuthorizeType = WechatAuthorizeType.SNSAPI_BASE;

    @Column(name = "enable_date_limit", columnDefinition = "bit(1) default 0")
    @Schema(description = "是否开启调查日期限制")
    @JsonView(ResourceViews.Detail.class)
    private Boolean enableDateLimit = false;

    @Temporal(TemporalType.TIMESTAMP)
    @JsonView(ResourceViews.Detail.class)
    @Schema(description = "问卷开始时间")
    @Column(name = "start_time")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @Temporal(TemporalType.TIMESTAMP)
    @JsonView(ResourceViews.Detail.class)
    @Schema(description = "问卷结束时间")
    @Column(name = "end_time")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @Temporal(TemporalType.TIMESTAMP)
    @JsonView(ResourceViews.Detail.class)
    @Schema(description = "问卷发送时间")
    @Column(name = "send_time")
    private Date sendTime;

    @Type(type = JsonColumn.TYPE)
    @JsonView(ResourceViews.Detail.class)
    @Schema(description = "重发时间")
    @Column(name = "resend_times")
    private List<ChannelDelayResendDto> resendTimes;

    //@Size(max = 10000, message = "名称超出10000长度")
    @Column(name = "configure", length = 65535)
    @Schema(description = "渠道配置信息，json格式")
    @JsonView(ResourceViews.Basic.class)
    private String configure = "";

//    @JsonIgnore
//    @Column(name = "task_type")
//    @Enumerated(EnumType.STRING)
//    private ChannelTaskType taskType = ChannelTaskType.NONE;
    @Column(name = "task_progress_id")
    private Long taskProgressId;

    @Schema(name = "订单报价方式：standard, manual")
    @Column(name = "order_pay_type")
    @JsonView(ResourceViews.Basic.class)
    private String orderPayType;

    @Column(name = "order_id")
    private Long orderId;

    @Column(name = "order_amount")
    @JsonView(ResourceViews.Basic.class)
    private Integer orderAmount;

    //  {
    //      "type": "full",
    //      "unitPrice": 1,
    //      "quantity": 101,
    //      "recycle": 100,
    //      "serviceRate": 0.06,
    //      "refundAmount": 100
    //  }
    @Column(name = "order_refund")
    @JsonView(ResourceViews.Basic.class)
    private String orderRefund;

}
