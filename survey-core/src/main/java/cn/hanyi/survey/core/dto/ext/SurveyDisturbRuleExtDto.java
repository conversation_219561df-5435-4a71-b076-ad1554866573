package cn.hanyi.survey.core.dto.ext;

import cn.hanyi.survey.core.entity.SurveyDisturbRule;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.projection.SimpleUser;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.rest.view.ResourceViews;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2023/1/3 11:30
 */
@Setter
@Getter
public class SurveyDisturbRuleExtDto extends BaseEntityDTO<SurveyDisturbRule> {

    public SurveyDisturbRuleExtDto() {
    }

    public SurveyDisturbRuleExtDto(SurveyDisturbRule entity) {
        super(entity);
    }

    @Schema(description = "修改人，可能为null")
    @JsonView(ResourceViews.Basic.class)
    private SimpleUser editorUser;

    @Schema(description = "创建人，可能为null")
    @JsonView(ResourceViews.Basic.class)
    private SimpleUser creator;
}
