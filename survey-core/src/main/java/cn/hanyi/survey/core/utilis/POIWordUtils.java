package cn.hanyi.survey.core.utilis;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.xwpf.usermodel.Document;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.befun.core.exception.BadRequestException;
import org.springframework.util.Assert;

import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.net.URL;

/**
 * <AUTHOR>
 * @date 2023/4/3 16:34:55
 */
public class POIWordUtils {


    public static void main(String[] args) {
        final String aaa = StringUtils.appendIfMissing("aaa", "<p>", "</p>");
        System.out.println(aaa);

    }


    public static XWPFRun addPicture(XWPFRun run, String pictureUrl, String filename) {
        Assert.notNull(pictureUrl, "url不能为null");

        URL url = null;
        InputStream is = null;
        try {
            url = new URL(pictureUrl);
            is = url.openConnection().getInputStream();
            run.addPicture(is, Document.PICTURE_TYPE_PNG, filename, 200, 200);
        } catch (MalformedURLException e) {
            throw new BadRequestException("pictureUrl 解析异常,pictureUrl=" + pictureUrl);
        } catch (IOException e) {
            throw new BadRequestException("获取图片异常,pictureUrl=" + pictureUrl);
        } catch (InvalidFormatException e) {
            throw new BadRequestException("添加图片异常,pictureUrl=" + pictureUrl);
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return run;
    }
}
