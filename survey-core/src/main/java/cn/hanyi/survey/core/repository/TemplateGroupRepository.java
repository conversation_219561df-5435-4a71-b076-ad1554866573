package cn.hanyi.survey.core.repository;


import cn.hanyi.survey.core.constant.Template.TemplateType;
import cn.hanyi.survey.core.entity.template.TemplateGroup;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TemplateGroupRepository extends ResourceRepository<TemplateGroup, Long> {

    List<TemplateGroup> findByType(TemplateType type, Sort sort);

    long countByType(TemplateType type);

}
