package cn.hanyi.survey.core.dto.lottery;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2022/11/15 17:37:54
 */
@Setter
@Getter
public class SendRedPackResult {

    @Schema(description = "是否可以领取")
    private Boolean isAllow = false;
    @Schema(description = "错误信息")
    private String errCode;
    @Schema(description = "是否审核通过")
    private Boolean isSuccess;
    @Schema(description = "是否需要审核")
    private Boolean isVerify = false;
    @Schema(description = "中奖金额")
    private int money;
}
