package cn.hanyi.survey.core.dto.channel;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/8/30 15:06
 */
@Getter
@Setter
public class SelectInterfaceDto {

    @Schema(description = "问卷id")
    private Long surveyId;

    @Schema(description = "渠道id")
    private Long channelId;

    @Schema(description = "发送方式，目前只支持SMS短信")
    private String type;

    @Schema(description = "消息库模版id")
    private Long thirdPartTemplateId;

    @Schema(description = "联系人")
    private List<UserContactDto> contacts;

    @Schema(description = "发送时间（yyyy-MM-dd HH:mm:ss）：定时发送有此参数")
    private String sendTime;

    @Schema(description = "模板内容")
    private Map<String, Object> content = new HashMap<>();

    public ChannelSendCustomerDto transform() {
        ChannelSendCustomerDto dto = new ChannelSendCustomerDto();
        dto.setSurveyId(surveyId);
        dto.setChannelId(channelId);
        dto.setThirdPartTemplateId(thirdPartTemplateId);
        dto.setContacts(contacts);
        dto.setSendTime(sendTime);
        dto.setContent(content);
        return dto;
    }

}
