package cn.hanyi.survey.core.dto.channel;

import cn.hanyi.survey.core.constant.ReplyStatus;
import cn.hanyi.survey.core.constant.channel.ChannelSendStatus;
import cn.hanyi.survey.core.constant.channel.ChannelStatus;
import cn.hanyi.survey.core.constant.survey.SendStatus;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.query.ResourceCustomQueryDto;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/11/10 16:19
 */
@Getter
@Setter
public class SurveySendRecordQueryDto extends ResourceCustomQueryDto {
    @JsonProperty("_q")
    @Schema(description = "关键字查询", example = "_q=abc")
    private String q = null;

    private SendStatus sendStatus;
    private ReplyStatus replyStatus;
    private ChannelSendStatus status;
}
