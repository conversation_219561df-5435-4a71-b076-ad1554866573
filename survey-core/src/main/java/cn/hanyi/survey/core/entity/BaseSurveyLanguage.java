package cn.hanyi.survey.core.entity;

import cn.hanyi.survey.core.constant.survey.TranslateStatus;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.persistence.Enumerated;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;

/**
 * <AUTHOR>
 * @date 2023/3/14 10:01:23
 */
@Getter
@Setter
@MappedSuperclass
public class BaseSurveyLanguage extends BaseEntity {
    @Schema(description = "语言")
    @Column(name = "language")
    @DtoProperty
    private String language;

    @Schema(description = "问卷别名")
    @Column(name = "title")
    @DtoProperty
    private String title = "";

    @Schema(description = "问卷标题")
    @Column(name = "real_title")
    @DtoProperty
    private String realTitle = "";

    @Schema(description = "欢迎语")
    @Column(name = "welcoming_remark")
    @DtoProperty
    private String welcomingRemark = "";

    @Schema(description = "正常结束语")
    @Column(name = "normal_finish_remark")
    @DtoProperty
    private String concludingRemark = "";

    @Schema(description = "非正常结束语")
    @Column(name = "abnormal_finish_remark")
    @DtoProperty
    private String abnormalConcludingRemark = "";

    @Schema(description = "个性化结束语")
    @Column(name = "personalized_remark")
    @JsonView(ResourceViews.Detail.class)
    private String personalizedRemarks = "";

    @Schema(description = "题目配置")
    @Column(name = "questions")
    @DtoProperty
    private String questions = "";

    @Schema(description = "翻译状态")
    @Column(name = "status")
    @DtoProperty
    @Enumerated
    private TranslateStatus status = TranslateStatus.UNTRANSLATED;

    @Schema(description = "原题目配置")
    @Column(name = "source_questions")
    @DtoProperty
    private String sourceQuestions = "";

}
