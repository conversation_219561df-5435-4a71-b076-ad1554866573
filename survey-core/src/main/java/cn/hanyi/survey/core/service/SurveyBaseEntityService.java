package cn.hanyi.survey.core.service;

import org.befun.core.entity.BaseEntity;
import org.befun.core.exception.EntityNotFoundException;
import org.befun.core.service.CrudService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class SurveyBaseEntityService {

    @Autowired
    private CrudService crudService;

    public <B extends BaseEntity> B require(Class<B> clazz, Long id) {
        return Optional.ofNullable(get(clazz, id)).orElseThrow(EntityNotFoundException::new);
    }

    public <B extends BaseEntity> B get(Class<B> clazz, Long id) {
        if (id == null || id <= 0) {
            return null;
        }
        return crudService.getRepository(clazz).findOne((root, query, builder) -> builder.equal(root.get("id"), id)).orElse(null);
    }
}
