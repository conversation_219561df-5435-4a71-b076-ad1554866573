package cn.hanyi.survey.core.exception;

import cn.hanyi.survey.core.constant.error.SurveyErrorCode;
import org.befun.core.exception.BaseException;

/**
 * The class description
 *
 * <AUTHOR>
 */
public class DateLimitException extends BaseException {

    public DateLimitException() {
        super(SurveyErrorCode.CHANNEL_STATUS_ERROR.getValue(), "问卷回收渠道状态错误");
    }

    public DateLimitException(String message) {
        super(SurveyErrorCode.CHANNEL_STATUS_ERROR.getValue(), message);
    }
}
