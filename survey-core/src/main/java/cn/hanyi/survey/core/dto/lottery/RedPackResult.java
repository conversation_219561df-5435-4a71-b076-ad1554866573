package cn.hanyi.survey.core.dto.lottery;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2022/11/9 15:54:39
 */
@Setter
@Getter
public class RedPackResult {
    @Schema(description = "是否中奖")
    private Boolean isWinner = false;
    @Schema(description = "是否需要审核")
    private Boolean isVerify;
    @Schema(description = "红包金额")
    private Integer money;
    @Schema(description = "中奖id")
    private Long winnerId;
    @Schema(description = "appid")
    private String appid;
    @Schema(description = "回调地址")
    private String redirectUrl;
    @Schema(description = "logo开关")
    private Boolean showBrand = false;
    @Schema(description = "logo地址")
    private String brandLogo;

}
