package cn.hanyi.survey.core.dto.ext;

import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.entity.SurveyChannelDto;
import cn.hanyi.survey.core.entity.template.TemplateSurvey;
import cn.hanyi.survey.core.entity.template.TemplateSurveyQuestionDto;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.projection.SimpleUser;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.rest.view.ResourceViews;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Getter
@Setter
public abstract class TemplateSurveyExtDto extends BaseEntityDTO<TemplateSurvey> {

    public TemplateSurveyExtDto() {
    }

    public TemplateSurveyExtDto(TemplateSurvey entity) {
        super(entity);
    }

    @Schema(description = "问题数量")
    @JsonView(ResourceViews.Basic.class)
    private Integer numOfQuestions;

    @JsonView(ResourceViews.Basic.class)
    private SimpleUser creator;

    @JsonView(ResourceViews.Basic.class)
    private LinkedHashMap<String, Object> cellData;

    @JsonView(ResourceViews.Basic.class)
    private SurveyChannelDto channel;

    public abstract List<TemplateSurveyQuestionDto> getQuestions();

    // 重写 numOfQuestions get
    public Integer getNumOfQuestions() {
        if (numOfQuestions == null) {
            numOfQuestions = Optional.ofNullable(getQuestions().stream().filter(q -> q.getType() != QuestionType.GROUP && q.getType() != QuestionType.SEPARATOR)
                    .collect(Collectors.toList())).map(List::size).orElse(0);
        }
        return numOfQuestions;
    }

}
