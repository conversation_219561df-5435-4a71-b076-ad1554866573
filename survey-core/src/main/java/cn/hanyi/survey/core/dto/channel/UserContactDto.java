package cn.hanyi.survey.core.dto.channel;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/8/30 16:29
 */
@Getter
@Setter
public class UserContactDto {
    @Schema(description = "手机号,短信渠道时必填")
    private String phone;

    @Schema(description = "邮箱,邮件渠道时必填")
    private String email;

    @Schema(description = "公众号appId,微信渠道时必填")
    private String wechatAppId;

    @Schema(description = "微信用户openId,微信渠道时必填")
    private String wechatOpenId;

    @Schema(description = "客户名称 {customer.username}, 可填")
    private String username;

    @Schema(description = "问卷参数系统参数,外部客户id, 可填")
    private String externalUserId;

    @Schema(description = "问卷参数系统参数,部门id， 可填")
    private Long departmentId;

    @Schema(description = "问卷参数系统参数,部门编号， 可填")
    private String departmentCode;

    @Schema(description = "问卷参数系统参数,链接过期时间， 可填")
    private String expireTime;

    @Schema(description = "问卷参数自定义参数, 可填")
    private Map<String, String> params = null;

    @Schema(description = "模版内容参数,发送问卷时替换模版占位符内容，可填")
    private Map<String, String> contentParams = null;
}
