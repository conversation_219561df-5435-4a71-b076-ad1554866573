package cn.hanyi.survey.core.dto.message;


import com.fasterxml.jackson.annotation.JsonView;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;
import org.befun.core.rest.view.ResourceViews;

import java.util.List;


@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class QuestionsItemsDto extends BaseDTO {
    @JsonView(ResourceViews.Basic.class)
    private Object value;
    @JsonView(ResourceViews.Basic.class)
    private List<QuestionsItemDto> items;
    @JsonView(ResourceViews.Basic.class)
    private List<QuestionsItemDto> columns;

    public QuestionsItemsDto(Object value) {
        this.value = value;
    }

}
