package cn.hanyi.survey.core.repository;

import cn.hanyi.survey.core.entity.template.TemplateGroup;
import cn.hanyi.survey.core.entity.template.TemplateSurveyQuestion;
import org.befun.core.repository.ResourceRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TemplateSurveyQuestionRepository extends ResourceRepository<TemplateSurveyQuestion, Long> {

    List<TemplateSurveyQuestion> findAllByOrgId(Long orgId);

    List<TemplateSurveyQuestion> findAllByGroup(TemplateGroup group);
}
