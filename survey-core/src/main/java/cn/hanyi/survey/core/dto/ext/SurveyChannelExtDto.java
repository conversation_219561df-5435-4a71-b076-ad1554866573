package cn.hanyi.survey.core.dto.ext;

import cn.hanyi.survey.core.entity.SurveyChannel;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.rest.view.ResourceViews;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/6/20 11:19
 */
@Getter
@Setter
public class SurveyChannelExtDto extends BaseEntityDTO<SurveyChannel> {

    public SurveyChannelExtDto() {
    }

    public SurveyChannelExtDto(SurveyChannel entity) {
        super(entity);
    }

    @Schema(description = "答题数量")
    @JsonView(ResourceViews.Basic.class)
    private Long numOfResponses = 0L;

    @Schema(description = "渠道订单状态：未设置、待报价(none)、待付款(init)、已付款(success)、已退款(refund)、退款失败(refund_failure)、退款中(refund_pending)")
    @JsonView(ResourceViews.Basic.class)
    private String orderStatus;
}
