befun:
  auth:
    pay:
      service-rate:
        wallet: ${PAY_SERVICE_RATE_WALLET:0.01}
        order_red_packet: ${PAY_SERVICE_RATE_ORDER_RED_PACKET:0.01}
        order_adminx_channel: ${PAY_SERVICE_RATE_ORDER_ADMINX_CHANNEL:0.06}
    recharge:
      enabled:
        recharge_wechat: true
        recharge_alipay: false
      expire-minute: ${AUTH_PAY_EXPIRE_MINUTE:120}
      mock-pay-url: ${AUTH_PAY_MOCK_URL:${xmplus.domain}/api/auth/organization/wallet/recharge/mockPay?rechargeId=%d&rechargeType=%s}
    order:
      sms:
        price: ${AUTH_PAY_SMS_PRICE:8}
    apps:
      - cem
    verify-code:
      code-cache-hours: 24
      code-expire-minutes: 5
      code-freeze-minutes: 1
      sms-template-name:
        survey-question-mobile: verify-code
    content-audit:
      enabled: true
      enable-audit-text: true
      enable-audit-image: true
      text-split-size: 6000
      text-retain-days: 15
      timeout-ms: 30000
      parallelism: 10
      app-id: ${AUTH_CONTENTAUDIT_APPID:41778716}
      api-key: ${AUTH_CONTENTAUDIT_APIKEY:mg1RNWKiAGYz5Ay49cLj1fGM}
      secret-key: ${AUTH_CONTENTAUDIT_SECRETKEY:q6XWhuse6U2kl6q3PDBuzcRE6mKc4rzq}
      image-url-pattern: https?://[-0-9a-zA-Z.:]+[-0-9a-zA-Z./_@#=?&%]*
      image-suffix: .png,.jpg,.jpeg,.bmp,.gif,.webp,.tiff,.svg
      access-token-wait-ms: 100
      access-token-wait-times: 10
      access-token-expire-day: 29