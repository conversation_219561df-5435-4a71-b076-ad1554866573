ALTER TABLE cem_platform.survey_language ADD status int NULL DEFAULT0 COMMENT '0:未翻译1:翻译中2:翻译完成3:翻译失败';
ALTER TABLE cem_platform.template_survey_language ADD status int NULL DEFAULT 0 COMMENT '0:未翻译1:翻译中2:翻译完成3:翻译失败';

INSERT INTO cem_platform.gpt_prompt (org_id,create_time,modify_time,prompt,variables,deleted,industry,`type`,config,llm) VALUES
(0,'2025-04-16 16:27:16','2025-06-27 11:30:59','请将以下{sourceLanguage}内容逐字翻译为{targetLanguage}，严格遵循以下规则：
格式处理原则：
保持原文JSON格式不变
保留所有换行符和空格格式

文本处理规范：
仅翻译标签间的自然语言文本
保持占位符/变量格式（如{{user_name}}、${date}）
维持特殊符号不变（&、#等符号实体）
保留数学公式、代码片段等非自然语言内容
原文JSON中的Key不需要翻译，仅翻译对象中的值
若文本包含富文本样式，则只翻译文本内容，保留富文本样式

翻译质量要求：
使用正式书面{targetLanguage}
保持问卷的问题逻辑一致性
专业术语需符合问卷调查领域规范
数字、计量单位自动转换（如"5公里"→"3.1 miles"）

需要翻译的内容如下：
{content}','',0,'health','TRANSLATE',NULL,'OPENAI');


INSERT INTO cem_platform.gpt_prompt (org_id,create_time,modify_time,prompt,variables,deleted,industry,`type`,config,llm) VALUES
(0,'2025-04-16 16:27:16','2025-06-27 11:30:59','请将以下{sourceLanguage}内容逐字翻译为{targetLanguage}，严格遵循以下规则：
格式处理原则：
保持原文JSON格式不变
保留所有换行符和空格格式

文本处理规范：
仅翻译标签间的自然语言文本
保持占位符/变量格式（如{{user_name}}、${date}）
维持特殊符号不变（&、#等符号实体）
保留数学公式、代码片段等非自然语言内容
原文JSON中的Key不需要翻译，仅翻译对象中的值
若文本包含富文本样式，则只翻译文本内容，保留富文本样式

翻译质量要求：
使用正式书面{targetLanguage}
保持问卷的问题逻辑一致性
专业术语需符合问卷调查领域规范
数字、计量单位自动转换（如"5公里"→"3.1 miles"）

需要翻译的内容如下：
{content}','',0,'health','TRANSLATE',NULL,'TONGYI');

ALTER TABLE survey_language ADD source_questions mediumtext NULL COMMENT '原题目配置';
ALTER TABLE template_survey_language ADD source_questions mediumtext NULL COMMENT '原题目配置';
