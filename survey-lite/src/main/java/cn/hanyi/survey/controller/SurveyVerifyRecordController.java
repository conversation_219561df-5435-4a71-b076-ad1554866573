package cn.hanyi.survey.controller;

import cn.hanyi.survey.dto.verifyRecord.VerifyRecordResultDto;
import cn.hanyi.survey.service.SurveyVerifyRecordService;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.view.ResourceViews;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/31 11:25:07
 */
@Tag(name = "问卷-审核")
@RestController
@PreAuthorize("isAuthenticated()")
@RequestMapping("/surveys/{sid}")
public class SurveyVerifyRecordController {

    @Autowired
    private SurveyVerifyRecordService verifyRecordService;

    @RequestMapping("/verifyRecords")
    @Operation(summary = "问卷审核记录")
    @JsonView(ResourceViews.Basic.class)
    public ResourceResponseDto<List<VerifyRecordResultDto>> findBySurveyId(@PathVariable Long sid) {
        return new ResourceResponseDto<>(verifyRecordService.findBySurveyId(sid));
    }
}





















