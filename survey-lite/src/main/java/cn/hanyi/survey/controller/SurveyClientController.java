package cn.hanyi.survey.controller;

import cn.hanyi.survey.core.dto.ext.SurveyEmbedExtDto;
import cn.hanyi.survey.core.entity.SurveyDto;
import cn.hanyi.survey.service.SurveyService;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.view.ResourceViews;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 答题端获取问卷 不需要鉴权
 *
 * <AUTHOR>
 */
@Tag(name = "问卷-客户端")
@RestController
@RequestMapping("/surveys")
public class SurveyClientController {

    @Autowired
    private SurveyService surveyService;

    @Operation(summary = "问卷预览")
    @GetMapping("{id}/start")
    public ResourceResponseDto<SurveyDto> start(@PathVariable Long id) {
        return new ResourceResponseDto<>(surveyService.findOne(id));
    }

    @RequestMapping("/{id}/embed")
    @JsonView(ResourceViews.Detail.class)
    @Operation(summary = "获取embed")
    public ResourceResponseDto<SurveyEmbedExtDto> getEmbed(@PathVariable Long id, @RequestParam Map<String, Object> params, @RequestBody(required = false) Map<String, Object> body) throws Exception {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        Optional.ofNullable(body).ifPresent(params::putAll);
        SurveyEmbedExtDto embed = surveyService.getEmbed(id, params, request);
        // 答题端获取嵌入配置的时候不要把黑白名暴露 , 直接返回空list
        embed.setEmbedCompanyId(List.of());
        embed.setEmbedUserId(List.of());
        return new ResourceResponseDto<>(embed);
    }
}
