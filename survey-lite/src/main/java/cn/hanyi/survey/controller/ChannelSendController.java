package cn.hanyi.survey.controller;

import cn.hanyi.survey.core.dto.channel.*;
import cn.hanyi.survey.core.entity.SurveyChannelDto;
import cn.hanyi.survey.core.entity.SurveySendRecordDto;
import cn.hanyi.survey.service.ChannelSendService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.rest.annotation.ResourceQueryPredicate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;


@Tag(name = "问卷-渠道-发送问卷")
@RestController
@PreAuthorize("isAuthenticated()")
@RequestMapping("/surveys")
public class ChannelSendController {

    @Autowired
    private ChannelSendService channelSendService;

    @Deprecated(since = "1.8.8")
    @PostMapping("/{surveyId}/channel/{channelId}/count-select-customers")
    @Operation(summary = "发布问卷（选择客户）-计算人数，消费短信条数")
    public ResourceResponseDto<CountSendDto> countSelectCustomers(@PathVariable long surveyId, @PathVariable long channelId,
                                                                  @RequestBody ChannelCountCustomerDto params) {
        return new ResourceResponseDto<>(channelSendService.countSelectCustomers(surveyId, channelId, params));
    }

    @Deprecated(since = "1.8.8")
    @PostMapping("/{surveyId}/channel/{channelId}/count-upload-customers")
    @Operation(summary = "发布问卷（上传文件）-计算人数，消费短信条数")
    public ResourceResponseDto<CountSendDto> countUploadCustomers(@PathVariable long surveyId, @PathVariable long channelId,
                                                                  @RequestBody ChannelCountCustomerDto params) {
        return new ResourceResponseDto<>(channelSendService.countSelectCustomers(surveyId, channelId, params));
    }

    @PostMapping("/{surveyId}/channel/{channelId}/count-customers")
    @Operation(summary = "发布问卷-计算人数，消费短信条数")
    public ResourceResponseDto<CountSendDto> countCustomers(@PathVariable long surveyId, @PathVariable long channelId,
                                                                  @RequestBody ChannelCountCustomerDto params) {
        return new ResourceResponseDto<>(channelSendService.countSelectCustomers(surveyId, channelId, params));
    }

    @Deprecated(since = "1.8.8")
    @PostMapping("/{surveyId}/channel/{channelId}/send-survey-by-phone")
    @Operation(summary = "发布问卷-手机短信")
    public ResourceResponseDto<SurveyChannelDto> sendSurveyByPhone(@PathVariable long surveyId,
                                                                   @PathVariable long channelId,
                                                                   @RequestBody ChannelSendCustomerDto params) {
        return new ResourceResponseDto<>(channelSendService.send(surveyId, channelId, params));
    }

    @Deprecated(since = "1.8.8")
    @PostMapping("/{surveyId}/channel/{channelId}/send-survey-by-wechat")
    @Operation(summary = "发布问卷-微信公众号")
    public ResourceResponseDto<SurveyChannelDto> sendSurveyByWeChat(@PathVariable long surveyId,
                                                                    @PathVariable long channelId,
                                                                    @RequestBody ChannelSendCustomerDto params) {
        return new ResourceResponseDto<>(channelSendService.send(surveyId, channelId, params));
    }

    @Deprecated(since = "1.8.8")
    @PostMapping("/{surveyId}/channel/{channelId}/send-survey-by-file")
    @Operation(summary = "发布问卷-上传文件，请使用发布问卷接口")
    public ResourceResponseDto<SurveyChannelDto> sendSurveyByFile(@PathVariable long surveyId,
                                                                  @PathVariable long channelId,
                                                                  @RequestBody ChannelSendCustomerDto params) {
        return new ResourceResponseDto<>(channelSendService.send(surveyId, channelId, params));
    }

    @PostMapping("/{surveyId}/channel/{channelId}/send-survey")
    @Operation(summary = "发布问卷")
    public ResourceResponseDto<SurveyChannelDto> sendSurvey(@PathVariable long surveyId,
                                                                  @PathVariable long channelId,
                                                                  @RequestBody ChannelSendCustomerDto params) {
        return new ResourceResponseDto<>(channelSendService.send(surveyId, channelId, params));
    }



    @PostMapping("/{surveyId}/channel/{channelId}/resend-survey/count-customers")
    @Operation(summary = "发布问卷-再次发送-计算人数，消费短信条数")
    public ResourceResponseDto<CountSendDto> countResendCustomers(@PathVariable long surveyId,
                                                                  @PathVariable long channelId,
                                                            @RequestBody ChannelCountResendCustomerDto params) {
        return new ResourceResponseDto<>(channelSendService.countResendCustomers(surveyId, channelId, params));
    }

    @PostMapping("/{surveyId}/channel/{channelId}/resend-survey")
    @Operation(summary = "发布问卷-再次发送")
    public ResourceResponseDto<Boolean> resendSurvey(@PathVariable long surveyId,
                                                     @PathVariable long channelId,
                                                     @RequestBody ResendDto params) {
        return new ResourceResponseDto<>(channelSendService.resendMsg(surveyId, channelId, params));
    }

}