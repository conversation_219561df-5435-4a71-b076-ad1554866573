package cn.hanyi.survey.controller;

import cn.hanyi.survey.dto.verifyRecord.VerifySurveyStatusResultDto;
import cn.hanyi.survey.service.SurveyVerifyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.auth.projection.SimpleUser;
import org.befun.core.dto.ResourceResponseDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/31 11:25:07
 */
@Tag(name = "问卷-审核")
@RestController
@PreAuthorize("isAuthenticated()")
@RequestMapping("/surveys/{surveyId}/")
public class SurveyVerifyController {

    @Autowired
    private SurveyVerifyService verifyService;

    @GetMapping("/verify/users/selected")
    @Operation(description = "已选择问卷审核人列表")
    public ResourceResponseDto<List<SimpleUser>> surveyVerifyList(@PathVariable Long surveyId) {
        return new ResourceResponseDto(verifyService.findVerifyUserList(surveyId));
    }

    @GetMapping("/status/verify")
    @Operation(description = "问卷状态和审核权限")
    public ResourceResponseDto<VerifySurveyStatusResultDto> getSurveyStatusAndVerify(@PathVariable Long surveyId) {
        return new ResourceResponseDto<>(verifyService.getSurveyStatusAndVerify(surveyId));
    }
}





















