package cn.hanyi.survey.controller.template;

import cn.hanyi.survey.core.entity.template.TemplateGroup;
import cn.hanyi.survey.core.entity.template.TemplateGroupDto;
import cn.hanyi.survey.core.repository.TemplateGroupRepository;
import cn.hanyi.survey.dto.template.GroupAddRequestDto;
import cn.hanyi.survey.dto.template.GroupUpdateRequestDto;
import cn.hanyi.survey.service.template.TemplateGroupService;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.dto.ResourceListResponseDto;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.dto.resource.ResourceCollectionType;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.ResourceMethodDto;
import org.befun.core.rest.annotation.ResourceQueryPredicate;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.rest.view.ResourceViews;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

import static org.befun.core.rest.annotation.processor.ResourceMethod.*;

@Tag(name = "模板-分组")
@Validated
@RestController
@RequestMapping("/templates-groups")
@PreAuthorize("isAuthenticated()")
@ResourceController(
        entityClass = TemplateGroup.class,
        repositoryClass = TemplateGroupRepository.class,
        serviceClass = TemplateGroupService.class,
        collectionType = ResourceCollectionType.COLLECTION_NO_PAGE,
        permission = "isAuthenticated()",
        excludeActions = {FIND_ONE, BATCH_UPDATE},
        methodDtoClass = {
                @ResourceMethodDto(method = CREATE, dtoClass = GroupAddRequestDto.class),
                @ResourceMethodDto(method = UPDATE_ONE, dtoClass = GroupUpdateRequestDto.class),
        },
        docCrud = "分组"
)
public class TemplateGroupController {

        @Autowired
        private TemplateGroupService templateGroupService;

        @Value("${survey.public-template-org-id:}")
        private Long publicTemplateOrgId;

        @GetMapping("/public")
        @Operation(summary = "公共模板-分组")
        @JsonView(ResourceViews.Basic.class)
        public ResourceListResponseDto<TemplateGroupDto> getPublicGroup(
                @ResourceQueryPredicate @Valid ResourceEntityQueryDto<TemplateGroupDto> params) {
                TenantContext.setCurrentTenant(publicTemplateOrgId);
                return new ResourceListResponseDto<>(templateGroupService.findAll(params));
        }
}