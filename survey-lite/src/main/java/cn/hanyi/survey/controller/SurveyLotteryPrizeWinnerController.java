package cn.hanyi.survey.controller;

import cn.hanyi.survey.service.SurveyLotteryWinnerStatisticsService;
import cn.hanyi.survey.core.dto.lottery.SurveyPrizeWinnerParam;
import cn.hanyi.survey.core.dto.lottery.SurveyPrizeWinnerResult;
import cn.hanyi.survey.core.entity.SurveyLotteryPrizeWinner;
import cn.hanyi.survey.service.SurveyLotteryPrizeWinnerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.dto.ResourceResponseDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2022/10/14 14:53:11
 */
@Tag(name = "问卷-抽奖-奖品-中奖")
@RestController
@PreAuthorize("isAuthenticated()")
public class SurveyLotteryPrizeWinnerController {

    @Autowired
    private SurveyLotteryWinnerStatisticsService winnerStatisticsService;
    @Autowired
    private SurveyLotteryPrizeWinnerService surveyLotteryPrizeWinnerService;


    @GetMapping("/surveys/{sid}/lottery/statistics")
    @Operation(summary = "奖励核销统计")
    public ResourceResponseDto<SurveyPrizeWinnerResult> statistics(@PathVariable Long sid,
                                                                   @RequestParam(value = "_page", defaultValue = "1") int page,
                                                                   @RequestParam(value = "_limit", defaultValue = "10") int limit,
                                                                   @RequestParam(value = "_sort", defaultValue = "createTime_desc") String sort,
                                                                   SurveyPrizeWinnerParam data) {
        return new ResourceResponseDto(winnerStatisticsService.prizeWinnerStatistics(sid, page, limit, sort, data));
    }

    @PutMapping("/surveys/{sid}/lottery/prize/winner/{prizeWinnerId}")
    @Operation(summary = "修改奖品发放状态")
    public ResourceResponseDto<SurveyLotteryPrizeWinner> updatePrizeWinnerSendStatus(@PathVariable Long sid, @PathVariable Long prizeWinnerId, @RequestBody SurveyLotteryPrizeWinner prizeWinner) {
        return new ResourceResponseDto<>(surveyLotteryPrizeWinnerService.updatePrizeWinnerSendStatus(sid, prizeWinnerId, prizeWinner.getStatus()));
    }


    @DeleteMapping("/surveys/{sid}/lottery/prize/winner/{prizeWinnerId}")
    @Operation(summary = "删除中奖")
    public ResourceResponseDto<Boolean> deletePrizeWinner(@PathVariable Long sid, @PathVariable Long prizeWinnerId) {
        return new ResourceResponseDto<>(surveyLotteryPrizeWinnerService.deletePrizeWinner(sid, prizeWinnerId));
    }

    @PostMapping("/surveys/{sid}/lottery/prize/winner/download")
    @Operation(summary = "奖励核销下载")
    public void downloadStatisticsData(HttpServletResponse response, @PathVariable Long sid, @RequestBody SurveyPrizeWinnerParam data) {
        winnerStatisticsService.downloadStatisticsData(sid, response, data);
    }
}















