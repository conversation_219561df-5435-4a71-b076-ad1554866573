package cn.hanyi.survey.controller;

import cn.hanyi.survey.core.constant.ResponseStatus;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.repository.SurveyRepository;
import cn.hanyi.survey.core.repository.SurveyResponseRepository;
import cn.hanyi.survey.service.SurveyService;
import com.alibaba.fastjson.JSONValidator;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Controller
public class UpdateController {

	@Autowired
	private SurveyService surveyService;

	@Autowired
	private SurveyRepository surveyRepository;
	@Autowired
	private SurveyResponseRepository responseRepository;

	@Autowired
	private JdbcTemplate jdbcTemplate;

	private static final Pattern compile = Pattern.compile("<.*>", Pattern.DOTALL);


	@GetMapping("/syncNumOfResponse")
	@Operation(summary = "同步答卷有效数量")
	public ResourceResponseDto<Boolean> syncNumOfResponse() {
		/**
		 * 同步问卷的提交数量到问卷表response_finish_num中
		 */
		ExecutorService executorService = Executors.newFixedThreadPool(10);
		int page = 0;
		int limit = 100;
		Integer count = jdbcTemplate.queryForObject("select count(*) from survey where deleted = 0", Integer.class);
		int size = count % limit == 0 ? count / limit : (count / limit) + 1;
		long id = 0L;
		CountDownLatch latch = new CountDownLatch(size);
		for (int i = 0; i < size; i++) {
			List<Survey> surveyList = surveyRepository.findAllSurvey(id, page, limit);
			id = surveyList.get(surveyList.size() - 1).getId();
			try {
				executorService.execute(() -> {
					for (Survey survey : surveyList) {
						Long sid = survey.getId();
						long responseNum = responseRepository.countBySurveyIdAndStatusForceIndex(sid, ResponseStatus.FINAL_SUBMIT.ordinal());
						String sql = String.format("update survey set response_finish_num = %s where id = %s", responseNum, sid);
						jdbcTemplate.batchUpdate(sql);
					}
				});
			} finally {
				latch.countDown();
			}
		}
		try {
			latch.await();
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
		executorService.shutdown();

		return new ResourceResponseDto<>(true);
	}


	@GetMapping("/update-finish-remark")
	@ResponseBody
	public String changeJson() {
		//查询json解析失败的正常结束页
		String sql1 = "SELECT id, normal_finish_remark from survey where JSON_VALID(normal_finish_remark)=0 and deleted=0";
		List<Map<String, Object>> list1 = jdbcTemplate.queryForList(sql1);
		for (Map map : list1) {
			try {
				Object normal_finish_remark = map.get("normal_finish_remark");
				if (Objects.nonNull(normal_finish_remark)) {
					String normalFinishRemark = getParseJson(normal_finish_remark.toString());
					if (JSONValidator.from(normalFinishRemark).validate()) {
						Survey survey = surveyService.requireSurvey((Long) map.get("id"));
						survey.setConcludingRemark(normalFinishRemark);
						surveyService.save(survey);
					}
				}
			} catch (Exception e) {
				log.error("update-finish-remark error,data:{},message:{}", JsonHelper.toJson(map), e.getMessage());
			}
		}

		//查询json解析失败的非正常结束页
		String sql2 = "SELECT id, abnormal_finish_remark from survey where JSON_VALID(abnormal_finish_remark)=0 and deleted=0";
		List<Map<String, Object>> list2 = jdbcTemplate.queryForList(sql2);
		for (Map map : list2) {
			try {
				Object abnormal_finish_remark = map.get("abnormal_finish_remark");
				if (Objects.nonNull(abnormal_finish_remark)) {
					String abnormalFinishRemark = getParseJson(abnormal_finish_remark.toString());
					if (JSONValidator.from(abnormalFinishRemark).validate()) {
						Survey survey = surveyService.requireSurvey((Long) map.get("id"));
						survey.setAbnormalConcludingRemark(abnormalFinishRemark);
						surveyService.save(survey);
					}
				}
			} catch (Exception e) {
				log.error("update-finish-remark error,data:{},message:{}", JsonHelper.toJson(map), e.getMessage());
			}
		}
		return "end";
	}

	public String getParseJson(String finishRemark) {
		if (finishRemark == null || finishRemark.isEmpty()) {
			return null;
		}
		Matcher matcher = compile.matcher(finishRemark);
		StringBuilder sb = new StringBuilder();
		while (matcher.find()) {
			String res = matcher.group();
			matcher.appendReplacement(sb, res.replace("\"", "\\\\\""));
		}
		matcher.appendTail(sb);
		return sb.toString();
	}

}
