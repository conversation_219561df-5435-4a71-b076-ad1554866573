package cn.hanyi.survey.controller.template;

import cn.hanyi.survey.core.entity.SurveyDto;
import cn.hanyi.survey.core.entity.template.TemplateSurvey;
import cn.hanyi.survey.core.entity.template.TemplateSurveyDto;
import cn.hanyi.survey.core.repository.TemplateSurveyRepository;
import cn.hanyi.survey.dto.template.SaveTemplateRequestDto;
import cn.hanyi.survey.dto.template.TemplateSurveyQueryDto;
import cn.hanyi.survey.dto.template.UpdateTemplateRequestDto;
import cn.hanyi.survey.dto.template.UseTemplateRequestDto;
import cn.hanyi.survey.service.SurveyService;
import cn.hanyi.survey.service.template.TemplateSurveyService;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.dto.ResourcePageResponseDto;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.ResourceMethodDto;
import org.befun.core.rest.annotation.ResourceQueryCustom;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.rest.view.ResourceViews;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static org.befun.core.rest.annotation.processor.ResourceMethod.*;


@Tag(name = "模板-问卷库")
@Validated
@RestController
@RequestMapping("/templates-surveys")
@PreAuthorize("isAuthenticated()")
@ResourceController(
        entityClass = TemplateSurvey.class,
        repositoryClass = TemplateSurveyRepository.class,
        serviceClass = TemplateSurveyService.class,
        permission = "isAuthenticated()",
        excludeActions = {CREATE, BATCH_UPDATE, UPDATE_ONE},
        methodDtoClass = {
                @ResourceMethodDto(method = FIND_ALL, dtoClass = TemplateSurveyQueryDto.class),
        },
        docCrud = "问卷模板"
)
public class TemplateGroupSurveyController {

    @Autowired
    private SurveyService surveyService;
    @Autowired
    private TemplateSurveyService templateSurveyService;

    @Value("${survey.public-template-org-id:}")
    private Long publicTemplateOrgId;

    @JsonView(ResourceViews.Basic.class)
    @PostMapping("save-as-template")
    @Operation(summary = "保存为模板")
    public ResourceResponseDto<TemplateSurveyDto> saveTemplate(@Valid @RequestBody SaveTemplateRequestDto dto) {
        return new ResourceResponseDto<>(templateSurveyService.saveTemplate(dto));
    }

    @JsonView(ResourceViews.Basic.class)
    @PostMapping("{templateId}/use-template")
    @Operation(summary = "使用模板创建问卷")
    public ResourceResponseDto<SurveyDto> useTemplate(@PathVariable("templateId") long templateId, @RequestBody UseTemplateRequestDto dto) {
//        surveyService.checkSurveyLimit();
        return new ResourceResponseDto<>(templateSurveyService.useTemplate(templateId, dto.getGroupId()));
    }

    @PostMapping("{templateId}/update-template")
    @Operation(summary = "修改模板")
    public ResourceResponseDto<Boolean> updateTemplate(@PathVariable("templateId") long templateId, @RequestBody UpdateTemplateRequestDto dto) {
        return new ResourceResponseDto<>(templateSurveyService.updateTemplate(templateId, dto.getGroupId(), dto.getTemplateName()));
    }

    @GetMapping("/public")
    @Operation(summary = "公共模板-问卷库")
    @JsonView(ResourceViews.Basic.class)
    public ResourcePageResponseDto<TemplateSurveyDto> getPublicTemplateSurveyList(
            @ResourceQueryCustom TemplateSurveyQueryDto params) {
        TenantContext.setCurrentTenant(publicTemplateOrgId);
        return new ResourcePageResponseDto(templateSurveyService.findAll(params));
    }

    @GetMapping("/public/{id}")
    @Operation(summary = "公共模板库单个问卷模板")
    @JsonView(ResourceViews.Detail.class)
    public ResourceResponseDto<TemplateSurveyDto> getPublicTemplateSurveyById(@PathVariable long id) {
        TenantContext.setCurrentTenant(publicTemplateOrgId);
        return new ResourceResponseDto(templateSurveyService.findOne(id));
    }

}