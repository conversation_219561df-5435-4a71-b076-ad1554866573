package cn.hanyi.survey.controller;

import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.service.SurveyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.auth.dto.DepartmentTreeDto;
import org.befun.auth.service.DepartmentService;
import org.befun.core.dto.ResourceResponseDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/4/2 2:22 下午
 */
@Tag(name = "组织结构")
@RestController
@RequestMapping("/surveys/department/")
public class DepartmentController {

    @Autowired
    private SurveyService surveyService;
    @Autowired
    private DepartmentService departmentService;

    @GetMapping("{id}/tree")
    @Operation(summary = "根据问卷id获取部门树结构")
    @Tag(
            name = "组织结构"
    )
    public ResourceResponseDto<List<DepartmentTreeDto>> tree(@PathVariable long id) {
        Survey survey = surveyService.requireSurvey(id);
        DepartmentTreeDto root = departmentService.treeByOrg(survey.getOrgId());
        return new ResourceResponseDto<>(root == null ? List.of() : List.of(root));
    }
}
