package cn.hanyi.survey.controller;

import cn.hanyi.survey.core.repository.SurveyLotteryPrizeRepository;
import cn.hanyi.survey.service.SurveyLotteryPrizeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/9/21 15:16:00
 */
@Tag(name = "问卷-抽奖-奖品")
@RestController
public class SurveyLotteryPrizeController extends BaseController<SurveyLotteryPrizeService> {

    @Autowired
    private SurveyLotteryPrizeService surveyLotteryPrizeService;
    @Autowired
    private SurveyLotteryPrizeRepository surveyLotteryPrizeRepository;
    @Autowired
    private StringRedisTemplate redisTemplate;

    @PostMapping("/lottery/{lotteryId}/prizes/{prizeId}/clear")
    @Operation(summary = "清除奖品信息")
    public ResourceResponseDto<Boolean> clearPrizes(@PathVariable Long lotteryId, @PathVariable Long prizeId) {
        return new ResourceResponseDto<>(surveyLotteryPrizeService.clearPrizes(prizeId));
    }
}















