package cn.hanyi.survey.controller;

import cn.hanyi.survey.dto.qrcode.DepartmentQrCodeDto;
import cn.hanyi.survey.service.SurveyQrCodeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.dto.ResourceResponseDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <AUTHOR>
 */
@Tag(name = "问卷-二维码")
@RestController
@RequestMapping("/surveys")
public class SurveyQrCodeController {

    @Autowired
    SurveyQrCodeService surveyQrCodeService;


    @GetMapping("/{id}/department-qrcode")
    @Operation(summary = "一店一码列表")
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<List<DepartmentQrCodeDto>> departmentQrcode(@PathVariable long id) {
        return surveyQrCodeService.departmentQrcode(id);
    }

    @GetMapping("/{id}/download-qrcode")
    @Operation(summary = "下载通用链接二维码")
    @PreAuthorize("isAuthenticated()")
    public void downloadSurveyQrcode(HttpServletResponse response, @PathVariable long id,
                                     @Parameter(description = "二维码宽度：px，默认300px") @RequestParam(defaultValue = "300") Integer width,
                                     @Parameter(description = "二维码高度：px，默认300px") @RequestParam(defaultValue = "300") Integer height) throws Exception {
        surveyQrCodeService.downloadSurveyQrcode(response, id, width, height);
    }

    @GetMapping("/{id}/departments/download-qrcode")
    @Operation(summary = "批量下载部门短链接二维码")
    @PreAuthorize("isAuthenticated()")
    public void downloadDepartmentQrcode(HttpServletResponse response, @PathVariable long id,
                                         @Parameter(description = "二维码宽度：px，默认300px") @RequestParam(defaultValue = "300") Integer width,
                                         @Parameter(description = "二维码高度：px，默认300px") @RequestParam(defaultValue = "300") Integer height) throws Exception {
        surveyQrCodeService.downloadDepartmentQrcode(response, id, width, height);
    }

    @GetMapping("/{id}/departments/{department_id}/download-qrcode")
    @Operation(summary = "下载部门短链接二维码")
    @PreAuthorize("isAuthenticated()")
    public void downloadDepartmentQrcode(HttpServletResponse response, @PathVariable long id,
                                         @PathVariable long department_id,
                                         @Parameter(description = "二维码宽度：px，默认300px") @RequestParam(defaultValue = "300") Integer width,
                                         @Parameter(description = "二维码高度：px，默认300px") @RequestParam(defaultValue = "300") Integer height) throws Exception {
        surveyQrCodeService.downloadDepartmentQrcode(response, id, department_id, width, height);
    }


}