package cn.hanyi.survey.controller;

import cn.hanyi.survey.core.entity.SurveyChannelDto;
import cn.hanyi.survey.dto.survey.SurveyPlusConfigureDto;
import cn.hanyi.survey.service.ChannelService;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.view.ResourceViews;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.Map;


@Tag(name = "问卷-渠道")
@Validated
@RestController
@RequestMapping("/surveys/{surveyId}/channel")
public class ChannelController {

    @Autowired
    ChannelService channelService;

//    @PostMapping
//    @JsonView(ResourceViews.Detail.class)
//    @Operation(
//            summary = "create",
//            description = "新建渠道"
//    )
//    @PreAuthorize("isAuthenticated()")
//    public ResourceResponseDto<SurveyChannel> create(@PathVariable long surveyId,
//                                                     @RequestBody SurveyChannel data) {
//        return channelService.create(surveyId, data);
//    }
//
//    @GetMapping
//    @JsonView(ResourceViews.Basic.class)
//    @Operation(
//            summary = "findAll",
//            description = "获取问卷所有的渠道"
//    )
//    public ResourcePageResponseDto<SurveyChannel> findAll(@PathVariable long surveyId, @RequestParam Map<String, Object> params) {
//        return channelService.getAllChannelList(surveyId, params);
//    }
//
//    @GetMapping("/{channelId}")
//    @JsonView(ResourceViews.Detail.class)
//    @Operation(
//            summary = "findOne",
//            description = "获取问卷渠道详情"
//    )
//    public ResourceResponseDto<SurveyChannel> findOne(@PathVariable long surveyId, @PathVariable long channelId) {
//        return channelService.getOneChannel(surveyId, channelId);
//    }
//
//    @PutMapping("/{channelId}")
//    @JsonView(ResourceViews.Detail.class)
//    @Operation(
//            summary = "updateOne"
//    )
//    @PreAuthorize("isAuthenticated()")
//    public ResourceResponseDto<SurveyChannel> updateOne(@PathVariable long surveyId, @PathVariable long channelId,
//                                                        @RequestBody Map<String, Object> data) {
//        return super.updateOne(channelId, data);
//    }

    @DeleteMapping("/{channelId}/delete")
    @JsonView(ResourceViews.Basic.class)
    @Operation(summary = "删除渠道")
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<Boolean> delete(@PathVariable long surveyId, @PathVariable long channelId) {
        channelService.deleteOneEmbeddedMany(surveyId, null, channelId);
        return new ResourceResponseDto<>(true);
    }


    @PutMapping("/{channelId}/pause")
    @JsonView(ResourceViews.Basic.class)
    @Operation(
            summary = "暂停问卷渠道",
            description = "暂停问卷渠道"
    )
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto pause(@PathVariable long surveyId, @PathVariable long channelId) {
        return channelService.pause(surveyId, channelId);
    }

    @PutMapping("/{channelId}/close")
    @JsonView(ResourceViews.Basic.class)
    @Operation(
            summary = "关闭问卷渠道",
            description = "关闭问卷渠道"
    )
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto close(@PathVariable long surveyId, @PathVariable long channelId) {
        return channelService.close(surveyId, channelId);
    }

    @PutMapping("/{channelId}/restart")
    @JsonView(ResourceViews.Basic.class)
    @Operation(
            summary = "重启问卷渠道",
            description = "重启问卷渠道"
    )
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto restart(@PathVariable long surveyId, @PathVariable long channelId) {
        return channelService.restart(surveyId, channelId);
    }

    @DeleteMapping("/{channelId}/wipe-data")
    @JsonView(ResourceViews.Basic.class)
    @Operation(
            summary = "清空渠道填答数据",
            description = "清空渠道填答数据"
    )
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto wipeData(@PathVariable long surveyId, @PathVariable long channelId) {
        return channelService.wipeData(surveyId, channelId);
    }

    @PutMapping("/{channelId}")
    @Operation(
            summary = "渠道"
    )
    @JsonView(ResourceViews.Detail.class)
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<SurveyChannelDto> updateOne(@PathVariable long surveyId, @PathVariable long channelId,
                                                           @RequestBody Map<String, Object> data) {
        return new ResourceResponseDto(channelService.updateOne(surveyId, channelId, data));
    }

    @PostMapping("/{channelId}/create-order")
    @Operation(
            summary = "创建社区调研订单"
    )
    @JsonView(ResourceViews.Detail.class)
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<SurveyChannelDto> createOrder(@NotNull @PathVariable long surveyId, @NotNull @PathVariable long channelId,
                                                             @RequestBody SurveyPlusConfigureDto data) {
        return new ResourceResponseDto(channelService.createSurveyPlusOrder(surveyId, channelId, data));
    }

    @PostMapping("/{channelId}/order-setting")
    @Operation(
            summary = "社区调研订单拦截设置"
    )
    @JsonView(ResourceViews.Detail.class)
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto orderSetting(@NotNull @PathVariable long surveyId, @NotNull @PathVariable long channelId) {
        channelService.orderSetting(surveyId, channelId);
        return new ResourceResponseDto();
    }

    @GetMapping("/{channelId}/download-embed")
    @Operation(
            summary = "下载嵌入问卷设置的用户id名单",
            description = "下载名单"
    )
    @PreAuthorize("isAuthenticated()")
    public void download(HttpServletResponse response, @PathVariable long surveyId, @PathVariable long channelId) throws IOException {
        channelService.download(response, surveyId, channelId);
    }

}