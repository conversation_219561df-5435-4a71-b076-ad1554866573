package cn.hanyi.survey.controller.open;

import cn.hanyi.survey.core.dto.channel.OpenApiCreateWechatChannelDto;
import cn.hanyi.survey.core.dto.channel.OpenApiSendWechatDto;
import cn.hanyi.survey.core.dto.channel.SelectInterfaceDto;
import cn.hanyi.survey.core.entity.SurveyChannel;
import cn.hanyi.survey.core.entity.SurveyChannelDto;
import cn.hanyi.survey.service.ChannelSendService;
import cn.hanyi.survey.service.ChannelService;
import cn.hanyi.survey.service.SurveyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.dto.ResourceResponseDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Tag(name = "开放接口-渠道问卷")
@Validated
@RestController
@PreAuthorize("isAuthenticated()")
public class OpenChannelSendController {

    @Autowired
    private SurveyService surveyService;
    @Autowired
    private ChannelSendService channelSendService;

    @PostMapping("surveys/batch-send")
    @Operation(summary = "发布问卷-json")
    public ResourceResponseDto<SurveyChannelDto> batchSend(@RequestBody SelectInterfaceDto data) {
        return new ResourceResponseDto<>(channelSendService.send(data.getSurveyId(), data.getChannelId(), data.transform()));
    }

    @PostMapping("open/surveys/channel/wechat/create")
    @Operation(summary = "微信渠道-创建")
    public ResourceResponseDto<SurveyChannelDto> createWechatChannel(@Valid @RequestBody OpenApiCreateWechatChannelDto data) {
        return new ResourceResponseDto<>(
                surveyService.createEmbeddedMany(
                        data.getSurveyId(),
                        "channel",
                        SurveyChannel.class,
                        SurveyChannelDto.class,
                        data.transform()));
    }

    @PostMapping("open/surveys/channel/wechat/send")
    @Operation(summary = "微信渠道-发送问卷（请求体参数总长度不超过60000字节）")
    public ResourceResponseDto<SurveyChannelDto> sendWechatChannel(@Valid @RequestBody OpenApiSendWechatDto data) {
        return new ResourceResponseDto<>(channelSendService.send(data.getSurveyId(), data.getChannelId(), data.transform()));
    }
}
