package cn.hanyi.survey.controller;

import cn.hanyi.ctm.service.CustomerService;
import cn.hanyi.survey.core.constant.channel.ChannelType;
import cn.hanyi.survey.core.dto.task.UploadFileDto;
import cn.hanyi.survey.service.channel.ChannelSendFileHelper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.dto.ResourceResponseDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Tag(name = "文件上传")
@RestController
@PreAuthorize("isAuthenticated()")
public class FileContentController {


    @Autowired
    private ChannelSendFileHelper channelSendFileHelper;
    @Autowired
    private CustomerService customerService;
    @Value("${survey.send-manage-file-max:5}")
    private Long sendManageFileMax;

    @Deprecated
    @Operation(summary = "上传发送问卷的客户信息-短信渠道")
    @RequestMapping(value = "/files/send-survey-csv-file", method = RequestMethod.POST)
    public ResourceResponseDto<UploadFileDto> uploadSurvey(@RequestParam("file") MultipartFile file) {
        return channelSendFileHelper.uploadSurvey0(ChannelType.PHONE_MSG, file);
    }

    @Operation(summary = "上传发送问卷的客户信息-短信渠道")
    @RequestMapping(value = "/files/send-survey-csv-file/sms", method = RequestMethod.POST)
    public ResourceResponseDto<UploadFileDto> uploadSurvey1(@RequestParam("file") MultipartFile file) {
        return channelSendFileHelper.uploadSurvey0(ChannelType.PHONE_MSG, file);
    }

    @Operation(summary = "上传发送问卷的客户信息-邮件渠道")
    @RequestMapping(value = "/files/send-survey-csv-file/email", method = RequestMethod.POST)
    public ResourceResponseDto<UploadFileDto> uploadSurvey2(@RequestParam("file") MultipartFile file) {
        return channelSendFileHelper.uploadSurvey0(ChannelType.EMAIL, file);
    }

    @Operation(summary = "上传发送管理的客户信息")
    @RequestMapping(value = "/files/send-manage-file", method = RequestMethod.POST)
    public ResourceResponseDto<UploadFileDto> uploadCustomer3(@RequestParam("file") MultipartFile file) {
        UploadFileDto dto = new UploadFileDto();
        LinkedHashMap<String, String> headers = new LinkedHashMap<>();
        List<LinkedHashMap<String, Object>> customers = new ArrayList<>();
        customerService.uploadSendCustomerFile(sendManageFileMax, file, headers,
                dto::setUrl,
                customer -> {
                    LinkedHashMap<String, Object> map = new LinkedHashMap<>();
                    map.put("username", customer.getUsername());
                    map.put("externalUserId", customer.getExternalUserId());
                    map.put("mobile", customer.getMobile());
                    map.put("email", customer.getEmail());
                    map.put("appId", customer.getWechatParams().getAppId());
                    map.put("openId", customer.getWechatParams().getOpenId());
                    map.putAll(customer.getUrlCustomParams());
                    customers.add(map);
                });
        dto.setHeaders(headers);
        dto.setCustomers(customers);
        return new ResourceResponseDto<>(dto);
    }

}
