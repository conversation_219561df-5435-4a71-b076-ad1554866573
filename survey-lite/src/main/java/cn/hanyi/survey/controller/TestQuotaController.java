package cn.hanyi.survey.controller;

import cn.hanyi.survey.service.quota.QuotaAllMatchLimit;
import cn.hanyi.survey.service.quota.bean.QuotaInit;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@RestController
@RequestMapping("test/quota")
public class TestQuotaController {
    @Autowired
    private QuotaAllMatchLimit quotaLimit;
    @Autowired
    private ObjectMapper objectMapper;

    @Getter
    @Setter
    public static class QuotaInitParam {
        private List<QuotaInit> init;
        private List<Long> sync;
        private long count;
    }

    @PostMapping("sync")
    public String test(@RequestBody QuotaInitParam params) {
//        quotaLimit.syncQuotaStat(1L,
//                params.init,
//                params.sync,
//                params.count,
//                (Long surveyId, List<Long> quotaIds, int page, int size) -> {
//                    int step = (page - 1) * size;
//                    int range = (int) Math.min(params.count, size);
//                    Map<Long, List<Long>> r = new HashMap<>();
//                    quotaIds.forEach(q -> {
//                        List<Long> rids = IntStream.range(0, range).parallel().filter(i -> RandomUtils.nextBoolean()).mapToObj(j -> (long) (j + step)).collect(Collectors.toList());
//                        r.put(q, rids);
//                    });
//                    return r;
//                },
//                (success, stat) -> {
//                    try {
//                        String stats = objectMapper.writeValueAsString(stat);
//                        log.info("sync: success={}, stat={}", success, stats);
//                    } catch (JsonProcessingException e) {
//                        e.printStackTrace();
//                    }
//                });
        return "ok";
    }

    @GetMapping("stat")
    public String stat() {
        try {
            return objectMapper.writeValueAsString(quotaLimit.getQuotaStat(1L));
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return "error";
    }

    @GetMapping("has")
    public String has(String qIds) {
        try {
            return objectMapper.writeValueAsString(quotaLimit.hasQuota(1L,Arrays.stream(qIds.split(",")).map(Long::valueOf).collect(Collectors.toList())));
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return "error";
    }

//    @PostMapping("use")
//    public String use(Long rId, String qIds) {
//        boolean r = quotaLimit.useQuota(1L, rId, Arrays.stream(qIds.split(",")).map(Long::valueOf).collect(Collectors.toList()));
//        return r + "";
//    }
//
//    @PostMapping("del")
//    public String delete(Long rId) {
//        boolean r = quotaLimit.deleteResponse(1L, rId);
//        return r + "";
//    }

//    @GetMapping("progress")
//    public String progress() {
//        try {
//            return objectMapper.writeValueAsString(quotaLimit.syncProgress(1L));
//        } catch (JsonProcessingException e) {
//            e.printStackTrace();
//        }
//        return "error";
//    }

    @PostMapping("token")
    public String quotas(Long sid, Long eid) {
        try {
            BufferedWriter out = new BufferedWriter(new FileWriter(String.format("/Users/<USER>/Downloads/quota/%d.txt",eid)));
            Optional.ofNullable(quotaLimit.getQuotaToken(sid, eid)).ifPresent(result ->{
                result.forEach(i -> {
                    try {
                        out.write(i.toString() + "\n");
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                });
            });
            out.close();
            return "success";
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "error";
    }
}
