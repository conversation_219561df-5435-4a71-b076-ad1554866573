package cn.hanyi.survey.controller;

import cn.hanyi.survey.core.dto.quota.SurveyQuotaBatchOptionRequestDto;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyQuotaDto;
import cn.hanyi.survey.service.QuotaService;
import cn.hanyi.survey.service.SurveyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.dto.ResourceListResponseDto;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.exception.BadRequestException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "问卷-配额")
@RestController
@PreAuthorize("isAuthenticated()")
@RequestMapping("/surveys/{surveyId}/quotas")
public class QuotaController {

    @Autowired
    private QuotaService quotaService;

    @Autowired
    private SurveyService surveyService;

    @PostMapping(path = "/batch-create")
    @Operation(summary = "批量增加")
    public ResourceListResponseDto<SurveyQuotaDto> batchCreateSurveyQuota(@PathVariable long surveyId,
            @RequestBody SurveyQuotaBatchOptionRequestDto quotaDtoList) {
        return quotaService.batchCreate(surveyId, quotaDtoList);
    }

    @PostMapping(path = "/batch-delete")
    @Operation(summary = "批量删除")
    public ResourceResponseDto<Boolean> batchDeleteSurveyQuota(@PathVariable long surveyId,
            @RequestBody SurveyQuotaBatchOptionRequestDto quotaDtoList) {
        return quotaService.batchDelete(surveyId, quotaDtoList);
    }

    @PostMapping(path = "/batch-update")
    @Operation(summary = "批量更新")
    public ResourceListResponseDto<SurveyQuotaDto> batchUpdateSurveyQuota(@PathVariable long surveyId,
            @RequestBody SurveyQuotaBatchOptionRequestDto quotaDtoList) {
        return quotaService.batchUpdate(surveyId, quotaDtoList);
    }

    @PostMapping(path = "/enable")
    @Operation(summary = "开启配额")
    public ResourceResponseDto<Boolean> enable(@PathVariable Long surveyId) {
        Survey survey = surveyService.requireSurvey(surveyId);
        quotaService.enable(survey);
        return new ResourceResponseDto<>(true);
    }

    @PostMapping(path = "/disable")
    @Operation(summary = "关闭配额")
    public ResourceResponseDto<Boolean> disable(@PathVariable Long surveyId) {
        Survey survey = surveyService.requireSurvey(surveyId);
        if (!survey.getEnableQuota()) {
            throw new BadRequestException("配额已经关闭");
        }
        if (survey.getQuotas().isEmpty()) {
            throw new BadRequestException("配额不能为空");
        }
        quotaService.disable(survey);
        return new ResourceResponseDto<>(true);
    }

//    @GetMapping
//    @JsonView(ResourceViews.Basic.class)
//    @Operation(
//            summary = "findAll",
//            description = "获取所有的配额"
//    )
//    public ResourcePageResponseDto<SurveyQuota> findAll(@PathVariable long surveyId, @RequestParam Map<String, Object> params) {
//        return quotaService.getAllQuotaStat(surveyId, params);
//    }  todo use crud

//    @PostMapping
//    @JsonView(ResourceViews.Detail.class)
//    @Operation(
//            summary = "create",
//            description = "新增配额"
//    )
//    @PreAuthorize("isAuthenticated()")
//    public ResourceResponseDto<SurveyQuota> create(@PathVariable long surveyId,
//                                                   @RequestBody SurveyQuota params) {
//        SurveyQuota result = quotaService.create(surveyId, params);
//        return new ResourceResponseDto(result);
//    } todo use crud

//    @GetMapping("/{eid}")
//    @JsonView(ResourceViews.Detail.class)
//    @Operation(
//            summary = "findOne",
//            description = "根据配额id获取配额信息"
//    )
//    public ResourceResponseDto<SurveyQuota> findOne(@PathVariable long surveyId, @PathVariable long eid) {
//        return quotaService.getOneQuotaStat(surveyId, eid);
//    } todo use crud

//    @PutMapping("/{eid}")
//    @JsonView(ResourceViews.Detail.class)
//    @Operation(
//            summary = "updateOne",
//            description = "修改配额"
//    )
//    @PreAuthorize("isAuthenticated()")
//    public ResourceResponseDto<SurveyQuota> updateOne(@PathVariable long surveyId, @PathVariable long eid,
//                                                      @RequestBody SurveyQuota params) {
//        SurveyQuota result = quotaService.updateOne(surveyId, eid, params);
//        return new ResourceResponseDto(result);
//    } todo use crud

}