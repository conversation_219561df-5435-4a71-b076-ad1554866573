package cn.hanyi.survey.controller;

import cn.hanyi.survey.client.service.luckyDraw.LuckyDrawService;
import cn.hanyi.survey.core.constant.lottery.LotteryType;
import cn.hanyi.survey.core.dto.lottery.HarvestAddressParam;
import cn.hanyi.survey.core.dto.lottery.SurveyClientLotteryResult;
import cn.hanyi.survey.core.dto.lottery.SurveyLotteryParam;
import cn.hanyi.survey.service.SurveyLotteryPrizeWinnerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.dto.ResourceResponseDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2022/9/21 18:14:37
 */
@Tag(name = "问卷-客户端-抽奖")
@RestController
public class SurveyClientLotteryController {

    @Autowired
    private SurveyLotteryPrizeWinnerService surveyLotteryPrizeWinnerService;

    @Autowired(required = false)
    private List<LuckyDrawService> luckyDrawServiceList;

    private final ConcurrentHashMap<LotteryType, LuckyDrawService> luckyDrawServiceMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        Optional.ofNullable(luckyDrawServiceList).ifPresent(l -> l.forEach(i -> luckyDrawServiceMap.put(i.type(), i)));
    }

    @Operation(summary = "答问卷抽奖")
    @PostMapping("/surveys/lottery/{lotteryId}/start")
    public ResourceResponseDto<SurveyClientLotteryResult> lottery(@PathVariable Long lotteryId, @RequestBody @Validated SurveyLotteryParam param) {
        Object result = null;
        LuckyDrawService service = luckyDrawServiceMap.get(param.getType());
        synchronized (service) {
            result = service.luckyDraw(lotteryId, param);
        }
        return new ResourceResponseDto(result);
    }

    @PostMapping("/surveys/{sid}/lottery/prize/winner/saveAddress")
    @Operation(summary = "保存中奖用户收获地址信息")
    public ResourceResponseDto<Boolean> savePrizeWinnerInfo(@PathVariable Long sid, @RequestBody HarvestAddressParam data) {
        return new ResourceResponseDto<>(surveyLotteryPrizeWinnerService.savePrizeWinnerInfo(data));
    }
}




















