package cn.hanyi.survey.controller;

import cn.hanyi.survey.dto.SurveyProcessDto;
import cn.hanyi.survey.service.QuotaService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.exception.BadRequestException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


@Tag(name = "问卷-配额")
@RestController
@RequestMapping("/surveys/{surveyId}/quotas")
public class QuotaOpenController {

    @Autowired
    private QuotaService quotaService;
    private static final String openSyncQuotaToken = "bc7c85319a63499bbae490bb020067b5";

    @PostMapping(path = "/sync")
    @Operation(summary = "同步配额")
    public SurveyProcessDto syncQuota(@PathVariable long surveyId, @RequestParam String token) {
        if (!openSyncQuotaToken.equals(token)) {
            throw new BadRequestException();
        }
        return quotaService.calculateQuota(surveyId);
    }

}