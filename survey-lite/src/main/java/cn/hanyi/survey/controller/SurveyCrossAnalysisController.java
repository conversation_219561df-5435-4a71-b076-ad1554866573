package cn.hanyi.survey.controller;

import cn.hanyi.survey.core.dto.SurveyCrossAnalysisRequestDto;
import cn.hanyi.survey.core.entity.SurveyCrossAnalysisDto;
import cn.hanyi.survey.service.SurveyCrossAnalysisService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.dto.ResourceListResponseDto;
import org.befun.core.dto.ResourceResponseDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@Tag(name = "问卷-交叉分析")
@RestController
@RequestMapping("/surveys")
@PreAuthorize("isAuthenticated()")
public class SurveyCrossAnalysisController {

    @Autowired
    private SurveyCrossAnalysisService service;

    @PostMapping("/{surveyId}/crossAnalysis")
    @Operation(summary = "新增交叉分析")
    public ResourceListResponseDto<SurveyCrossAnalysisDto> createSurveyCrossAnalysis(
            @PathVariable long surveyId,
            @RequestBody SurveyCrossAnalysisRequestDto data) {
        return new ResourceListResponseDto<>(service.batchAdd(surveyId, data));
    }

    @PutMapping("/{surveyId}/crossAnalysis/{crossAnalysisId}")
    @Operation(summary = "修改交叉分析")
    public ResourceResponseDto<SurveyCrossAnalysisDto> updateOneSurveyCrossAnalysis(
            @PathVariable long surveyId,
            @PathVariable long crossAnalysisId,
            @RequestBody SurveyCrossAnalysisRequestDto data) {
        return new ResourceResponseDto<>(service.update(surveyId, crossAnalysisId, data));
    }

}
