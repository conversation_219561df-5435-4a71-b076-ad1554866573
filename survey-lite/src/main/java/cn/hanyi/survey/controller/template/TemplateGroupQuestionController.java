package cn.hanyi.survey.controller.template;

import cn.hanyi.survey.core.entity.SurveyDto;
import cn.hanyi.survey.core.entity.template.TemplateSurveyQuestion;
import cn.hanyi.survey.core.entity.template.TemplateSurveyQuestionDto;
import cn.hanyi.survey.core.repository.TemplateSurveyQuestionRepository;
import cn.hanyi.survey.dto.template.QuestionsConvertTemplateDto;
import cn.hanyi.survey.dto.template.TemplateConvertQuestionDto;
import cn.hanyi.survey.dto.template.TemplateSurveyQueryDto;
import cn.hanyi.survey.dto.template.UpdateTemplateRequestDto;
import cn.hanyi.survey.service.template.TemplateSurveyQuestionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.ResourceMethodDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static org.befun.core.rest.annotation.processor.ResourceMethod.*;


/**
 * <AUTHOR>
 */
@Tag(name = "模板-问题库")
@RestController
@RequestMapping("/templates-questions")
@ResourceController(
        entityClass = TemplateSurveyQuestion.class,
        repositoryClass = TemplateSurveyQuestionRepository.class,
        serviceClass = TemplateSurveyQuestionService.class,
        permission = "isAuthenticated()",
        excludeActions = {CREATE, BATCH_UPDATE, UPDATE_ONE},
        methodDtoClass = {
                @ResourceMethodDto(method = FIND_ALL, dtoClass = TemplateSurveyQueryDto.class),
        },
        docCrud = "问题模板"
)
public class TemplateGroupQuestionController {

    @Autowired
    TemplateSurveyQuestionService templateSurveyQuestionService;

    @RequestMapping(
            path = "use-template",
            method = RequestMethod.POST
    )
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "模板转题型")
    public ResourceResponseDto<SurveyDto> convert(@RequestBody TemplateConvertQuestionDto questionsConvertDto) {
        return new ResourceResponseDto<>(templateSurveyQuestionService.convertToSurveyQuestion(questionsConvertDto));
    }

    @RequestMapping(
            path = "save-as-template",
            method = RequestMethod.POST
    )
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "题型转模板")
    public ResourceResponseDto<List<TemplateSurveyQuestionDto>> convertToTemplate(
            @RequestBody QuestionsConvertTemplateDto questionsConvertTemplateDto) {
        return new ResourceResponseDto<>(templateSurveyQuestionService.convertToTemplate(questionsConvertTemplateDto));
    }

    @PostMapping("{templateId}/update-template")
    @Operation(summary = "修改模板")
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<Boolean> updateTemplate(@PathVariable("templateId") long templateId, @RequestBody UpdateTemplateRequestDto dto) {
        return new ResourceResponseDto<>(templateSurveyQuestionService.updateTemplate(templateId, dto.getGroupId(), dto.getTemplateName()));
    }
}