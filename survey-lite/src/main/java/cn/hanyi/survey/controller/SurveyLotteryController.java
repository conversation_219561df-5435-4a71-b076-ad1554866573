package cn.hanyi.survey.controller;

import cn.hanyi.survey.core.dto.lottery.OrderParam;
import cn.hanyi.survey.core.dto.lottery.SimpleSurveyLotteryResult;
import cn.hanyi.survey.core.entity.SurveyLottery;
import cn.hanyi.survey.core.entity.SurveyLotteryDto;
import cn.hanyi.survey.core.entity.SurveyLotteryPrize;
import cn.hanyi.survey.core.repository.SurveyLotteryPrizeRepository;
import cn.hanyi.survey.core.repository.SurveyLotteryRepository;
import cn.hanyi.survey.service.SurveyLotteryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.dto.ResourcePageResponseDto;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.ResourceEmbeddedMany;
import org.befun.core.rest.annotation.ResourcePermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static org.befun.core.rest.annotation.processor.ResourceMethod.COUNT;
import static org.befun.core.rest.annotation.processor.ResourceMethod.CREATE;

/**
 * <AUTHOR>
 * @date 2022/9/21 14:53:11
 */
@Tag(name = "问卷-抽奖")
@RestController
@RequestMapping("/lottery")
@PreAuthorize("isAuthenticated()")
@ResourceController(
        entityClass = SurveyLottery.class,
        repositoryClass = SurveyLotteryRepository.class,
        serviceClass = SurveyLotteryService.class,
        docTag = "问卷-抽奖",
        docCrud = "抽奖",
        excludeActions = {COUNT, CREATE},
        permission = "isAuthenticated()",
        permissions = {
                @ResourcePermission(action = "create", permission = "isAuthenticated()"),
                @ResourcePermission(action = "updateOne", permission = "isAuthenticated()"),
                @ResourcePermission(action = "deleteOne", permission = "isAuthenticated()"),
                @ResourcePermission(action = "batchUpdate", permission = "isAuthenticated()")
        }
)
@ResourceEmbeddedMany(
        path = "prizes",
        fieldNameInRoot = "prizes",
        entityClass = SurveyLotteryPrize.class,
        repositoryClass = SurveyLotteryPrizeRepository.class,
        docTag = "问卷-抽奖-奖品",
        excludeActions = {},
        docCrud = "奖品",
        permissions = {
                @ResourcePermission(action = "create", permission = "isAuthenticated()"),
                @ResourcePermission(action = "updateOne", permission = "isAuthenticated()"),
                @ResourcePermission(action = "deleteOne", permission = "isAuthenticated()"),
                @ResourcePermission(action = "batchUpdate", permission = "isAuthenticated()")
        }
)
public class SurveyLotteryController {

    @Autowired
    private SurveyLotteryService surveyLotteryService;
    @Autowired
    private SurveyLotteryRepository surveyLotteryRepository;

    @Operation(summary = "抽奖渠道列表")
    @GetMapping("/survey/{sid}/list")
    public ResourcePageResponseDto<SurveyLotteryDto> findAllBySid(@PathVariable Long sid,
                                                                  @RequestParam(value = "_page", defaultValue = "1") int page,
                                                                  @RequestParam(value = "_limit", defaultValue = "10") int limit) {
        Page<SurveyLotteryDto> dtoList = surveyLotteryService.findAllBySid(sid, page, limit);
        return new ResourcePageResponseDto<>(dtoList);
    }

    @PostMapping("")
    @Operation(summary = "新增抽奖渠道")
    public ResourceResponseDto<SurveyLotteryDto> create(@RequestBody SurveyLotteryDto data) {
        return new ResourceResponseDto<>(surveyLotteryService.insert(data));
    }

    @Operation(summary = "simple活动渠道列表")
    @GetMapping("/survey/{sid}/simple/list")
    public ResourceResponseDto<SimpleSurveyLotteryResult> findSimpleLottery(@PathVariable Long sid) {
        List<SimpleSurveyLotteryResult> list = surveyLotteryService.findSimpleLottery(sid);
        return new ResourceResponseDto(list);
    }

    @PutMapping("/survey/{sid}/lottery/{lotteryId}/enable")
    @Operation(summary = "启用活动，修改活动启用状态")
    public ResourceResponseDto<SurveyLotteryDto> updateStatus(@PathVariable Long lotteryId, @Validated @RequestBody OrderParam order) {
        return new ResourceResponseDto(surveyLotteryService.update(lotteryId, order));
    }
}















