package cn.hanyi.survey.controller;

import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.core.entity.SurveyQuestionItemDto;
import cn.hanyi.survey.dto.survey.SurveyQuestionItemChangeDto;
import cn.hanyi.survey.service.QuestionService;
import cn.hanyi.survey.service.QuestionsItemService;
import cn.hanyi.survey.service.SurveyService;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.dto.BaseResponseDto;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.exception.EntityNotFoundException;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.view.ResourceViews;
import org.befun.core.service.MapperService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Tag(name = "问卷-问题-选项")
@RestController
@PreAuthorize("isAuthenticated()")
@RequestMapping("/surveys/{id}/questions/{mid}/items")
public class QuestionsItemsController extends BaseController<QuestionsItemService> {

    @Autowired
    private QuestionsItemService questionsItemService;
    @Autowired
    private SurveyService surveyService;
    @Autowired
    private QuestionService questionService;

    @Autowired
    private MapperService mapperService;

    @DeleteMapping("/{eid}")
    @Operation(summary = "删除选项")
    public BaseResponseDto<String> deleteOne(
            @PathVariable long id,
            @PathVariable long mid,
            @PathVariable long eid,
            @RequestBody Optional<List<Long>> logicIds) {
        questionsItemService.deleteOneDeepEmbeddedMany(id, "survey", mid, "question", eid);
        logicIds.ifPresent(logicIds1 -> logicIds1.forEach(logicId -> surveyService.deleteLogic(logicId)));
        return new BaseResponseDto<>();
    }

    @PutMapping("")
    @Operation(summary = "批量创建问题选项")
    public ResourceResponseDto<List<SurveyQuestionItemDto>> batchCreate(@PathVariable Long id, @PathVariable Long mid, @RequestBody List<SurveyQuestionItemDto> items) {
        Survey survey = surveyService.requireSurvey(id);
        List<SurveyQuestion> surveyQuestions = survey.getQuestions();
        SurveyQuestion question = surveyQuestions.stream().filter(q -> q.getId().equals(mid)).findFirst().orElseThrow(() -> new EntityNotFoundException(SurveyQuestion.class));
        return new ResourceResponseDto<>(questionsItemService.insertQuestionItems(question, items));
    }

    @PutMapping("/delAndInsert")
    @Operation(summary = "先删除原有选项再批量创建问题选项")
    public ResourceResponseDto<List<SurveyQuestionItemDto>> batchCreateAfterDel(@PathVariable Long id, @PathVariable Long mid, @RequestBody SurveyQuestionItemChangeDto data) {

        if (data.getItemIds().size() > 0) {
            //先删除原有选项
            questionsItemService.deleteByIdIn(data.getItemIds());
        }
        List<SurveyQuestionItemDto> items = data.getItems();
        Survey survey = surveyService.requireSurvey(id);
        List<SurveyQuestion> surveyQuestions = survey.getQuestions();
        SurveyQuestion question = surveyQuestions.stream().filter(q -> q.getId().equals(mid)).findFirst().orElseThrow(() -> new EntityNotFoundException(SurveyQuestion.class));
        return new ResourceResponseDto<>(questionsItemService.insertQuestionItems(question, items));
    }

    @DeleteMapping("")
    @Operation(summary = "删除全部选项")
    public BaseResponseDto<String> deleteAll(
            @PathVariable long id,
            @PathVariable long mid,
            @RequestBody Optional<List<Long>> logicIds) {
        Survey survey = surveyService.requireSurvey(id);
        List<SurveyQuestion> surveyQuestions = survey.getQuestions();
        SurveyQuestion question = surveyQuestions.stream().filter(q -> q.getId().equals(mid)).findFirst().orElseThrow(() -> new EntityNotFoundException(SurveyQuestion.class));
        questionsItemService.deleteAll(question);
        logicIds.ifPresent(logicIds1 -> logicIds1.forEach(logicId -> surveyService.deleteLogic(logicId)));
        return new BaseResponseDto<>();
    }

    @DeleteMapping("/batch")
    @Operation(summary = "批量删除")
    public BaseResponseDto<String> deleteBatch(
            @PathVariable long id,
            @PathVariable long mid,
            @RequestBody List<Long> itemsIds) {
        Survey survey = surveyService.requireSurvey(id);
        List<SurveyQuestion> surveyQuestions = survey.getQuestions();
        questionsItemService.deleteByIdIn(itemsIds);
        return new BaseResponseDto<>();
    }

    @PutMapping("/{did}")
    @Operation(
            summary = "修改选项"
    )
    @Tag(
            name = "问卷-问题-选项"
    )
    @JsonView(ResourceViews.Detail.class)
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<SurveyQuestionItemDto> updateOneSurveyQuestionSurveyQuestionItem(
            @PathVariable long id, @PathVariable long mid, @PathVariable long did,
            @RequestBody Map<String, Object> data) {
        return new ResourceResponseDto<>(service.mapToDto(service.update(id, mid, did, data)));
    }

}
