package cn.hanyi.survey.controller;

import cn.hanyi.survey.core.dto.channel.ResendDto;
import cn.hanyi.survey.core.dto.channel.SendStatisticDto;
import cn.hanyi.survey.core.entity.SurveySendRecordDto;
import cn.hanyi.survey.service.ChannelRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.rest.annotation.ResourceQueryPredicate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;


/**
 * <AUTHOR>
 */
@Tag(name = "问卷-渠道-记录")
@RestController
@PreAuthorize("isAuthenticated()")
@RequestMapping("/surveys/{surveyId}/channel")
public class ChannelRecordController {

    @Autowired
    ChannelRecordService channelRecordService;

//    @GetMapping("/{channelId}/get-send-record")
//    @Operation(
//            summary = "getSendRecord",
//            description = "获取问卷推送列表"
//    )
//    @PreAuthorize("isAuthenticated()")
//    @JsonView(ResourceViews.Basic.class)
//    public ResourcePageResponseDto<SurveySendRecord> getSendRecord(@PathVariable long surveyId, @PathVariable long channelId,
//                                                                   @RequestParam Map<String, Object> params) {
//        return channelRecordService.getSendRecord(surveyId, channelId, params);
//    } todo use crud


    @GetMapping("/{channelId}/get-send-statistic")
    @Operation(
            summary = "获取问卷渠道推送统计数据",
            description = "获取问卷渠道推送统计数据"
    )

    public ResourceResponseDto<SendStatisticDto> getSendStatistic(@PathVariable long surveyId, @PathVariable long channelId) {
        SendStatisticDto sendStatisticDto = channelRecordService.getSendStatistic(surveyId, channelId);
        return new ResourceResponseDto<>(sendStatisticDto);
    }

    @PostMapping("/{channelId}/delete-send-record")
    @Operation(
            summary = "删除推送记录",
            description = "删除推送记录"
    )
    public ResourceResponseDto<Boolean> deleteSendRecord(@PathVariable long surveyId, @PathVariable long channelId,
                                                         @RequestBody ResendDto params) {
        return channelRecordService.deleteSendRecord(surveyId, channelId, params);
    }

    @GetMapping("/{channelId}/download-send-record")
    @Operation(
            summary = "下载推送已完成记录",
            description = "下载推送已完成记录"
    )
    public void download(HttpServletResponse response, @PathVariable long surveyId, @PathVariable long channelId) {
        channelRecordService.download(response, channelId);
    }
}