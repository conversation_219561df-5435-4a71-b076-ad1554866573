package cn.hanyi.survey.controller;

import cn.hanyi.survey.core.constant.channel.SurveyCollectorMethod;
import cn.hanyi.survey.core.constant.survey.SurveyStatus;
import cn.hanyi.survey.core.dto.SurveyLinkDto;
import cn.hanyi.survey.core.dto.SurveyWithGroupTreeDto;
import cn.hanyi.survey.core.dto.survey.CreateShortLinkDto;
import cn.hanyi.survey.core.entity.*;
import cn.hanyi.survey.core.repository.*;
import cn.hanyi.survey.core.service.SurveyLinkService;
import cn.hanyi.survey.dto.*;
import cn.hanyi.survey.dto.analysis.*;
import cn.hanyi.survey.dto.index.SimpleSurveyDto;
import cn.hanyi.survey.dto.survey.*;
import cn.hanyi.survey.service.*;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.dto.ResourceListResponseDto;
import org.befun.core.dto.ResourcePageResponseDto;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.*;
import org.befun.core.rest.view.ResourceViews;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

import static org.befun.core.rest.annotation.processor.ResourceMethod.*;

@Tag(name = "问卷")
@RestController
@RequestMapping("/surveys")
@PreAuthorize("isAuthenticated()")
@ResourceController(
        entityClass = Survey.class,
        repositoryClass = SurveyRepository.class,
        serviceClass = SurveyService.class,
        excludeActions = {CREATE, DELETE_ONE, UPDATE_ONE, FIND_ONE},
        docCrud = "问卷",
        permission = "isAuthenticated()",
        permissions = {
                @ResourcePermission(action = "create", permission = "isAuthenticated()"),
                @ResourcePermission(action = "updateOne", permission = "isAuthenticated()"),
                @ResourcePermission(action = "deleteOne", permission = "isAuthenticated()"),
                @ResourcePermission(action = "batchUpdate", permission = "isAuthenticated()")
        }
)
@ResourceEmbeddedMany(
        path = "questions",
        fieldNameInRoot = "questions",
        entityClass = SurveyQuestion.class,
        repositoryClass = SurveyQuestionRepository.class,
        excludeActions = {DELETE_ONE, BATCH_UPDATE, UPDATE_ONE, CREATE},
        docTag = "问卷-问题",
        docCrud = "问题",
        permissions = {
                @ResourcePermission(action = "create", permission = "isAuthenticated()"),
                @ResourcePermission(action = "updateOne", permission = "isAuthenticated()"),
                @ResourcePermission(action = "deleteOne", permission = "isAuthenticated()"),
                @ResourcePermission(action = "batchUpdate", permission = "isAuthenticated()")
        },
        deepEmbeddedMany = {
                @ResourceDeepEmbeddedMany(
                        path = "items",
                        fieldNameInEmbedded = "items",
                        entityClass = SurveyQuestionItem.class,
                        repositoryClass = SurveyQuestionItemRepository.class,
                        excludeActions = {DELETE_ONE, UPDATE_ONE},
                        docTag = "问卷-问题-选项",
                        docCrud = "选项",
                        permissions = {
                                @ResourcePermission(action = "create", permission = "isAuthenticated()"),
                                @ResourcePermission(action = "updateOne", permission = "isAuthenticated()"),
                                @ResourcePermission(action = "deleteOne", permission = "isAuthenticated()"),
                                @ResourcePermission(action = "batchUpdate", permission = "isAuthenticated()")
                        }
                ),
                @ResourceDeepEmbeddedMany(
                        path = "columns",
                        fieldNameInEmbedded = "columns",
                        entityClass = SurveyQuestionColumn.class,
                        repositoryClass = SurveyQuestionColumnRepository.class,
                        excludeActions = {DELETE_ONE, BATCH_UPDATE, UPDATE_ONE},
                        docTag = "问卷-问题-列",
                        docCrud = "列",
                        permissions = {
                                @ResourcePermission(action = "create", permission = "isAuthenticated()"),
                                @ResourcePermission(action = "deleteOne", permission = "isAuthenticated()"),
                                @ResourcePermission(action = "batchUpdate", permission = "isAuthenticated()")
                        }
                )
        }
)
@ResourceEmbeddedMany(
        path = "logics",
        fieldNameInRoot = "logics",
        entityClass = SurveyLogic.class,
        repositoryClass = SurveyLogicRepository.class,
        docTag = "问卷-逻辑",
        docCrud = "逻辑",
        permissions = {
                @ResourcePermission(action = "create", permission = "isAuthenticated()"),
                @ResourcePermission(action = "updateOne", permission = "isAuthenticated()"),
                @ResourcePermission(action = "deleteOne", permission = "isAuthenticated()"),
                @ResourcePermission(action = "batchUpdate", permission = "isAuthenticated()")
        }
)
@ResourceEmbeddedMany(
        path = "randoms",
        fieldNameInRoot = "randoms",
        entityClass = SurveyQuestionRandom.class,
        repositoryClass = SurveyQuestionRandomRepository.class,
        docTag = "问卷-题目随机",
        docCrud = "题目随机",
        permissions = {
                @ResourcePermission(action = "create", permission = "isAuthenticated()"),
                @ResourcePermission(action = "updateOne", permission = "isAuthenticated()"),
                @ResourcePermission(action = "deleteOne", permission = "isAuthenticated()"),
                @ResourcePermission(action = "batchUpdate", permission = "isAuthenticated()")
        }
)
@ResourceEmbeddedMany(
        path = "quotas",
        fieldNameInRoot = "quotas",
        entityClass = SurveyQuota.class,
        repositoryClass = SurveyQuotaRepository.class,
        docTag = "问卷-配额",
        docCrud = "配额",
        permissions = {
                @ResourcePermission(action = "create", permission = "isAuthenticated()"),
                @ResourcePermission(action = "updateOne", permission = "isAuthenticated()"),
                @ResourcePermission(action = "deleteOne", permission = "isAuthenticated()"),
                @ResourcePermission(action = "batchUpdate", permission = "isAuthenticated()")
        }
)
@ResourceEmbeddedMany(
        path = "tags",
        fieldNameInRoot = "tags",
        entityClass = SurveyTag.class,
        repositoryClass = SurveyTagRepository.class,
        docTag = "问卷-答卷-标签",
        docCrud = "标签",
        permissions = {
                @ResourcePermission(action = "create", permission = "isAuthenticated()"),
                @ResourcePermission(action = "updateOne", permission = "isAuthenticated()"),
                @ResourcePermission(action = "deleteOne", permission = "isAuthenticated()"),
                @ResourcePermission(action = "batchUpdate", permission = "isAuthenticated()")
        }
)
@ResourceEmbeddedMany(
        path = "responses",
        fieldNameInRoot = "responses",
        entityClass = SurveyResponse.class,
        repositoryClass = SurveyResponseRepository.class,
        dtoClass = SurveyResponseDetailDto.class,
        excludeActions = {CREATE, UPDATE_ONE, FIND_ONE, BATCH_UPDATE},
        docTag = "答卷",
        docCrud = "答卷",
        permissions = {
                @ResourcePermission(action = "create", permission = "isAuthenticated()"),
                @ResourcePermission(action = "updateOne", permission = "isAuthenticated()"),
                @ResourcePermission(action = "deleteOne", permission = "isAuthenticated()"),
                @ResourcePermission(action = "batchUpdate", permission = "isAuthenticated()")
        }
)
@ResourceEmbeddedMany(
        path = "channel",
        fieldNameInRoot = "channel",
        entityClass = SurveyChannel.class,
        repositoryClass = SurveyChannelRepository.class,
        dtoClass = SurveyChannelDto.class,
        excludeActions = {BATCH_UPDATE, DELETE_ONE, UPDATE_ONE},
        docTag = "问卷-渠道",
        docCrud = "渠道",
        permissions = {
                @ResourcePermission(action = "create", permission = "isAuthenticated()"),
                @ResourcePermission(action = "updateOne", permission = "isAuthenticated()"),
                @ResourcePermission(action = "deleteOne", permission = "isAuthenticated()"),
                @ResourcePermission(action = "batchUpdate", permission = "isAuthenticated()")
        },
        deepEmbeddedMany = {
                @ResourceDeepEmbeddedMany(
                        path = "get-send-record",
                        fieldNameInEmbedded = "records",
                        entityClass = SurveySendRecord.class,
                        repositoryClass = SurveySendRecordRepository.class,
                        excludeActions = {FIND_ONE, CREATE, UPDATE_ONE, DELETE_ONE, BATCH_UPDATE},
                        docTag = "问卷-渠道-记录",
                        docCrud = "记录",
                        permissions = {
                                @ResourcePermission(action = "create", permission = "isAuthenticated()"),
                                @ResourcePermission(action = "updateOne", permission = "isAuthenticated()"),
                                @ResourcePermission(action = "deleteOne", permission = "isAuthenticated()"),
                                @ResourcePermission(action = "batchUpdate", permission = "isAuthenticated()")
                        }
                )
        }
)

@ResourceEmbeddedMany(
        path = "lottery",
        fieldNameInRoot = "lottery",
        entityClass = SurveyLottery.class,
        repositoryClass = SurveyLotteryRepository.class,
        dtoClass = SurveyLotteryDto.class,
        excludeActions = {BATCH_UPDATE, DELETE_ONE, UPDATE_ONE, COUNT, CREATE, FIND_ONE},
        docTag = "问卷-抽奖",
        docCrud = "抽奖",
        permissions = {
                @ResourcePermission(action = "create", permission = "isAuthenticated()"),
                @ResourcePermission(action = "updateOne", permission = "isAuthenticated()"),
                @ResourcePermission(action = "deleteOne", permission = "isAuthenticated()"),
                @ResourcePermission(action = "batchUpdate", permission = "isAuthenticated()")
        }
)
@ResourceEmbeddedMany(
        path = "languages",
        fieldNameInRoot = "languages",
        entityClass = SurveyLanguage.class,
        repositoryClass = SurveyLanguageRepository.class,
        docTag = "问卷-语言",
        docCrud = "语言",
        permissions = {
                @ResourcePermission(action = "create", permission = "isAuthenticated()"),
                @ResourcePermission(action = "updateOne", permission = "isAuthenticated()"),
                @ResourcePermission(action = "deleteOne", permission = "isAuthenticated()"),
                @ResourcePermission(action = "batchUpdate", permission = "isAuthenticated()")
        }
)
@ResourceEmbeddedMany(
        path = "personalizedRemarks",
        fieldNameInRoot = "personalizedRemarks",
        entityClass = SurveyPersonalizedRemark.class,
        repositoryClass = SurveyPersonalizedRemarkRepository.class,
        docTag = "问卷-个性化结束页",
        docCrud = "个性化结束页",
        permissions = {
                @ResourcePermission(action = "create", permission = "isAuthenticated()"),
                @ResourcePermission(action = "updateOne", permission = "isAuthenticated()"),
                @ResourcePermission(action = "deleteOne", permission = "isAuthenticated()"),
                @ResourcePermission(action = "batchUpdate", permission = "isAuthenticated()")
        }
)
@ResourceEmbeddedMany(
        path = "crossAnalysis",
        fieldNameInRoot = "crossAnalysis",
        entityClass = SurveyCrossAnalysis.class,
        repositoryClass = SurveyCrossAnalysisRepository.class,
        excludeActions = {CREATE, UPDATE_ONE, COUNT, BATCH_UPDATE, FIND_ALL},
        docTag = "问卷-交叉分析",
        docCrud = "交叉分析"
)
@Validated
public class SurveyController extends BaseController<SurveyService> {

    @Autowired
    private SurveyService surveyService;
    @Autowired
    private AnalyticService analyticService;
    @Autowired
    private ResponseService responseService;
    @Autowired
    private SurveyCustomQueryService surveyCustomQueryService;
    @Autowired
    private SurveyVerifyService surveyVerifyService;
    @Autowired
    private SurveyContentAuditService surveyContentAuditService;
    @Autowired
    private GptConversationHistoryService gptConversationHistoryService;
    @Autowired
    private SurveyLinkService surveyLinkService;

    @GetMapping("page/{type}")
    @Operation(summary = "问卷管理（我的问卷，与我共享,我的审核）")
    @JsonView(ResourceViews.Basic.class)
    public ResourcePageResponseDto<SurveySimpleListDto> findPageByGroup(
            @Parameter(name = "type", description = "all 全部问卷，owner 我的问卷，share 与我共享 audit 我的审核") @PathVariable String type,
            @Valid @ResourceQueryCustom SurveySearchDto params) {
        params.setType(type);
        return new ResourcePageResponseDto<>(surveyCustomQueryService.findPageByGroup(params));
    }

    @GetMapping("/noPage/{type}")
    @Operation(summary = "返回文件夹和问卷")
    public ResourceListResponseDto<SurveyWithGroupTreeDto> findSimpleSurveyWithGroupTree(
            @Parameter(name = "type", description = "all 全部问卷，owner 我的问卷，share 与我共享 audit 我的审核") @PathVariable String type) {
        List<SurveyWithGroupTreeDto> result = surveyService.findSimpleSurveyWithGroupTree(type);
        return new ResourceListResponseDto(result);
    }

    @GetMapping("/findSimpleSurvey")
    @Operation(summary = "问卷id和标题（客户旅程，体验指标）")
    @JsonView(ResourceViews.Basic.class)
    public ResourceListResponseDto<SurveySimpleDto> findSimpleSurvey(
            @Parameter(name = "type", description = "all 包括无量表题的所有问卷,6：返回量表题,多个题型逗号分割：6，11，19") @RequestParam(required = false) String type) {
        List<SimpleSurveyDto> result = surveyService.findSimpleSurvey(type);
        return new ResourceListResponseDto(result);
    }

    @GetMapping("/current/relation")
    @Operation(summary = "查询问卷与我的关系")
    public ResourceListResponseDto<SurveyRelationDto> surveyRelation(@Parameter(name = "ids", description = "问卷id，多个逗号分割") String ids) {
        return new ResourceListResponseDto<>(surveyCustomQueryService.surveyRelation(ids));
    }

    @PostMapping
    @JsonView(ResourceViews.Detail.class)
    @Operation(summary = "创建问卷")
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<SurveyDto> create(@RequestBody Survey data) {
//        surveyService.checkSurveyLimit();
        return new ResourceResponseDto<>(surveyService.create(data));
    }

    @PostMapping("/import")
    @JsonView(ResourceViews.Detail.class)
    @Operation(summary = "导入问卷")
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<SurveyDto> importSurvey(@RequestBody SurveyImportDto importDto) {
        return new ResourceResponseDto<>(surveyService.importSurvey(importDto));
    }

    @GetMapping("/{sid}/export-word")
    @JsonView(ResourceViews.Detail.class)
    @Operation(summary = "导出问卷")
    public void exportWord(@PathVariable long sid, HttpServletRequest request, HttpServletResponse response) {
        surveyService.export(sid, request, response);
    }

    @PostMapping("/{id}/publish")
    @Operation(
            summary = "启用问卷"
    )
    public ResourceResponseDto<SurveyProcessDto> publish(@PathVariable long id, @RequestBody(required = false) SurveyPublishDto dto) {
        return new ResourceResponseDto<>(surveyService.publish(id, dto != null && dto.isManualAuditIfFail()));
    }

    @PostMapping("/{id}/stop")
    @Operation(
            summary = "暂停问卷"
    )
    public ResourceResponseDto<SurveyDto> stop(@PathVariable long id) {
        return new ResourceResponseDto<>(surveyService.stop(id));
    }

    @PostMapping("/{sid}/clone")
    @Operation(summary = "克隆问卷")
    public ResourceResponseDto<SurveyDto> clone(@PathVariable Long sid, @RequestBody(required = false) SurveyCloneDto dto) {
//        surveyService.checkSurveyLimit();
        return new ResourceResponseDto<>(surveyService.clone(sid, dto));
    }

    @ResourceInstanceAction(
            path = "analysis",
            method = RequestMethod.POST,
            description = "问卷分析",
            permission = "isAuthenticated()"
    )
    public ResourceResponseDto<List<Map<String, Object>>> analysis(Survey survey, @RequestBody CubeDto cube) {
        List<Map<String, Object>> result = null;
        try {
            result = analyticService.queryByCube(survey, cube);
        } catch (NoSuchFieldException e) {
            e.printStackTrace();
            throw new BadRequestException(e.getMessage());
        }
        return new ResourceResponseDto<>(result);
    }

    @GetMapping({"/{id}/analysis"})
    @PreAuthorize("isAuthenticated()")
    @Operation(
            summary = "问题统计分析",
            description = "问题统计分析"
    )
    public ResourceResponseDto<SingleAnalysisResponseDto<?>> analysis(@PathVariable long id, @PathVariable Optional<Long> qid,
                                                                      SingleAnalysisRequest data) {
        Survey survey = surveyService.requireSurvey(id);
        return new ResourceResponseDto<>(analyticService.singleAnalysis(survey, qid, data));
    }

    @GetMapping({"/{id}/conversation-history"})
    @PreAuthorize("isAuthenticated()")
    @Operation(
            summary = "gpt分析",
            description = "gpt分析"
    )
    public ResourceResponseDto<ConversationHistoryResponseDto> conversationHistory(@PathVariable long id) {
        Survey survey = surveyService.requireSurvey(id);

        return new ResourceResponseDto<>(gptConversationHistoryService.getHistory(survey));
    }


    @PostMapping({"/{id}/analysis/{qid}", "/{id}/analysis/total"})
    @PreAuthorize("isAuthenticated()")
    @Operation(
            summary = "单题分析",
            description = "单题分析"
    )
    public ResourceResponseDto<SingleAnalysisResponseDto<?>> singleAnalysis(@PathVariable long id, @PathVariable Optional<Long> qid,
                                                                            @Valid @ResourceQueryCustom @RequestBody SingleAnalysisRequest data) {
        Survey survey = surveyService.requireSurvey(id);
        return new ResourceResponseDto<>(analyticService.singleAnalysis(survey, qid, data));
    }

    @GetMapping("/{id}/crossAnalysis")
    @Operation(
            summary = "全部交叉分析"
    )
    @Tag(
            name = "问卷-交叉分析"
    )
    @JsonView(ResourceViews.Basic.class)
    public ResourcePageResponseDto<SurveyCrossAnalysisDto> findAllSurveyCrossAnalysis(
            @PathVariable long id,
            @ResourceQueryPredicate @Valid ResourceEntityQueryDto<SurveyCrossAnalysisDto> params) {
        analyticService.checkVersionLimit();
        return new ResourcePageResponseDto(service.findAllEmbeddedMany(id, "crossAnalysis", cn.hanyi.survey.core.entity.SurveyCrossAnalysis.class, cn.hanyi.survey.core.entity.SurveyCrossAnalysisDto.class, params));
    }

    @GetMapping("/{sid}/analysis/tags")
    @Operation(summary = "获取答卷标签")
    public ResourceResponseDto<SingleAnalysisResponseDto> getTags(@PathVariable Long sid) {
        Survey survey = surveyService.requireSurvey(sid);
        analyticService.checkVersionLimit();
        return new ResourceResponseDto<>(analyticService.countTags(survey));
    }

    @GetMapping("/{sid}/analysis/textView")
    @Operation(summary = "获取指定选项文本输入答题内容")
    public ResourceResponseDto<TextInputViewDto> getItemResponseCell(@PathVariable Long sid, @RequestParam(required = false) Map<String, Object> data) {

        String channelIds = Objects.toString(data.get("channelIds"), null);
        data.put("channelIds", StringUtils.isNotEmpty(channelIds) ? Arrays.stream(channelIds.split(",")).collect(Collectors.toList()) : List.of());

        String channelTypes = Objects.toString(data.get("channelTypes"), null);
        data.put("channelTypes", StringUtils.isNotEmpty(channelTypes) ? Arrays.stream(channelTypes.split(",")).map(SurveyCollectorMethod::valueOf).collect(Collectors.toList()) : List.of());

        TextInputViewRequest textInputViewRequest = JsonHelper.toObject(data, TextInputViewRequest.class);


        return new ResourceResponseDto(analyticService.getItemResponseCell(sid, textInputViewRequest));
    }


    /**
     * 1.7.7 采用中值插入新增题型，不再使用此接口
     */
    @Deprecated(since = "1.7.7")
    @ResourceInstanceAction(
            path = "insert",
            method = RequestMethod.POST,
            description = "插入问卷",
            permission = "isAuthenticated()"
    )
    public ResourceResponseDto<SurveyDto> insert(Survey survey, @RequestBody SurveyQuestion question) {
        surveyService.checkSurveyVerifyStatus(survey.getStatus());
        return new ResourceResponseDto<>(surveyService.insertQuestion(survey, question));
    }

    @ResourceInstanceAction(
            path = "clean",
            method = RequestMethod.DELETE,
            description = "删除问卷下答卷",
            permission = "isAuthenticated()"
    )
    @JsonView(ResourceViews.Detail.class)
    public ResourceResponseDto<String> cleanResponse(Survey survey) {
        responseService.cleanResponse(survey);
        return new ResourceResponseDto<>("");
    }

    @GetMapping("/{apiKey}/encrypt/{content}")
    @Operation(summary = "使用apiKey加密内容")
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<ApiKeyDto> encrypt(@PathVariable String apiKey, @PathVariable String content) {
        return new ResourceResponseDto<>(new ApiKeyDto(apiKey, content, responseService.encryptParams(apiKey, content)));
    }


    @GetMapping("/{apiKey}/decrypt/{content}")
    @Operation(summary = "使用apiKey解密内容")
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<ApiKeyDto> decrypt(@PathVariable String apiKey, @PathVariable String content) {
        return new ResourceResponseDto<>(new ApiKeyDto(apiKey, content, responseService.decryptParams(apiKey, content)));
    }

    @PostMapping("change-group")
    @Operation(summary = "修改问卷分组")
    public ResourceResponseDto<Boolean> changeGroup(@Valid @RequestBody SurveyChangeGroupDto dto) {
        return new ResourceResponseDto<>(surveyService.changeGroup(dto.getTargetGroupId(), dto.getSurveyIds()));
    }

    @DeleteMapping("/{id}")
    @Operation(
            summary = "删除问卷"
    )
    @JsonView(ResourceViews.Detail.class)
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<Boolean> deleteOne(@PathVariable long id) {
        Boolean result = surveyService.deleteOne(id);
        Survey survey = service.requireSurvey(id);
        return new ResourceResponseDto(result);
    }

    @PutMapping("/{id}")
    @Operation(
            summary = "修改问卷"
    )
    @JsonView(ResourceViews.Detail.class)
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<SurveyDto> updateOne(@PathVariable long id,
                                                    @RequestBody Map<String, Object> data) {
        return new ResourceResponseDto(service.updateOne(id, data));
    }

    @PostMapping(value = "/createShortLink")
    @Operation(summary = "1.9.0-创建问卷短链")
    public ResourceResponseDto<SurveyLinkDto> createShortLink(@Valid @RequestBody CreateShortLinkDto dto) {
        return new ResourceResponseDto<>(surveyLinkService.createShortLink(dto, 1, true));
    }


    @PostMapping("/{sid}/verify/commit")
    @Operation(summary = "提交审核")
    public ResourceResponseDto<SurveyDto> commitVerify(@PathVariable Long sid, @RequestBody List<Long> userIds) {
        return new ResourceResponseDto<>(surveyVerifyService.commitVerify(sid, userIds));
    }

    @PostMapping("/{sid}/verify/withdraw")
    @Operation(summary = "撤销审核")
    public ResourceResponseDto<SurveyDto> withdrawSurvey(@PathVariable Long sid) {
        return new ResourceResponseDto<>(surveyVerifyService.withdrawSurvey(sid));
    }

    @PostMapping("/{sid}/verify/{status}")
    @Operation(summary = "立即审核")
    public ResourceResponseDto<SurveyDto> verifyNow(@PathVariable Long sid,
                                                    @Parameter(name = "status", description = "approval:通过，reject：驳回") @PathVariable String status,
                                                    @RequestBody(required = false) Map<String, Object> data) {
        //审核描述
        String comment = data.get("comment").toString();
        return new ResourceResponseDto<>(surveyVerifyService.verifyNow(sid, status, comment));
    }

    @GetMapping("/{sid}/verify")
    @Operation(summary = "审核问卷详情")
    public ResourceResponseDto<SurveyDto> getOneVerifySurvey(@PathVariable Long sid) {
        return new ResourceResponseDto<>(surveyVerifyService.getOneVerifySurvey(sid));
    }


    @GetMapping("/{id}/contentAudit")
    @Operation(summary = "问卷内容审核")
    public ResourceResponseDto<Boolean> contentAudit(@PathVariable long id, @RequestParam SurveyStatus status, @RequestParam(required = false) boolean manualAuditIfFail) {
        return new ResourceResponseDto<>(surveyContentAuditService.checkContentAudit(id, status, manualAuditIfFail));
    }

    @PostMapping("/{id}/changeOwner")
    @Operation(summary = "修改拥有者")
    public ResourceResponseDto<Boolean> changeOwner(@PathVariable long id, @Valid @RequestBody SurveyChangeOwnerDto dto) {
        return new ResourceResponseDto<>(service.changeOwner(id, dto.getTargetUserId()));
    }
    
}





















