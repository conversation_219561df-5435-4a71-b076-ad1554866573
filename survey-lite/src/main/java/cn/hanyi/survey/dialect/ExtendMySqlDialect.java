package cn.hanyi.survey.dialect;

import org.hibernate.dialect.MySQLDialect;
import org.hibernate.dialect.function.SQLFunctionTemplate;
import org.hibernate.type.StandardBasicTypes;

/**
 * The class description
 *
 * <AUTHOR>
 */
public class ExtendMySqlDialect extends MySQLDialect {

    public ExtendMySqlDialect() {
        super();
        registerFunction("DATE_FORMAT_DAY", new SQLFunctionTemplate(StandardBasicTypes.STRING, "DATE_FORMAT(?1, '%Y-%m-%d')"));
        registerFunction("DATE_FORMAT_MONTH", new SQLFunctionTemplate(StandardBasicTypes.STRING, "DATE_FORMAT(?1, '%Y-%n')"));
        registerFunction("DATE_FORMAT_YEAR", new SQLFunctionTemplate(StandardBasicTypes.STRING, "DATE_FORMAT(?1, '%Y')"));
    }
}
