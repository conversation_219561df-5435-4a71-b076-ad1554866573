package cn.hanyi.survey.dialect;

import org.hibernate.dialect.H2Dialect;
import org.hibernate.dialect.function.SQLFunctionTemplate;
import org.hibernate.type.StandardBasicTypes;

/**
 * The class description
 *
 * <AUTHOR>
 */
public class ExtendH2Dialect extends H2Dialect {

    public ExtendH2Dialect() {
        super();
        registerFunction("DATE_FORMAT_DAY", new SQLFunctionTemplate(StandardBasicTypes.STRING, "FORMATDATETIME(?1, 'yyyy-MM-dd')"));
        registerFunction("DATE_FORMAT_MONTH", new SQLFunctionTemplate(StandardBasicTypes.STRING, "FORMATDATETIME(?1, 'yyyy-MM')"));
        registerFunction("DATE_FORMAT_YEAR", new SQLFunctionTemplate(StandardBasicTypes.STRING, "FORMATDATETIME(?1, 'yyyy')"));
    }
}
