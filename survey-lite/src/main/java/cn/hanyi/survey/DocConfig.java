package cn.hanyi.survey;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import org.befun.extension.doc.OverrideQueryOperation;
import org.springdoc.core.GroupedOpenApi;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnProperty(name = "springdoc.api-docs.enabled", havingValue = "true")
public class DocConfig {

    @Bean
    public GroupedOpenApi doc() {
        String[] packagedToMatch = {"cn.hanyi.survey"};
        OverrideQueryOperation overrideQueryOperation = new OverrideQueryOperation();
        return GroupedOpenApi.builder()
                .group("survey-pc")
                .pathsToMatch("/**")
                .addOperationCustomizer(overrideQueryOperation)
                .addOpenApiCustomiser(overrideQueryOperation)
                .packagesToScan(packagedToMatch).build();
    }

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("survey api")
                        .version("1.8.0")
                        .description("survey doc")
                        .termsOfService("https://dev.xmplus.cn/api/doc/doc.html"));
    }
}
