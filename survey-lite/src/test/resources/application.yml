spring:
  flyway:
    enabled: false
  datasource:
    driverClassName: org.h2.Driver
    jdbc-url: jdbc:h2:mem:CEM_PLATFORM;DB_CLOSE_DELAY=-1
  datasource-cem:
    driverClassName: org.h2.Driver
    jdbc-url: jdbc:h2:mem:CEM_PLATFORM;DB_CLOSE_DELAY=-1
  jpa:
    show-sql: true
    hibernate:
      ddl-auto: create
      generate-ddl: true
    properties:
      hibernate:
        dialect: cn.hanyi.survey.dialect.ExtendH2Dialect
        enable_lazy_load_no_trans: true
  redis:
    host: localhost
    port: 6378


hanyi:
  common:
    ip-resolver:
      default-platform: local
      local:
        algorithm: memory
    file-storage:
      default-platform: ${FILE_PLATFORM:local}
      enable-delete: ${FILE_ENABLE_DELETE:true}
      local:
        - platform: local
          enable-storage: true
          enable-access: true
          domain: ${FILE_URL:https://dev.xmplus.cn/api/ctm/files?url=}
          base-path: ${FILE_PATH:/tmp/files/lite/}
          path-patterns: ${FILE_PATH_PATTERNS:/tmp/files/lite/**}

logging:
  level:
    root: ${LOG_LEVEL:info}
    org:
      hibernate:
        SQL: info
#        type: trace
consumer:
  group-default: backend-lite
  topic-register-user: queuing-user-create

survey:
  client.enabled: ${SURVEY_CLIENT_ENABLED:true}
  cem-domain: ${CEM_DOMAIN:https://dev.xmplus.cn/cem/}
  survey-url-prefix:
    root: ${SURVEY_URL_PREFIX:https://dev.xmplus.cn/lite/}
  quota:
    sync:
      size-per-page: 100

befun:
  task:
    prefix: befun.task
    trim:
      enabled: true
      cron: "*/10 * * * * *"
    scheduler:
      enabled: true
      cron: "*/10 * * * * *"
    worker:
      enabled: true
      interval-seconds: 5
      group: survey
  extension:
    wechat-open:
      enable: true
      component-app-id: ${WECHAT_OPEN_APP_ID:test}
      component-secret: ${WECHAT_OPEN_APP_SECRET:test}
      component-token: ${WECHAT_OPEN_TOKEN:test}
      component-aes-key: ${WECHAT_OPEN_AES_KEY:test}
    wechat-mp:
      enable: true
      app-id: ${WECHAT_MP_CEM_APP_ID:wx38eb115cf5014c74}
      app-secret: ${WECHAT_MP_CEM_APP_SECRET:03eca9a58e86f8e376e6cc2ea33a06c0}
      token: ${WECHAT_MP_CEM_TOKEN:surveyplus20190710surveyplus}
    wechat-miniprogram:
      enable: true
      app-id: ${WECHAT_MINIPROGRAM_APP_ID:wx11defe095afaf247}
      app-secret: ${WECHAT_MINIPROGRAM_SECRET:982e41fee09416518ebf67bc91f24740}
      token: ${WECHAT_MINIPROGRAM_TOKEN:surveyplus}
      aes-key: ${WECHAT_MINIPROGRAM_AES_KEY:0F68wpOxqNJs4bL5xUoCyrlWinYF6QNPtmYVx8Ex0BB}
      version: ${WECHAT_MINIPROGRAM_VERSION:develop}
    smart-verify:
      enable: true
      regionId: cn-hangzhou
      accessKeyId: LTAI4G5RfyxPtajMojKJPvmM
      accessKeySecret: ******************************
      product: afs
      domain: afs.aliyuncs.com
      appKey: FFFF0N00000000008B36
    shorturl:
      root: ${SHORTURL:https://dev-t.xmplus.cn}
      survey-client-prefix: ${SURVEY_CLIENT_PREFIX:https://dev.xmplus.cn/lite/l}
    wechat-pay:
      enable: ${WECHAT_PAY_ENABLE:true}
      app-id: ${WECHAT_PAY_APP_ID:wx38eb115cf5014c74}
      mch-id: ${WECHAT_PAY_MCH_ID:1521879911}
      v2-mch-key: ${WECHAT_PAY_V2_MCH_KEY:LDPpsBzLOqo6dVjfGj2wWXYZHa9UdcO7}
      v3-mch-key: ${WECHAT_PAY_V3_MCH_KEY:c0R7uY7aiRljM6eXLFYB2HBqZ3kw0KTN}
      cert-p12-path: ${WECHAT_PAY_CERT_P12_PATH:/config/cert/wx/apiclient_cert.p12}
      private-key-path: ${WECHAT_PAY_PRIVATE_KEY_PATH:/config/cert/wx/apiclient_key.pem}
      cert-pem-path: ${WECHAT_PAY_CERT_PEM_PATH:/config/cert/wx/apiclient_cert.pem}
      use-sandbox: ${WECHAT_PAY_USE_SANDBOX:false}
      pay-notify-url: ${WECHAT_PAY_NOTIFY_URL:${xmplus.domain}/api/auth/wechatPay/placeOrder/callback/cem}

worker:
  producer:
    event:
      enabled:
        auth: false
        survey: false
        ctm: false
    task:
      enabled:
        auth: false
        survey: false
        ctm: false