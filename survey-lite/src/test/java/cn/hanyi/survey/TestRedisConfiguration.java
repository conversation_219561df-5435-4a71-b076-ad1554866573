package cn.hanyi.survey;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.test.context.TestConfiguration;
import redis.embedded.RedisServer;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

/**
 * The class description
 *
 * <AUTHOR>
 */
@TestConfiguration
@Slf4j
public class TestRedisConfiguration {

    private RedisServer redisServer;

    public TestRedisConfiguration() {
        log.info("redis config init....");
        redisServer = RedisServer.builder().setting("maxmemory 200m").port(6378).build();
    }

    @PostConstruct
    public void postConstruct() {
        log.info("start redis server");
        redisServer.start();
    }

    @PreDestroy
    public void preDestroy() {
        log.info("stop redis server");
        redisServer.stop();
    }
}

