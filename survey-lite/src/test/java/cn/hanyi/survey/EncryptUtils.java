package cn.hanyi.survey;

import org.befun.auth.configuration.AuthProperties;
import org.befun.auth.utils.EncryptDecryptUtils;

public class EncryptUtils {

    public static void main(String[] args) {
        AuthProperties authProperties = new AuthProperties();

        String rsaPrivateKey = authProperties.getRsaPrivateKey();
        String rsaPaddingMode = authProperties.getRsaModPadding();
        String aesPaddingMode = authProperties.getAesModPadding();


        String encryptAesKey = "k33rJq3AFyLMaTHqyc8SeXuF4LphkvUN6XtnBHh1UgmQAeWTaTm8/j0TbkgKx2fgiXCZvAQH69UccJ0coQ9RYfwlPq3wkZytUcfvW85ED2tmYDBy0QDMjXeipZxQmKlWXP5BaBszVA3LL4DrjmqHWbbBnIw+Vju09lBkHpnvOtM=";
        String encryptData = "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";
        String aesKey = EncryptDecryptUtils.decryptRsa(rsaPrivateKey, rsaPaddingMode, encryptAesKey);
        System.out.println("aesKey:\n" + aesKey);
        String data = EncryptDecryptUtils.decryptAes(aesKey, aesPaddingMode, encryptData);
        System.out.println("data:\n" + data);
    }
}
