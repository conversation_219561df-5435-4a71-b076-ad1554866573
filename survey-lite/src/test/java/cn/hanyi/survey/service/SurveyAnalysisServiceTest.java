package cn.hanyi.survey.core.service;

import cn.hanyi.survey.BaseSurveyTest;
import cn.hanyi.survey.TestRedisConfiguration;
import cn.hanyi.survey.core.constant.ResponseStatus;
import cn.hanyi.survey.core.constant.analysis.GranularityType;
import cn.hanyi.survey.core.constant.analysis.MeasureType;
import cn.hanyi.survey.core.constant.channel.SurveyCollectorMethod;
import cn.hanyi.survey.dto.analysis.CubeDto;
import cn.hanyi.survey.dto.analysis.DimensionDto;
import cn.hanyi.survey.dto.analysis.FilterDto;
import cn.hanyi.survey.dto.analysis.MeasureDto;
import cn.hanyi.survey.service.AnalyticService;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Sort;
import org.springframework.test.annotation.DirtiesContext;

import java.text.ParseException;
import java.time.LocalDate;
import java.util.*;

/**
 * The class description
 *
 * <AUTHOR>
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {TestRedisConfiguration.class})
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
public class SurveyAnalysisServiceTest extends BaseSurveyTest {

    @Autowired
    AnalyticService analyticService;

    @Test
    public void simpleCount() throws NoSuchFieldException {
        // count(id)
        MeasureDto measure = MeasureDto.builder()
                .field("id")
                .name("count(id)")
                .title("数量")
                .type(MeasureType.COUNT)
                .build();
        DimensionDto dimension = DimensionDto.builder()
                .field("province")
                .name("province")
                .title("省")
                .build();
        CubeDto cube = new CubeDto();
        cube.getDimensions().add(dimension);
        cube.getMeasures().add(measure);

        List<Map<String, Object>> result = analyticService.queryByCube(s1, cube);
        Assert.assertSame(2, result.size());
        Assert.assertSame(Long.valueOf(2), result.get(0).get("count(id)"));
        Assert.assertSame(Long.valueOf(1), result.get(1).get("count(id)"));
    }

    @Test
    public void simpleCountAndFilter() throws NoSuchFieldException {
        // count(id)
        MeasureDto measure = MeasureDto.builder()
                .field("id")
                .name("count(id)")
                .title("数量")
                .type(MeasureType.COUNT)
                .build();
        DimensionDto dimension = DimensionDto.builder()
                .field("province")
                .name("province")
                .title("省")
                .build();
        FilterDto filter = FilterDto.builder()
                .name("province")
                .value("guangdong")
                .operator("eq")
                .build();
        CubeDto cube = new CubeDto();
        cube.getDimensions().add(dimension);
        cube.getMeasures().add(measure);
        cube.getFilters().add(filter);

        List<Map<String, Object>> result = analyticService.queryByCube(s1, cube);
        Assert.assertSame(1, result.size());
        Assert.assertSame(Long.valueOf(2), result.get(0).get("count(id)"));
    }

    @Test
    public void averageDurationSeconds() throws NoSuchFieldException {
        // count(id)
        MeasureDto measure = MeasureDto.builder()
                .field("durationSeconds")
                .name("duration")
                .title("平均答题时长")
                .type(MeasureType.AVG)
                .build();
        CubeDto cube = new CubeDto();
        cube.getMeasures().add(measure);

        List<Map<String, Object>> result = analyticService.queryByCube(s1, cube);
        Assert.assertFalse(result.isEmpty());
        Assert.assertEquals((double) ((10L + 5L + 6L) / 3), result.get(0).get("duration"));
    }

    @Test
    public void countByToday() throws NoSuchFieldException, ParseException {
        // count(id)
        MeasureDto measure = MeasureDto.builder()
                .field("id")
                .name("count")
                .title("数量")
                .type(MeasureType.COUNT)
                .build();
        DimensionDto dimension = DimensionDto.builder()
                .field("createTime")
                .name("createTime")
                .granularity(GranularityType.DAY)
                .build();
        FilterDto filter = FilterDto.builder()
                .name("createTime")
                .value(LocalDate.now().toString())
                .operator("gte")
                .build();

        CubeDto cube = new CubeDto();
        cube.getDimensions().add(dimension);
        cube.getMeasures().add(measure);
        cube.getFilters().add(filter);
        cube.setLimit(1);

        List<Map<String, Object>> result = analyticService.queryByCube(s2, cube);
        Assert.assertEquals(1, result.size());
        Assert.assertEquals(Long.valueOf(1), result.get(0).get("count"));
        Assert.assertEquals(LocalDate.now().toString(), result.get(0).get("createTime"));
    }

    @Test
    public void countByCreateTimeDay() throws NoSuchFieldException {
        // count(id)
        MeasureDto measure = MeasureDto.builder()
                .field("id")
                .name("count")
                .title("数量")
                .type(MeasureType.COUNT)
                .build();
        DimensionDto dimension = DimensionDto.builder()
                .field("createTime")
                .name("createTime")
                .granularity(GranularityType.DAY)
                .build();
        CubeDto cube = new CubeDto();
        cube.getDimensions().add(dimension);
        cube.getMeasures().add(measure);

        List<Map<String, Object>> result = analyticService.queryByCube(s1, cube);
        Assert.assertEquals(2, result.size());
        Assert.assertEquals(Long.valueOf(2), result.get(0).get("count"));
        Assert.assertEquals("2021-05-01", result.get(0).get("createTime"));
        Assert.assertEquals(Long.valueOf(1), result.get(1).get("count"));
        Assert.assertEquals("2021-05-02", result.get(1).get("createTime"));
    }

    @Test
    public void trendsByColelctorMethodAndCreateTimeDay() throws NoSuchFieldException {
        // count(id)
        FilterDto c_link = FilterDto.builder()
                .operator("eq")
                .name("collectorMethod")
                .value(SurveyCollectorMethod.LINK)
                .build();

        FilterDto c_embedded = FilterDto.builder()
                .operator("eq")
                .name("collectorMethod")
                .value(SurveyCollectorMethod.EMBEDDED)
                .build();

        List<FilterDto> fc1 = new ArrayList<>(Arrays.asList(new FilterDto[]{c_link}));
        List<FilterDto> fc2 = new ArrayList<>(Arrays.asList(new FilterDto[]{c_embedded}));

        MeasureDto m0 = MeasureDto.builder()
                .field("id")
                .name("total")
                .title("全部")
                .type(MeasureType.COUNT)
                .build();
        MeasureDto m1 = MeasureDto.builder()
                .field("id")
                .name("link")
                .title("链接")
                .filters(fc1)
                .type(MeasureType.COUNT)
                .build();
        MeasureDto m2 = MeasureDto.builder()
                .field("id")
                .name("embedded")
                .title("嵌入")
                .filters(fc2)
                .type(MeasureType.COUNT)
                .build();
        DimensionDto dimension = DimensionDto.builder()
                .field("createTime")
                .name("createTime")
                .granularity(GranularityType.DAY)
                .build();
        CubeDto cube = new CubeDto();
        cube.getMeasures().add(m0);
        cube.getMeasures().add(m1);
        cube.getMeasures().add(m2);
        cube.getDimensions().add(dimension);

        List<Map<String, Object>> result = analyticService.queryByCube(s2, cube);
        Assert.assertEquals(2, result.size());
        Assert.assertEquals(Long.valueOf(2), result.get(0).get("total"));
        Assert.assertEquals(Long.valueOf(2), result.get(0).get("link"));
        Assert.assertEquals(Long.valueOf(0), result.get(0).get("embedded"));
        Assert.assertEquals("2021-07-01", result.get(0).get("createTime"));
        Assert.assertEquals(Long.valueOf(1), result.get(1).get("total"));
        Assert.assertEquals(Long.valueOf(1), result.get(1).get("link"));
        Assert.assertEquals(Long.valueOf(0), result.get(1).get("embedded"));
        Assert.assertEquals(LocalDate.now().toString(), result.get(1).get("createTime"));
    }

    @Test
    public void countWithFilter() throws NoSuchFieldException {
        // count(id)
        FilterDto s_0 = FilterDto.builder()
                .operator("eq")
                .name("status")
                .value(Objects.toString(ResponseStatus.FINAL_SUBMIT))
                .build();
        List<FilterDto> fs1 = new ArrayList<>(Arrays.asList(new FilterDto[]{s_0}));

        MeasureDto m1 = MeasureDto.builder()
                .field("id")
                .name("success_count")
                .title("成功数量")
                .filters(fs1)
                .type(MeasureType.COUNT)
                .build();
        MeasureDto m2 = MeasureDto.builder()
                .field("id")
                .name("total_count")
                .title("总数量")
                .type(MeasureType.COUNT)
                .build();
        CubeDto cube = new CubeDto();
        cube.getMeasures().add(m1);
        cube.getMeasures().add(m2);

        List<Map<String, Object>> result = analyticService.queryByCube(s1, cube);
        Assert.assertSame(1, result.size());
        Assert.assertSame(Long.valueOf(1), result.get(0).get("success_count"));
        Assert.assertSame(Long.valueOf(3), result.get(0).get("total_count"));
    }

    @Test
    public void orderByField() throws NoSuchFieldException {
        // count(id)
        MeasureDto measure = MeasureDto.builder()
                .field("id")
                .name("count")
                .title("数量")
                .type(MeasureType.COUNT)
                .build();
        DimensionDto dimension = DimensionDto.builder()
                .field("province")
                .name("province")
                .title("省")
                .build();
        CubeDto cube = new CubeDto();
        cube.getDimensions().add(dimension);
        cube.getMeasures().add(measure);

        cube.setSort(Sort.by(Sort.Direction.DESC, "id"));
        List<Map<String, Object>> result_desc = analyticService.queryByCube(s1, cube);
        Assert.assertSame(2, result_desc.size());
        Long desc_one = (Long) result_desc.get(0).get("count");
        Long desc_two = (Long) result_desc.get(1).get("count");
        Assert.assertTrue(desc_one > desc_two);


        cube.setSort(Sort.by(Sort.Direction.ASC, "id"));
        List<Map<String, Object>> result_asc = analyticService.queryByCube(s1, cube);
        Assert.assertSame(2, result_asc.size());
        Long asc_one = (Long) result_asc.get(0).get("count");
        Long asc_two = (Long) result_asc.get(1).get("count");
        Assert.assertTrue(asc_one < asc_two);
    }

}