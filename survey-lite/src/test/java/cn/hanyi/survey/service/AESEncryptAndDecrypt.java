package cn.hanyi.survey.core.service;

import cn.hanyi.survey.TestRedisConfiguration;
import cn.hanyi.survey.core.utilis.AESUtils;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.DirtiesContext;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {TestRedisConfiguration.class})
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
public class AESEncryptAndDecrypt {

    @SneakyThrows
    @Test
    public void encryptAndDecrypt() {
        String key = "ycb6A7jcexyDPkbYHBFRshvScMKuNl8B";
        String text = "汉AbC!?>";
        String encrypt = AESUtils.encrypt(key, text);
        Assertions.assertEquals(text, AESUtils.decrypt(key, encrypt));
    }

}
