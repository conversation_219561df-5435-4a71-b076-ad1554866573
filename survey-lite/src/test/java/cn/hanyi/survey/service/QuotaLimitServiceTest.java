package cn.hanyi.survey.core.service;

import cn.hanyi.survey.BaseSurveyTest;
import cn.hanyi.survey.TestRedisConfiguration;
import cn.hanyi.survey.service.quota.QuotaLimit;
import cn.hanyi.survey.service.quota.bean.QuotaInit;
import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.DirtiesContext;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * The class description
 *
 * <AUTHOR>
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {TestRedisConfiguration.class})
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
public class QuotaLimitServiceTest extends BaseSurveyTest {

    @Autowired
    private QuotaLimit quotaLimit;

    @Test
    public void simpleCount() {
//        quotaLimit.syncQuotaStat(1L,
//                List.of(
//                        new QuotaInit(1, 100),
//                        new QuotaInit(1, 200),
//                        new QuotaInit(1, 300),
//                        new QuotaInit(1, 400)
//                ),
//                List.of(1L, 2L),
//                999L,
//                (Long surveyId, List<Long> quotaIds, int page, int size) -> {
//                    int step = (page - 1) * size;
//                    Map<Long, List<Long>> r = new HashMap<>();
//                    quotaIds.forEach(q -> {
//                        List<Long> rids = IntStream.range(0, 99).parallel().filter(i -> RandomUtils.nextBoolean()).mapToObj(j -> (long) (j + step)).collect(Collectors.toList());
//                        r.put(q, rids);
//                    });
//                    return r;
//                },
//                (success, stat) -> {
////                    System.out.println(quotaLimit.hasQuota(1L));
//                    System.out.println(stat);
////                    Assertions.assertTrue(quotaLimit.hasQuota(1L));
//                    Assertions.assertTrue(quotaLimit.useQuota(1L, 1L, List.of(1L, 2L, 3L, 4L)));
//                });
    }

}