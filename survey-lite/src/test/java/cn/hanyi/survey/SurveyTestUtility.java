package cn.hanyi.survey;

import cn.hanyi.survey.core.constant.ResponseStatus;
import cn.hanyi.survey.core.constant.question.InputType;
import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.constant.survey.SurveyStatus;
import cn.hanyi.survey.core.entity.*;
import lombok.SneakyThrows;
import org.befun.core.dto.UserDto;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * The class description
 *
 * <AUTHOR>
 */
public class SurveyTestUtility {

    @SneakyThrows
    public static SurveyQuota buildQuota(SurveyQuestion question, String expression, int max) {
        SurveyQuota quota = new SurveyQuota();
        quota.setSurvey(question.getSurvey());
        quota.setExpression(expression);
        quota.setMax(max);
        return quota;
    }

    public static SurveyResponse buildResponse(Survey survey, String province, boolean isCompelted, ResponseStatus status) {
        return buildResponse(survey, province, isCompelted, status, null);
    }

    @SneakyThrows
    public static SurveyResponse buildResponse(Survey survey, String province, boolean isCompelted, ResponseStatus status, Date createTime) {

        SurveyResponse response = new SurveyResponse();
        response.setSurveyId(survey.getId());
        response.setProvince(province);
        response.setIsCompleted(isCompelted);
        response.setStatus(status);
        response.setOrgId(survey.getOrgId());
        if (createTime != null) {
            response.setCreateTime(createTime);
        }
        return response;
    }

    public static SurveyResponseCell buildResponseCell(Survey survey, SurveyQuestion question, SurveyResponse response, Object value) {
        SurveyResponseCell cell = new SurveyResponseCell(survey, question, response, value);
        return cell;
    }

    public static SurveyQuestion buildQuestion(Survey survey, String name, QuestionType type) {
        SurveyQuestion question = new SurveyQuestion();
        question.setName(name);
        question.setTitle(name + " description");
        question.setType(type);
        question.setSurvey(survey);
        return question;
    }

    public static Survey buildSurvey(int index, QuestionType[] types, int itemSize, UserDto user) {
        Survey survey = new Survey();
        survey.setTitle(String.format("survey_%d", index));
        survey.setStatus(SurveyStatus.COLLECTING);
        survey.setOrgId(user.getOrgId());
        survey.setUserId(user.getId());
        survey.setWelcomingRemark(String.format("survey_%d welcome", index));

        // prepare questions
        List<SurveyQuestion> questions = new ArrayList<>();
        for (int i = 0; i < types.length; i++) {
            QuestionType type = types[i];
            SurveyQuestion question = buildQuestion(survey, String.format("q%d", i + 1), type);
            switch (type) {
                case EMPTY:
                case MARK:
                case SEPARATOR:
                case TEXT:
                    break;
                case SCORE:
                    question.setInputType(InputType.NUMBER);
                    question.setMin(0);
                    question.setMax(10);
                case NUMBER:
                case MOBILE:
                case EMAIL:
                case DROP_DOWN:
                case FILE:
                case MEDIA:
                    break;
                case MATRIX_CHOICE:
                    List<SurveyQuestionItem> items_text = new ArrayList<>();
                    SurveyQuestionItem item_text = new SurveyQuestionItem();
                    item_text.setText("c1 text");
                    item_text.setValue("c1");
                    item_text.setSequence(1);
                    item_text.setQuestion(question);
                    items_text.add(item_text);
                    question.setItems(new ArrayList<>(items_text));
                    break;
                case SINGLE_CHOICE:
                case AREA:
                case MULTIPLE_CHOICES:
                case MATRIX_SCORE:
                case MATRIX_SLIDER:
                    List<SurveyQuestionItem> items = new ArrayList<>();
                    for (int j = 0; j < itemSize; j++) {
                        SurveyQuestionItem item = new SurveyQuestionItem();
                        item.setText(String.format("c%d text", j + 1));
                        item.setValue(String.format("c%d", j + 1));
                        item.setSequence(j);
                        item.setQuestion(question);
                        items.add(item);
                    }
                    question.setItems(new ArrayList<>(items));
                    break;
            }
            question.setSequence(BigDecimal.valueOf(i + 1));
            questions.add(question);
        }

        survey.setQuestions(questions);
        return survey;
    }
}
