package cn.hanyi.survey.repository;

import cn.hanyi.survey.BaseSurveyTest;
import cn.hanyi.survey.TestRedisConfiguration;
import cn.hanyi.survey.core.constant.survey.LogicType;
import cn.hanyi.survey.core.constant.survey.SurveyStatus;
import cn.hanyi.survey.core.entity.*;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.DirtiesContext;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * The class description
 *
 * <AUTHOR>
 */
@SpringBootTest(webEnvironment= SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {TestRedisConfiguration.class})
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
public class SurveyRepositoryIntegrationTest extends BaseSurveyTest {

    @Test
    public void cloneSurvey() throws Exception {
        s1.setStatus(SurveyStatus.COLLECTING);
        Survey n1 = (Survey) s1.clone();

        Assert.assertEquals("survey_1_复制", n1.getTitle());
        Assert.assertEquals(n1.getWelcomingRemark(), s1.getWelcomingRemark());
        Assert.assertEquals(n1.getStatus(), SurveyStatus.STOPPED);
        Assert.assertEquals(n1.getEnableQuota(), s1.getEnableQuota());
        Assert.assertEquals(n1.getBackgroundImage(), s1.getBackgroundImage());
        Assert.assertEquals(n1.getHeaderImageMobile(), s1.getHeaderImageMobile());
        Assert.assertEquals(n1.getHeaderImagePc(), s1.getHeaderImagePc());
        Assert.assertEquals(n1.getLanguage(), s1.getLanguage());
        Assert.assertEquals(n1.getLogoFit(), s1.getLogoFit());
        Assert.assertEquals(n1.getShowBackground(), s1.getShowBackground());

        SurveyStyle n1_style = n1.getStyle();

        Assert.assertEquals(s1_style.getFontSize(), n1_style.getFontSize());
        Assert.assertEquals(s1_style.getMainColor(), n1_style.getMainColor());
        Assert.assertEquals(s1_style.getTitleColor(), n1_style.getTitleColor());
        Assert.assertEquals(s1_style.getWelcomingColor(), n1_style.getWelcomingColor());
        Assert.assertEquals(s1_style.getQuestionColor(), n1_style.getQuestionColor());
        Assert.assertEquals(s1_style.getItemColor(), n1_style.getItemColor());
        Assert.assertEquals(s1_style.getButtonColor(), n1_style.getButtonColor());
        Assert.assertEquals(s1_style.getBackgroundColor(), n1_style.getBackgroundColor());
        Assert.assertEquals(s1_style.getBackgroundOpacity(), n1_style.getBackgroundOpacity());

        Assert.assertEquals(3, s1.getQuestions().size());

        Map<String, SurveyQuestion> n1Questions = n1.getQuestions().stream()
                .collect(Collectors.toMap(SurveyQuestion::getName, Function.identity()));

        SurveyQuestion n1_q1 = n1Questions.get("q1");

        Assert.assertNotEquals(s1_q1, n1_q1);
        Assert.assertNotEquals(s1_q1.getId(), n1_q1.getId());
        Assert.assertEquals(s1_q1.getName(), n1_q1.getName());
        Assert.assertEquals(s1_q1.getTitle(), n1_q1.getTitle());
        Assert.assertEquals(s1_q1.getType(), n1_q1.getType());
        Assert.assertEquals(s1_q1.getItems().size(), n1_q1.getItems().size());

        SurveyQuestion n1_q2 = n1Questions.get("q2");
        Map<String, SurveyQuestionItem> n1_q2_items = n1_q2.getItems().stream()
                .collect(Collectors.toMap(SurveyQuestionItem::getValue, Function.identity()));

        SurveyQuestionItem n1_q2_c1 = n1_q2_items.get("c1");
        Assert.assertNotEquals(s1_q2_c1, n1_q2_c1);
        Assert.assertNotEquals(s1_q2_c1.getId(), n1_q2_c1.getId());
        Assert.assertEquals(s1_q2_c1.getText(), n1_q2_c1.getText());

        SurveyLogic n1_l1 = n1.getLogics().get(0);
        Assert.assertEquals(1, n1.getLogics().size());
        Assert.assertEquals("q1.value == \"123\"", n1_l1.getExpression());
        Assert.assertEquals(LogicType.GOTO, n1_l1.getType());
        Assert.assertEquals(s1_q2.getName(), n1_l1.getTarget());
    }
}
