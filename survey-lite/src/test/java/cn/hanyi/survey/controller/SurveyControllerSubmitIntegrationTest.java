package cn.hanyi.survey.controller;

import cn.hanyi.survey.BaseSurveyTest;
import cn.hanyi.survey.TestRedisConfiguration;
import cn.hanyi.survey.core.constant.ResponseStatus;
import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.dto.SurveySubmitRequestDto;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.core.entity.SurveyResponse;
import cn.hanyi.survey.core.entity.SurveyResponseCell;
import org.befun.core.entity.BaseEntity;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.web.servlet.MockMvc;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * The class description
 *
 * <AUTHOR>
 */
@SpringBootTest(
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
        classes = {TestRedisConfiguration.class})
@AutoConfigureMockMvc
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
public class SurveyControllerSubmitIntegrationTest extends BaseSurveyTest {
    @Autowired
    protected MockMvc mvc;

    @Test
    public void submitWithEmptyClientId() throws Exception {
        Map<String, Object> answer = new HashMap<>();
        answer.put("q1", "hello");
        answer.put("q2", "c1");
        answer.put("q3", new String[]{"c1", "c2"});
        SurveySubmitRequestDto request = new SurveySubmitRequestDto();
        request.setData(answer);
        request.setIsCompleted(false);
        request.setDurationSeconds(10);
        request.setIsEarlyCompleted(true);

        mvc.perform(post(String.format("/surveys/%d/submit", s1.getId()))
                        .content(objectMapper.writeValueAsString(request))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON));
    }

    @Test
    public void submitWithWrongStatus() throws Exception {
        String clientId = "random_client_01";
        Map<String, Object> answer = new HashMap<>();
        answer.put("q1", "hello");
        answer.put("q2", "c1");
        answer.put("q3", new String[]{"c1", "c2"});
        SurveySubmitRequestDto request = new SurveySubmitRequestDto();
        request.setClientId(clientId);
        request.setData(answer);
        request.setIsCompleted(false);
        request.setDurationSeconds(10);
        request.setIsEarlyCompleted(true);

        mvc.perform(post(String.format("/surveys/%d/submit", s1.getId()))
                        .content(objectMapper.writeValueAsString(request))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON));
    }

    @Test
    public void submitPartialCompleted() throws Exception {
        String clientId = "random_client_01";
        Map<String, Object> answer = new HashMap<>();
        answer.put("q1", "hello");
        answer.put("q2", "c1");
        answer.put("q3", new String[]{"c1", "c2"});
        SurveySubmitRequestDto request = new SurveySubmitRequestDto();
        request.setClientId(clientId);
        request.setData(answer);
        request.setDurationSeconds(10);
        request.setIsCompleted(false);

        mvc.perform(post(String.format("/surveys/%d/submit", s1.getId()))
                        .content(objectMapper.writeValueAsString(request))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));

        Optional<SurveyResponse> response = responseRepository.findOneBySurveyIdAndClientIdOrderByCreateTimeAsc(s1.getId(), clientId);
        Assert.assertTrue(response.isPresent());
        Assert.assertEquals(ResponseStatus.INIT, response.get().getStatus());
        Assert.assertEquals("127.0.0.1", response.get().getIp());
        Assert.assertNull(response.get().getDurationSeconds());

        Collection<SurveyResponseCell> cells = response.get().getCells();
        Assert.assertEquals(3, cells.size());

        Map<Long, SurveyQuestion> questionIdMap = surveyRepository.findById(s1.getId()).get().getQuestions().stream().collect(Collectors.toMap(BaseEntity::getId,Function.identity()));

        Map<String, SurveyResponseCell> cellsMap = cells.stream()
                .collect(Collectors.toMap(cell -> questionIdMap.get(cell.getQuestionId()).getName(), Function.identity()));
        Assert.assertEquals("hello", cellsMap.get("q1").getStrValue());
        Assert.assertEquals("c1", cellsMap.get("q2").getStrValue());
        Assert.assertEquals(new ArrayList<String>(Arrays.asList("c1", "c2")), cellsMap.get("q3").getValue());

        // could continue submit
        mvc.perform(post(String.format("/surveys/%d/submit", s1.getId()))
                        .content(objectMapper.writeValueAsString(request))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void submitWithNumber() throws Exception {
        String clientId = "random_client_01";
        Map<String, Object> answer = new HashMap<>();
        answer.put("q1", "hello");
        answer.put("q2", "c1");
        answer.put("q3", 1.0);
        answer.put("q4", 1);
        SurveySubmitRequestDto request = new SurveySubmitRequestDto();
        request.setClientId(clientId);
        request.setIsEarlyCompleted(false);
        request.setIsCompleted(true);
        request.setDurationSeconds(10);
        request.setData(answer);

        mvc.perform(post(String.format("/surveys/%d/submit", s3.getId()))
                        .content(objectMapper.writeValueAsString(request))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));

        Optional<SurveyResponse> response = responseRepository.findOneBySurveyIdAndClientIdOrderByCreateTimeAsc(s3.getId(), clientId);

        Collection<SurveyResponseCell> cells = response.get().getCells();
        Assert.assertEquals(4, cells.size());

        Map<Long, SurveyQuestion> questionIdMap = surveyRepository.findById(s3.getId()).get().getQuestions().stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));

        Map<String, SurveyResponseCell> cellsMap = cells.stream()
                .collect(Collectors.toMap(cell -> questionIdMap.get(cell.getQuestionId()).getName(), Function.identity()));
        Assert.assertEquals("hello", cellsMap.get("q1").getStrValue());
        Assert.assertEquals("c1", cellsMap.get("q2").getStrValue());
        Assert.assertEquals(1.0, cellsMap.get("q3").getValue());
        Assert.assertEquals(1.0, cellsMap.get("q4").getValue());

//        Consumer<Integer, String> consumer = configureConsumer(kafkaEmbeded, TOPIC_SURVEY_RESPONSE);
//        ConsumerRecord<Integer, String> singleRecord = KafkaTestUtils.getSingleRecord(consumer, TOPIC_SURVEY_RESPONSE);
//        Assert.assertNotNull(singleRecord.value());

//        ObjectMapper mapper = new ObjectMapper();
//        SurveyResponseMessageDto messageDto = mapper.readValue(singleRecord.value(), SurveyResponseMessageDto.class);
//        Assert.assertEquals(s3.getId(), messageDto.getSurveyId());
//        consumer.close();
    }

    @Test
    public void submitWithFullCompleted() throws Exception {
        String clientId = "random_client_01";
        Map<String, Object> answer = new HashMap<>();
        answer.put("q1", "hello");
        answer.put("q2", "c1");
        answer.put("q3", new String[]{"c1", "c2"});
        SurveySubmitRequestDto request = new SurveySubmitRequestDto();
        request.setClientId(clientId);
        request.setIsEarlyCompleted(false);
        request.setIsCompleted(true);
        request.setDurationSeconds(10);
        request.setData(answer);

        mvc.perform(post(String.format("/surveys/%d/submit", s1.getId()))
                        .content(objectMapper.writeValueAsString(request))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));

        Optional<SurveyResponse> response = responseRepository.findOneBySurveyIdAndClientIdOrderByCreateTimeAsc(s1.getId(), clientId);
        Assert.assertTrue(response.isPresent());
        Assert.assertEquals(ResponseStatus.FINAL_SUBMIT, response.get().getStatus());
        Assert.assertEquals("127.0.0.1", response.get().getIp());
        Assert.assertEquals(Optional.of(10).get(), response.get().getDurationSeconds());

        Collection<SurveyResponseCell> cells = response.get().getCells();
        Assert.assertEquals(3, cells.size());

        Map<Long, SurveyQuestion> questionIdMap = surveyRepository.findById(s1.getId()).get().getQuestions().stream().collect(Collectors.toMap(BaseEntity::getId,Function.identity()));

        Map<String, SurveyResponseCell> cellsMap = cells.stream()
                .collect(Collectors.toMap(cell -> questionIdMap.get(cell.getQuestionId()).getName(), Function.identity()));
        Assert.assertEquals("hello", cellsMap.get("q1").getStrValue());
        Assert.assertEquals("c1", cellsMap.get("q2").getStrValue());
        Assert.assertEquals(new ArrayList<String>(Arrays.asList("c1", "c2")), cellsMap.get("q3").getValue());

        // response already completed, should reject any future submit
        mvc.perform(post(String.format("/surveys/%d/submit", s1.getId()))
                        .content(objectMapper.writeValueAsString(request))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    @Test
    public void submitWithFullQuestionType() throws Exception {
        String clientId = "random_client_01";
        Map<String, Object> answer = new HashMap<>();
        Map<String, Integer> matrixScore = Map.of(
                "c1", 5,
                "c2", 6,
                "c3", 7
        );

        answer.put("q3", "text");
        answer.put("q4", "c1");
        answer.put("q5", new String[]{"c1", "c2"});
        answer.put("q7", 10);
        answer.put("q8", 18);
        answer.put("q9", "13333333333");
        answer.put("q10", "<EMAIL>");
        answer.put("q11", 5);
        answer.put("q12", matrixScore);
        answer.put("q13", new String[]{"省", "市", "区"});

        SurveySubmitRequestDto request = new SurveySubmitRequestDto();
        request.setClientId(clientId);
        request.setIsEarlyCompleted(false);
        request.setIsCompleted(true);
        request.setDurationSeconds(10);
        request.setData(answer);

        mvc.perform(post(String.format("/surveys/%d/submit", sf.getId()))
                        .content(objectMapper.writeValueAsString(request))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));

        Optional<SurveyResponse> surveyResponse = responseRepository.findOneBySurveyIdAndClientIdOrderByCreateTimeAsc(sf.getId(), clientId);
        List<SurveyResponseCell> cells = cellRepository.findAllBySurveyIdAndResponseId(sf.getId(), surveyResponse.get().getId());
        Map<String, SurveyResponseCell> cellsMap = cells.stream()
                .collect(Collectors.toMap(cell -> cell.getType().name(), Function.identity()));
        Assert.assertFalse(cellsMap.get(QuestionType.TEXT.name()).getStrValue().isEmpty());
        Assert.assertFalse(cellsMap.get(QuestionType.SINGLE_CHOICE.name()).getStrValue().isEmpty());
        Assert.assertFalse(cellsMap.get(QuestionType.MULTIPLE_CHOICES.name()).getStrValue().isEmpty());
        Assert.assertFalse(null == cellsMap.get(QuestionType.SCORE.name()).getIntValue());
        Assert.assertFalse(null == cellsMap.get(QuestionType.NUMBER.name()).getDoubleValue());
        Assert.assertFalse(cellsMap.get(QuestionType.MOBILE.name()).getStrValue().isEmpty());
        Assert.assertFalse(cellsMap.get(QuestionType.EMAIL.name()).getStrValue().isEmpty());
        Assert.assertFalse(null == cellsMap.get(QuestionType.MATRIX.name()).getIntValue());
        Assert.assertFalse(cellsMap.get(QuestionType.MATRIX_SCORE.name()).getJsonValue().isEmpty());
        Assert.assertFalse(cellsMap.get(QuestionType.AREA.name()).getStrValue().isEmpty());
    }

    @Test
    public void submitWithEarlyCompleted() throws Exception {
        String clientId = "random_client_01";
        Map<String, Object> answer = new HashMap<>();
        answer.put("q1", "hello");
        answer.put("q2", "c1");
        answer.put("q3", new String[]{"c1", "c2"});
        SurveySubmitRequestDto request = new SurveySubmitRequestDto();
        request.setClientId(clientId);
        request.setIsEarlyCompleted(true);
        request.setIsCompleted(true);
        request.setData(answer);

        mvc.perform(post(String.format("/surveys/%d/submit", s1.getId()))
                        .content(objectMapper.writeValueAsString(request))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));

        Optional<SurveyResponse> response = responseRepository.findOneBySurveyIdAndClientIdOrderByCreateTimeAsc(s1.getId(), clientId);
        Assert.assertTrue(response.isPresent());
        Assert.assertEquals(ResponseStatus.EARLY_COMPLETED, response.get().getStatus());
        Assert.assertEquals(response.get().getIp(), "127.0.0.1");

        Collection<SurveyResponseCell> cells = response.get().getCells();
        Assert.assertEquals(3, cells.size());

        Map<Long, SurveyQuestion> questionIdMap = surveyRepository.findById(s1.getId()).get().getQuestions().stream().collect(Collectors.toMap(BaseEntity::getId,Function.identity()));

        Map<String, SurveyResponseCell> cellsMap = cells.stream()
                .collect(Collectors.toMap(cell -> questionIdMap.get(cell.getQuestionId()).getName(), Function.identity()));
        Assert.assertEquals("hello", cellsMap.get("q1").getStrValue());
        Assert.assertEquals("c1", cellsMap.get("q2").getStrValue());
        Assert.assertEquals(new ArrayList<String>(Arrays.asList("c1", "c2")), cellsMap.get("q3").getValue());
        // response already completed, should reject any future submit
        mvc.perform(post(String.format("/surveys/%d/submit", s1.getId()))
                        .content(objectMapper.writeValueAsString(request))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }
}
