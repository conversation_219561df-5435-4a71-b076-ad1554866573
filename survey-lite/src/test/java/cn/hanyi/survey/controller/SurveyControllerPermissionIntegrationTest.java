package cn.hanyi.survey.controller;

import cn.hanyi.survey.BaseSurveyTest;
import cn.hanyi.survey.TestRedisConfiguration;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.web.servlet.MockMvc;

import java.util.HashMap;
import java.util.Map;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * The class description
 *
 * <AUTHOR>
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {TestRedisConfiguration.class})
@AutoConfigureMockMvc
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
public class SurveyControllerPermissionIntegrationTest extends BaseSurveyTest {
    @Autowired
    protected MockMvc mvc;

    @Test
    public void createSurveyWithoutTokenShouldBeRejected() throws Exception {
        Map<String, Object> data = new HashMap<>();
        mvc.perform(post(String.format("/surveys"))
                        .content(objectMapper.writeValueAsString(data))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isUnauthorized());
    }

    @Test
    public void createQuestionWithoutTokenShouldBeRejected() throws Exception {
        Map<String, Object> data = new HashMap<>();
        mvc.perform(post(String.format("/surveys/1/questions"))
                        .content(objectMapper.writeValueAsString(data))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isUnauthorized());
    }

    @Test
    public void createItemsWithoutTokenShouldBeRejected() throws Exception {
        Map<String, Object> data = new HashMap<>();
        mvc.perform(post(String.format("/surveys/1/questions/1/items"))
                        .content(objectMapper.writeValueAsString(data))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isUnauthorized());
    }

}
