package cn.hanyi.survey.controller;

import cn.hanyi.survey.BaseSurveyTest;
import cn.hanyi.survey.TestRedisConfiguration;
import cn.hanyi.survey.core.constant.Template.TemplateType;
import cn.hanyi.survey.core.entity.template.TemplateSurveyQuestion;
import cn.hanyi.survey.dto.template.*;
import cn.hanyi.survey.service.SurveyService;
import org.befun.core.utils.JsonHelper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import javax.persistence.EntityManager;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * The class description
 *
 * <AUTHOR>
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {TestRedisConfiguration.class})
@AutoConfigureMockMvc
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
public class SurveyTemplateTest extends BaseSurveyTest {
    @Autowired
    protected MockMvc mvc;

    @Autowired
    protected EntityManager entityManager;

    @Autowired
    protected SurveyService surveyService;

    @Test
    public void templateGroup() throws Exception {
        mvc.perform(get(String.format("/templates-groups?type=SURVEY"))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.items.size()").value(2));

        GroupAddRequestDto groupAddRequestDto = new GroupAddRequestDto(1, "测试组", TemplateType.SURVEY);
        mvc.perform(post(String.format("/templates-groups"))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(JsonHelper.toJson(groupAddRequestDto))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.title").value("测试组"));

        mvc.perform(get(String.format("/templates-groups?type=SURVEY"))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.items.size()").value(3));
    }

    @Test
    public void templateSurvey() throws Exception {
        SaveTemplateRequestDto saveTemplateRequestDto
                = new SaveTemplateRequestDto(0L, s1.getId(), "ts1", "https://dev.xmplus.cn/api/ctm/files?url=lite/cem/example1714119164757.txt");
        //模板-问卷
        //保存为模板
        MvcResult survey = mvc.perform(post(String.format("/templates-surveys/save-as-template"))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(JsonHelper.toJson(saveTemplateRequestDto))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andReturn();

        Map data = JsonHelper.toObject(survey.getResponse().getContentAsString(), Map.class);
        mvc.perform(get(String.format("/templates-surveys/%s", ((Map) data.get("data")).get("id")))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.title").value("ts1"));

        SaveTemplateRequestDto saveTemplateRequestDto2 = new SaveTemplateRequestDto(0L, s1.getId(), "ts2-2", "https://dev.xmplus.cn/api/ctm/files?url=lite/cem/example1714119164757.txt");

        MvcResult data1 = mvc.perform(post(String.format("/templates-surveys/%s/use-template", ((Map) data.get("data")).get("id")))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(JsonHelper.toJson(saveTemplateRequestDto2))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.title").value("ts1"))
                .andReturn();

        Map data1_1 = JsonHelper.toObject(data1.getResponse().getContentAsString(), Map.class);
        mvc.perform(get(String.format("/surveys/%s", ((Map) data1_1.get("data")).get("id")))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.title").value("ts1"));
    }


    @Test
    public void templateQuestion() throws Exception {

        //获取模板列表
        mvc.perform(get(String.format("/templates-questions"))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));


        //题型转模板
        QuestionsConvertTemplateDto questionsConvertTemplateDto
                = new QuestionsConvertTemplateDto(List.of(s1.getQuestions().get(0).getId()), 0L);
        MvcResult templateQuestion = mvc.perform(post(String.format("/templates-questions/save-as-template"))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(JsonHelper.toJson(questionsConvertTemplateDto))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andReturn();

        //模板转题型
        Map context = JsonHelper.toObject(templateQuestion.getResponse().getContentAsString(), Map.class);
        List<TemplateSurveyQuestion> data = (List<TemplateSurveyQuestion>) context.get("data");

//        String title = (data.get(0)).getTitle();
        long questionsId = s1.getQuestions().get(1).getId();
        TemplateConvertQuestionSequenceDto templateConvertQuestionSequenceDto = new TemplateConvertQuestionSequenceDto();
        templateConvertQuestionSequenceDto.setQuestionId(questionsId);
        List<TemplateConvertQuestionSequenceDto> list = new ArrayList<>();
        list.add(templateConvertQuestionSequenceDto);
        TemplateConvertQuestionDto templateConvertQuestionDto = new TemplateConvertQuestionDto(s1.getId(), list);
        mvc.perform(post(String.format("/templates-questions/use-template"))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(JsonHelper.toJson(templateConvertQuestionDto))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));
//                .andExpect(jsonPath("$.data[1].items").value(title));

//        Map data = JsonHelper.toObject(result.getResponse().getContentAsString(), Map.class);


//                .andExpect(jsonPath("$.data.title").value(""));


//
//        MvcResult data1 = mvc.perform(post(String.format("/templates-surveys/%s/use-template", ((Map) data.get("data")).get("id")))
//                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
//                        .contentType(MediaType.APPLICATION_JSON))
//                .andExpect(status().isOk())
//                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
//                .andExpect(jsonPath("$.code").value(200))
//                .andExpect(jsonPath("$.data.title").value("ts1"))
//                .andReturn();

//        Map data1_1 = JsonHelper.toObject(data1.getResponse().getContentAsString(), Map.class);
//        mvc.perform(get(String.format("/surveys/%s", ((Map) data1_1.get("data")).get("id")))
//                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
//                        .contentType(MediaType.APPLICATION_JSON))
//                .andExpect(status().isOk())
//                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
//                .andExpect(jsonPath("$.code").value(200))
//                .andExpect(jsonPath("$.data.title").value("ts1"));
    }
}
