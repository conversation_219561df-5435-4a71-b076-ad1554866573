package cn.hanyi.survey.controller;

import cn.hanyi.survey.BaseSurveyTest;
import cn.hanyi.survey.TestRedisConfiguration;
import cn.hanyi.survey.core.constant.error.SurveyErrorCode;
import cn.hanyi.survey.core.dto.SurveySubmitRequestDto;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.web.servlet.MockMvc;

import java.util.HashMap;
import java.util.Map;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * The class description
 *
 * <AUTHOR>
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {TestRedisConfiguration.class})
@AutoConfigureMockMvc
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
public class SurveyControllerSubmitFilterIntegrationTest extends BaseSurveyTest {
    @Autowired
    protected MockMvc mvc;

    @Test
    public void submitTwiceWhenEnableIpLimitWillReject() throws Exception {
        s1.setEnableIpLimit(true);
        surveyRepository.save(s1);

        String clientId = "random_client_01";
        Map<String, Object> answer = new HashMap<>();
        answer.put("q1", "hello");
        answer.put("q2", "c1");
        answer.put("q3", new String[]{"c1", "c2"});
        SurveySubmitRequestDto request = new SurveySubmitRequestDto();
        request.setClientId(clientId);
        request.setIsCompleted(true);
        request.setData(answer);

        mvc.perform(post(String.format("/surveys/%d/submit", s1.getId()))
                        .content(objectMapper.writeValueAsString(request))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));

        request.setClientId("random_client_02");
        mvc.perform(post(String.format("/surveys/%d/submit", s1.getId()))
                        .content(objectMapper.writeValueAsString(request))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(SurveyErrorCode.IP_ALREADY_SUBMIT.getValue()))
                .andExpect(jsonPath("$.message").value("您的IP地址已填答过该问卷"));
    }

    //    @Test
    public void submitAndAccessAgainWillReject() throws Exception {
        s1.setEnableIpLimit(true);
        surveyRepository.save(s1);

        String clientId = "random_client_01";
        Map<String, Object> answer = new HashMap<>();
        answer.put("q1", "hello");
        answer.put("q2", "c1");
        answer.put("q3", new String[]{"c1", "c2"});
        SurveySubmitRequestDto request = new SurveySubmitRequestDto();
        request.setClientId(clientId);
        request.setIsCompleted(true);
        request.setData(answer);

        mvc.perform(post(String.format("/surveys/%d/submit", s1.getId()))
                        .content(objectMapper.writeValueAsString(request))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));

        mvc.perform(get(String.format("/surveys/%d/start", s1.getId()))
                        .content(objectMapper.writeValueAsString(request))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(SurveyErrorCode.OVER_LIMIT.getValue()))
                .andExpect(jsonPath("$.message").value("您的IP地址已填答过该问卷"));
    }

    @Test
    public void submitTwiceWithDiffIpShouldAccept() throws Exception {
        s1.setEnableIpLimit(true);
        surveyRepository.save(s1);

        String clientId = "random_client_01";
        Map<String, Object> answer = new HashMap<>();
        answer.put("q1", "hello");
        answer.put("q2", "c1");
        answer.put("q3", new String[]{"c1", "c2"});
        SurveySubmitRequestDto request = new SurveySubmitRequestDto();
        request.setClientId(clientId);
        request.setData(answer);

        mvc.perform(post(String.format("/surveys/%d/submit", s1.getId()))
                        .content(objectMapper.writeValueAsString(request))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));

        request.setClientId("random_client_02");
        mvc.perform(post(String.format("/surveys/%d/submit", s1.getId()))
                        .with(remoteAddr(anotherIp))
                        .content(objectMapper.writeValueAsString(request))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @Disabled("TBD: quota will be tested later")
    public void submitWithQuotaLimit() throws Exception {
        s1.setEnableQuota(true);
        surveyRepository.save(s1);
        s1_q1_quota.setExpression("q1.value == \"hit_me\"");
        s1_q1_quota.setMax(1);
        quotaRepository.save(s1_q1_quota);

        Map<String, Object> answer = new HashMap<>();
        answer.put("q1", "hit_me");
        answer.put("q2", "c1");
        answer.put("q3", new String[]{"c1", "c2"});

        SurveySubmitRequestDto request = new SurveySubmitRequestDto();
        request.setData(answer);

        mvc.perform(post(String.format("/surveys/%d/submit", s1.getId()))
                        .content(objectMapper.writeValueAsString(request))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));

        mvc.perform(post(String.format("/surveys/%d/submit", s1.getId()))
                        .content(objectMapper.writeValueAsString(request))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(SurveyErrorCode.OVER_LIMIT.getValue()))
                .andExpect(jsonPath("$.message").value("您的IP地址已填答过该问卷"));
    }
}
