package cn.hanyi.survey;

import cn.hanyi.survey.core.constant.ResponseStatus;
import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyResponse;
import cn.hanyi.survey.core.repository.SurveyRepository;
import cn.hanyi.survey.core.repository.SurveyResponseRepository;
import org.befun.core.dto.UserDto;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.DirtiesContext;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {TestRedisConfiguration.class})
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
public class TestRepository {

    @Autowired
    protected SurveyResponseRepository responseRepository;
    @Autowired
    protected SurveyRepository surveyRepository;

    @Test
    public void findAllResponse() throws Exception {
        UserDto mock_user_1 = mock_user_1 = UserDto
                .builder()
                .orgId(1L)
                .id(1L)
                .username("u1")
                .build();
        Survey s2 = SurveyTestUtility.buildSurvey(2,
                new QuestionType[]{QuestionType.TEXT, QuestionType.SINGLE_CHOICE, QuestionType.MULTIPLE_CHOICES},
                3, mock_user_1
        );
        surveyRepository.save(s2);

        SurveyResponse sf_r1 = SurveyTestUtility.buildResponse(s2, "guangdong", true, ResponseStatus.FINAL_SUBMIT);
        responseRepository.save(sf_r1);

        responseRepository.findAll();
    }
}
