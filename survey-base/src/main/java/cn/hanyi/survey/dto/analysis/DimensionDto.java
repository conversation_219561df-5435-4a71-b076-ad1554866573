package cn.hanyi.survey.dto.analysis;

import cn.hanyi.survey.core.constant.analysis.GranularityType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.befun.core.dto.BaseDTO;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DimensionDto extends BaseDTO {
    @Schema(description = "纬度类型", example = "COUNT")
    private String name;

    @Schema(description = "纬度类型", example = "COUNT")
    private String field;

    private String title;

    @Schema(description = "粒度类型 一般会groupBy, 目前只针对时间维度有效", example = "DAY")
    private GranularityType granularity;
}
