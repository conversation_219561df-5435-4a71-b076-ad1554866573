package cn.hanyi.survey.dto;

import cn.hanyi.survey.core.entity.SurveyResponse;
import cn.hanyi.survey.core.entity.SurveyResponseCell;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
public class SurveyResponseSubmitOnlyDto {
    SurveyResponse response;
    Boolean isCompleted;
    Collection<SurveyResponseCell> cells;
    Map<String, Object> data;

    public SurveyResponseSubmitOnlyDto(SurveyResponse response, Boolean isCompleted, Collection<SurveyResponseCell> cells) {
        this.response = response;
        this.isCompleted = isCompleted;
        this.cells = cells;
    }

}
