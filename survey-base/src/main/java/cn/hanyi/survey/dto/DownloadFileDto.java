package cn.hanyi.survey.dto;

import cn.hanyi.survey.core.annotation.DownloadField;
import cn.hanyi.survey.core.constant.channel.SurveyCollectorMethod;
import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.entity.SurveyChannel;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.core.entity.SurveyQuestionItem;
import cn.hanyi.survey.core.entity.SurveyResponse;
import cn.hanyi.survey.core.utilis.CollectionUtils;
import cn.hanyi.survey.core.utilis.RegularExpressionUtils;
import lombok.Getter;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.befun.core.utils.JsonHelper;

import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static cn.hanyi.survey.core.utilis.DownloadUtils.dynamicDownloadFields;

/**
 * <AUTHOR>
 */

@Getter
public class DownloadFileDto {

    public static List<QuestionType> EX_INCLUDE_TYPE = List.of(
            QuestionType.MARK,
            QuestionType.SEPARATOR,
            QuestionType.EMPTY,
            QuestionType.MEDIA,
            QuestionType.GROUP
    );
    protected static List<String> DYNAMIC_FIELD = List.of(
            "departmentId",
            "externalUserId",
            "customerId",
            "customerGender",
            "customerName",
            "departmentName",
            "departmentCode",
            "externalCompanyId",
            "defaultPa",
            "defaultPb",
            "defaultPc",
            "parameters",
            "openid",
            "totalScore",
            "name",
            "phone",
            "email"
    );

    @DownloadField(display = "id", displayCode = "id")
    private String id;

    @DownloadField(display = "答卷编号", displayCode = "QID")
    private Long sequence;

    @DownloadField(display = "回收类型", displayCode = "send_type")
    private String collectorMethod;

    @DownloadField(display = "发送名称", displayCode = "send_name")
    private Long channelId;

    @DownloadField(display = "姓名", displayCode = "name")
    private String name;

    @DownloadField(display = "手机号码", displayCode = "phone")
    private String phone;

    @DownloadField(display = "邮箱", displayCode = "mailbox")
    private String email;

    @DownloadField(display = "提交时间", displayCode = "end_time")
    private String finishTime;

    @DownloadField(display = "开始时间", displayCode = "start_time")
    private String startTime;

    @DownloadField(display = "答题状态", displayCode = "result_code")
    private String status;

    @DownloadField(display = "ip", displayCode = "ip")
    private String ip;

    @DownloadField(display = "操作系统", displayCode = "os")
    private String os;

    @DownloadField(display = "浏览器", displayCode = "browser")
    private String browser;

    @DownloadField(display = "答题时长(秒)", displayCode = "duration_seconds")
    private Integer durationSeconds;

    @DownloadField(display = "答题总时长(秒)", displayCode = "duration_total")
    private Long durationTotal;

    @DownloadField(display = "国家(ip)", displayCode = "ip_country")
    private String country;

    @DownloadField(display = "省份(ip)", displayCode = "ip_province")
    private String province;

    @DownloadField(display = "城市(ip)", displayCode = "ip_city")
    private String city;

    @DownloadField(display = "总得分", displayCode = "score")
    private Integer totalScore;

    @DownloadField(display = "客户ID", displayCode = "customerId")
    private String customerId;

    @DownloadField(display = "外部客户ID", displayCode = "externalUserId")
    private String externalUserId;

    @DownloadField(display = "层级ID", displayCode = "departmentId")
    private String departmentId;

    @DownloadField(display = "部门名称", displayCode = "departmentName")
    private String departmentName;

    @DownloadField(display = "客户名称", displayCode = "customerName")
    private String customerName;

    @DownloadField(display = "客户性别", displayCode = "customerGender")
    private String customerGender;

    @DownloadField(display = "外部组织编号", displayCode = "外部组织编号")
    private String departmentCode;

    @DownloadField(display = "外部企业ID", displayCode = "externalCompanyId")
    private String externalCompanyId;

    @DownloadField(display = "默认参数a", displayCode = "默认参数a")
    private String defaultPa;

    @DownloadField(display = "默认参数b", displayCode = "默认参数b")
    private String defaultPb;

    @DownloadField(display = "默认参数c", displayCode = "默认参数c")
    private String defaultPc;

    @DownloadField(display = "外部参数", displayCode = "external_parameters")
    private String parameters;

    @DownloadField(display = "微信openId", displayCode = "openID")
    private String openid;


    public List buildHeaders(List<SurveyQuestion> surveyQuestion, List<SurveyResponse> surveyResponses, Boolean transCode) {

        /**
         * 外部参数 departmentId externalUserId customerId
         * 暂不考虑答题有一部分会带一部分不带
         */
        Field[] fields = this.getClass().getDeclaredFields();
        List<String> attributes = Arrays.stream(fields)
                .filter(x -> x.getAnnotationsByType(DownloadField.class).length > 0)
                .map(field -> {
                    if (DYNAMIC_FIELD.contains(field.getName())) {
                        // 当初商量是一份问卷的答题要么同时会有动态带ID的字段,要么不带
                        // 现在动态添加
                        var surveyResponseWithField = surveyResponses.stream().anyMatch(surveyResponse -> {
                            try {
                                Object value = PropertyUtils.getProperty(surveyResponse, field.getName());
                                if (value != null && CollectionUtils.size(value) > 0) {
                                    return true;
                                }
                            } catch (Exception ex) {
                                ex.printStackTrace();
                            }
                            return false;
                        });

                        if (surveyResponseWithField) {
                            // 当前字段有数据需要下载
                            dynamicDownloadFields().put(field.getName(), true);
                        } else {
                            // 不需要下载的字段返回null 给Objects::nonNull过滤
                            return null;
                        }
                    }

                    return transCode ? field.getAnnotation(DownloadField.class).displayCode() : field.getAnnotation(DownloadField.class).display();
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        List<String> questions = surveyQuestion.stream()
                .filter(x -> !EX_INCLUDE_TYPE.contains(x.getType()))
                .map(
                        question -> {

                            List<String> questionsItems;
                            AtomicInteger atomicInteger = new AtomicInteger(1);

                            if (question.getItems().isEmpty()) {
                                // 不带选项的题型
                                List<String> headerText;
                                switch (question.getType()) {
                                    case AREA:
                                        // 地区题型不带item 还需要拆分省市区地址到单元格
                                        headerText = transCode
                                                ? Arrays.stream(question.getAreaType().getFormat().split("-"))
                                                .map(x -> String.format("%s_%s", question.getCode(), atomicInteger.getAndIncrement()))
                                                .collect(Collectors.toList())
                                                : Arrays.stream(question.getAreaType().getFormat().split("-"))
                                                .map(x -> String.format("%s_%s_%s", question.getCode(), RegularExpressionUtils.replaceHtml(question.getTitle()), x))
                                                .collect(Collectors.toList());
                                        break;
                                    case DROP_DOWN:
                                        // 逐级下拉不带item 还需要拆分到单元格
                                        QuestionDropDownDto config = JsonHelper.toObject(question.getConfigure(), QuestionDropDownDto.class);
                                        if (config == null) {
                                            // 新建题型时 前端不会存储configure
                                            // 产品需求默认使用一级选项, 二级选项
                                            List<String> config_default = List.of("一级选项", "二级选项");
                                            headerText = transCode
                                                    ? config_default
                                                    .stream()
                                                    .map(x -> String.format("%s_%s", question.getCode(), atomicInteger.getAndIncrement()))
                                                    .collect(Collectors.toList())
                                                    : config_default
                                                    .stream()
                                                    .map(x -> String.format("%s_%s_%s", question.getCode(), RegularExpressionUtils.replaceHtml(question.getTitle()), x))
                                                    .collect(Collectors.toList());
                                            break;
                                        }
                                        headerText = transCode
                                                ? config.getProps()
                                                .stream()
                                                .map(x -> String.format("%s_%s", question.getCode(), atomicInteger.getAndIncrement()))
                                                .collect(Collectors.toList())
                                                : config.getProps()
                                                .stream()
                                                .map(x -> String.format("%s_%s_%s", question.getCode(), RegularExpressionUtils.replaceHtml(question.getTitle()), x))
                                                .collect(Collectors.toList());
                                        break;
                                    case LOCATION:
                                        String title = RegularExpressionUtils.replaceHtml(question.getTitle());
                                        headerText = transCode
                                                ? List.of(String.format("%s_%s", question.getCode(), "datasource")
                                                , String.format("%s_%s", question.getCode(), "longitude")
                                                , String.format("%s_%s", question.getCode(), "latitude")
                                                , String.format("%s_%s", question.getCode(), "location"))
                                                : List.of(String.format("%s_%s", question.getCode(), "数据来源")
                                                , String.format("%s_%s_%s", question.getCode(), title, "经度")
                                                , String.format("%s_%s_%s", question.getCode(), title, "纬度")
                                                , String.format("%s_%s_%s", question.getCode(), title, "位置名称"));
                                        break;
                                    default:
                                        headerText = transCode
                                                ? List.of(question.getCode())
                                                : List.of(String.format(
                                                "%s_%s",
                                                question.getCode(),
                                                RegularExpressionUtils.replaceHtml(question.getTitle()))
                                        );
                                }
                                questionsItems = new ArrayList<>(headerText);
                            } else {
                                // 带选项题型
                                questionsItems = new ArrayList<>();
                                switch (question.getType()) {
                                    //联合实验题
                                    case EXPERIMENT:
                                        String text = question.getConfigure();
                                        //所有属性和水平
//                                        List<Map<String, Object>> attrs = (List<Map<String, Object>>) JSONArray.parse(text);
                                        List<Map> attrs = JsonHelper.toList(text, Map.class);
                                        //组合个数
                                        for (int i = 0; i < question.getItems().size(); i++) {
                                            //属性个数
                                            for (int j = 0; j < attrs.size(); j++) {
                                                questionsItems.add(transCode
                                                        //M1_1_1
                                                        ? String.format("%s_%s_%s", question.getCode(), i + 1, j + 1)
                                                        //M1_实验题_组合1_属性1
                                                        : String.format("%s_%s_%s_%s", question.getCode(),
                                                        RegularExpressionUtils.replaceHtml(question.getTitle()),
                                                        RegularExpressionUtils.replaceHtml(question.getItems().get(i).getText()),
                                                        RegularExpressionUtils.replaceHtml(attrs.get(j).get("attribute").toString()))
                                                );
                                            }
                                        }
                                        break;
                                    //单选只保留一个选项内容
                                    case SINGLE_CHOICE:
                                    case COMBOBOX:
                                        questionsItems.add(transCode
                                                ? String.format("%s", question.getCode())
                                                : String.format("%s_%s", question.getCode(),
                                                RegularExpressionUtils.replaceHtml(question.getTitle()))
                                        );
                                        //如果选项设置文本输入
                                        for (SurveyQuestionItem item : question.getItems()) {
                                            if (item.getEnableTextInput()) {
                                                questionsItems.add(transCode
                                                        ? String.format("%s_text", question.getCode(), atomicInteger.getAndIncrement())
                                                        : String.format("%s_%s_%s", question.getCode(),
                                                        RegularExpressionUtils.replaceHtml(question.getTitle()),
                                                        "文本输入")
                                                );
                                                break;
                                            }
                                        }
                                        break;
                                    case EVALUATION:
                                    case SCORE_EVALUATION:
                                        int order = 1;
                                        if (question.getType().equals(QuestionType.SCORE_EVALUATION)) {
                                            questionsItems.add(transCode
                                                    ? String.format("%s_%s", question.getCode(), order++)
                                                    : String.format("%s_%s_打分", question.getCode(), RegularExpressionUtils.replaceHtml(question.getTitle())));
                                        }
                                        questionsItems.add(transCode
                                                ? String.format("%s_%s", question.getCode(), order++)
                                                : String.format("%s_%s_评价", question.getCode(), RegularExpressionUtils.replaceHtml(question.getTitle())));
                                        questionsItems.add(transCode
                                                ? String.format("%s_%s", question.getCode(), order++)
                                                : String.format("%s_%s_标签", question.getCode(), RegularExpressionUtils.replaceHtml(question.getTitle())));
                                        questionsItems.add(transCode
                                                ? String.format("%s_%s", question.getCode(), order++)
                                                : String.format("%s_%s_评价文本", question.getCode(), RegularExpressionUtils.replaceHtml(question.getTitle())));
                                        break;
                                    case NPS:
                                        questionsItems.add(transCode
                                                ? String.format("%s_1", question.getCode())
                                                : String.format("%s_%s", question.getCode(), RegularExpressionUtils.replaceHtml(question.getTitle())));
                                        questionsItems.add(transCode
                                                ? String.format("%s_2", question.getCode())
                                                : String.format("%s_%s_标签", question.getCode(), RegularExpressionUtils.replaceHtml(question.getTitle())));
                                        questionsItems.add(transCode
                                                ? String.format("%s_3", question.getCode())
                                                : String.format("%s_%s_评价文本", question.getCode(), RegularExpressionUtils.replaceHtml(question.getTitle())));
                                        break;
                                    default:
                                        for (SurveyQuestionItem item : question.getItems()) {
                                            Integer index = atomicInteger.getAndIncrement();
                                            questionsItems.add(transCode
                                                    ? String.format("%s_%s", question.getCode(), index)
                                                    : String.format("%s_%s_%s", question.getCode(),
                                                    RegularExpressionUtils.replaceHtml(question.getTitle()),
                                                    RegularExpressionUtils.replaceHtml(item.getText()))
                                            );
                                            if (item.getEnableTextInput()) {
                                                questionsItems.add(transCode
                                                        ? String.format("%s_%s_text", question.getCode(), index)
                                                        : String.format("%s_%s_%s", question.getCode(),
                                                        RegularExpressionUtils.replaceHtml(question.getTitle()),
                                                        "文本输入")
                                                );
                                            }
                                        }
                                }
                            }
                            //TODO 兼容单选多选的其他选项
                            if (question.getHasOther() && Arrays.asList(QuestionType.SINGLE_CHOICE, QuestionType.MULTIPLE_CHOICES).contains(question.getType())) {
                                questionsItems.add(transCode
                                        ? String.format("%s_%s", question.getCode(), questionsItems.size() + 1)
                                        : String.format("%s_%s_%s", question.getCode(),
                                        RegularExpressionUtils.replaceHtml(question.getTitle()),
                                        question.getOtherLabel()
                                ));
                            }
                            return questionsItems;
                        }
                ).
                collect(Collectors.toList()).
                stream().
                flatMap(Collection::stream).
                collect(Collectors.toList());

        return ListUtils.union(attributes, questions);
    }

    public List<Object> buildResponse(SurveyResponse surveyResponse, List<SurveyChannel> channels) {

        String finishTime = surveyResponse.getFinishTime() == null ? "" : new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(surveyResponse.getFinishTime());

        this.id = surveyResponse.getId().toString();
        this.sequence = surveyResponse.getSequence();
        this.collectorMethod = surveyResponse.getCollectorMethod().getText();
        this.channelId = surveyResponse.getChannelId();
        this.name = surveyResponse.getName();
        this.phone = surveyResponse.getPhone();
        this.email = surveyResponse.getEmail();
        this.finishTime = finishTime;
        this.startTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(surveyResponse.getCreateTime());
        this.status = surveyResponse.getStatus().name();
        this.ip = surveyResponse.getIp();
        this.os = surveyResponse.getOs();
        this.browser = surveyResponse.getBrowser();
        this.durationSeconds = surveyResponse.getDurationSeconds();
        this.durationTotal = (surveyResponse.getFinishTime() != null && surveyResponse.getCreateTime() != null) ? (surveyResponse.getFinishTime().getTime() - surveyResponse.getCreateTime().getTime()) / 1000 : null;
        this.country = surveyResponse.getCountry();
        this.province = surveyResponse.getProvince();
        this.city = surveyResponse.getCity();
        this.totalScore = surveyResponse.getTotalScore() != null ? surveyResponse.getTotalScore() : null;
        this.customerId = Objects.toString(surveyResponse.getCustomerId(), "");
        this.departmentId = Objects.toString(surveyResponse.getDepartmentId(), "");
        this.externalUserId = Objects.toString(surveyResponse.getExternalUserId(), "");
        this.departmentName = Objects.toString(surveyResponse.getDepartmentName(), "");
        this.customerGender = Objects.toString(surveyResponse.getCustomerGender(), "");
        this.customerName = Objects.toString(surveyResponse.getCustomerName(), "");
        this.parameters = ObjectUtils.isEmpty(surveyResponse.getParameters()) ? "" : JsonHelper.toJson(surveyResponse.getParameters());
        this.departmentCode = Objects.toString(surveyResponse.getDepartmentCode(), "");
        this.externalCompanyId = Objects.toString(surveyResponse.getExternalCompanyId(), "");
        this.defaultPa = Objects.toString(surveyResponse.getDefaultPa(), "");
        this.defaultPb = Objects.toString(surveyResponse.getDefaultPb(), "");
        this.defaultPc = Objects.toString(surveyResponse.getDefaultPc(), "");
        if(surveyResponse.getCollectorMethod() == SurveyCollectorMethod.SURVEY_PLUS) this.openid = "";
        else this.openid = Objects.toString(surveyResponse.getOpenid(), "");

        Field[] fields = this.getClass().getDeclaredFields();

        var data = Arrays.stream(fields).filter(x -> x.getAnnotationsByType(DownloadField.class).length > 0).map(field -> {
            try {
                Object value = PropertyUtils.getProperty(this, field.getName());
                if (DYNAMIC_FIELD.contains(field.getName())) {

                    if (dynamicDownloadFields().getOrDefault(field.getName(), false)) {
                        // 动态ID字段如果其中有人带了参数，其他人没有就为空
                        return Objects.requireNonNullElse(value, "");
                    }

                    if (value == null || String.valueOf(value).isEmpty()) {
                        return null;
                    }

                }
                if ("channelId".equals(field.getName())) {
                    if (value != null && ObjectUtils.isNotEmpty(channels)) {
                        return channels.get(0).getName();
                    } else {
                        return SurveyCollectorMethod.LINK.getText();
                    }
                }
                return value;
            } catch (Exception e) {
                e.printStackTrace();
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        return data;
    }

}