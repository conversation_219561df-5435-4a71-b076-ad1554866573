package cn.hanyi.survey.dto.survey;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2023/6/26 19:07
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SurveyPlusConfigureDto {


    /*****************销售咨询********************/
    @Schema(name = "联系人")
    private String contacts;
    @Schema(name = "手机号")
    private String mobile;
    @Schema(name = "二维码")
    private String qrcode;

    /*****************样本特征********************/
    @Schema(name = "问卷题目数量")
    private int countQuestion = 0;
    @Schema(name = "样本数量")
    private int quantity = 0;
    @Schema(name = "样本单价")
    private BigDecimal unitPrice;
    @Schema(name = "样本总价")
    private String totalPrice;
    @Schema(name = "样本特征-性别")
    private String genderMatch;
    @Schema(name = "样本特征-年龄")
    private String ageMatch;
    @Schema(name = "样本特征-学历")
    private String educationMatch;
    @Schema(name = "样本特征-地区")
    private String locationMatch;
    @Schema(name = "样本特征-其他")
    private String other;
    @Schema(name = "样本待报价内容")
    private String detail;

    @Schema(name = "订单创建时间")
    private Date createTime;
}
