package cn.hanyi.survey.dto.analysis;

import cn.hanyi.survey.core.constant.analysis.GptConversationStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/2/14 14:29:02
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ConversationHistoryMessageResponseDto {

    @Schema(description = "类型-数据")
    private String type;

    @Schema(description = "类型-数据")
    private String content;

    @Schema(description = "类型-数据")
    private Date date;

    @Schema(description = "类型-数据")
    private GptConversationStatus status;

}