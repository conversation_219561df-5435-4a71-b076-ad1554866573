package cn.hanyi.survey.dto.index;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Setter
@Getter
public class SimpleSurveyQuestionDto {
    @JsonView(ResourceViews.Basic.class)
    Long id;
    @JsonView(ResourceViews.Basic.class)
    Long sid;
    @JsonView(ResourceViews.Basic.class)
    String title;
    @JsonView(ResourceViews.Basic.class)
    String type;
    @JsonView(ResourceViews.Basic.class)
    BigDecimal sequence;
    @JsonView(ResourceViews.Basic.class)
    String groupCode;
    @JsonView(ResourceViews.Basic.class)
    String name;
    @JsonView(ResourceViews.Basic.class)
    Double avgScore = 0.00;
    @JsonView(ResourceViews.Basic.class)
    List<Map<String, Object>> questionItems;
    @JsonView(ResourceViews.Basic.class)
    String code;
}
