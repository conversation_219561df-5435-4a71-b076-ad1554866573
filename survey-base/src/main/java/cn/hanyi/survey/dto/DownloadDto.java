package cn.hanyi.survey.dto;

import cn.hanyi.survey.core.constant.DownloadFromType;
import cn.hanyi.survey.core.constant.DownloadType;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Setter
@Getter
public class DownloadDto {
    private List<Long> responseIds = new ArrayList<>();
    private DownloadType downloadType = DownloadType.CSV;
    // true 表示异步下载数据，false 表示同步下载
    private Boolean async = false;
    private Boolean processAll = false;
    private SurveyResponseQueryDto queryDto;

    // 新的下载方法参数
    private Boolean useNewDownload = true;
    private Integer batchSize;

    //是否重新下载
    private Boolean reDownload = false;
    private Long taskProgressId;

    // 详情页需要无效数据
    private DownloadFromType from = DownloadFromType.Banner;

    public void appendResponseIds(List<Long> ids) {
        if (this.getProcessAll()) this.responseIds = ids;
    }
}
