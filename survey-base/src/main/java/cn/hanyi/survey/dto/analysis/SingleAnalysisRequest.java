package cn.hanyi.survey.dto.analysis;

import cn.hanyi.survey.core.constant.channel.SurveyCollectorMethod;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.befun.core.dto.query.ResourceCustomQueryDto;
import org.befun.core.validation.ValidSearchText;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/3 17:27:38
 */
@Data
public class SingleAnalysisRequest extends ResourceCustomQueryDto {

    /*文本题筛选*/

    @Schema(description = "文本题答题数据")
    @ValidSearchText
    private String textResponseCell;

    @Schema(description = "地区题省份下钻到市")
    @ValidSearchText
    private String AreaProvince;

    /*下面用于整体筛选*/

    @Schema(description = "提交日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date submitDate;

    @Schema(description = "结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;

    @Schema(description = "发送类型")
    private List<SurveyCollectorMethod> channelTypes = new ArrayList<>();

    @Schema(description = "渠道")
    private List<Long> channelIds = new ArrayList<>();

    @Schema(description = "省")
    private String province;

    @Schema(description = "市")
    private String city;
}

















