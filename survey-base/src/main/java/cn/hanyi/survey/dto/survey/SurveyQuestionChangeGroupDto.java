package cn.hanyi.survey.dto.survey;

import cn.hanyi.survey.core.entity.SurveyQuestion;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Getter
@Setter
public class SurveyQuestionChangeGroupDto {

    @NotNull
    @Schema(description = "目标分组", required = true)
    private Long groupId;

    @NotEmpty
    @Schema(description = "要移动的问题id", required = true)
    private List<SurveyQuestion> questions;
}
