package cn.hanyi.survey.dto.analysis;

import cn.hanyi.survey.core.constant.analysis.MeasureFormat;
import cn.hanyi.survey.core.constant.analysis.MeasureType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.befun.core.dto.BaseDTO;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MeasureDto extends BaseDTO {

    @Schema(description = "度量类型", example = "COUNT")
    private MeasureType type;

    @Schema(description = "名称 (结果回显使用)", example = "name")
    private String name;

    @Schema(description = "字段 参考对应的Model的字段保持一致", example = "name")
    private String field;

    private String title;

    @Schema(description = "格式控制 (暂未启用)", example = "PERCENT")
    private Optional<MeasureFormat> format;

    @Schema(description = "滚动窗口 (暂未启用)")
    private Optional<RollingWindowDto> rollingWindow;

    private List<FilterDto> filters = new ArrayList<>();
}
