package cn.hanyi.survey.dto;

import cn.hanyi.survey.core.dto.SurveySubmitRequestDto;
import cn.hanyi.survey.core.entity.Survey;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;

import java.util.HashMap;
import java.util.Map;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class SurveyResponseContextDto extends BaseDTO {
    private static final long serialVersionUID = 6529685098267757690L;
    private String ipAddress;
    private String deviceId;
    private String clientId;
    private Survey survey;
    private SurveySubmitRequestDto requestDto;
    private Map<String, SurveyResponseCellContextDto> answers = new HashMap<>();

    public void SurveyResponseContextDto(String ip, Map<String, SurveyResponseCellContextDto> answers) {
        this.ipAddress = ip;
        this.answers = answers;
    }
}
