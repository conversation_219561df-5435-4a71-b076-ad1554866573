package cn.hanyi.survey.dto.template;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.query.ResourceCustomQueryDto;

@Getter
@Setter
public class TemplateSurveyQueryDto extends ResourceCustomQueryDto {

    @JsonProperty("_q")
    @Schema(description = "关键字查询", example = "_q=abc")
    private String q = null;

    @Schema(description = "分组id")
    private Long groupId;
}
