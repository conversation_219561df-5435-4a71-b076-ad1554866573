package cn.hanyi.survey.dto;

import cn.hanyi.survey.core.constant.ResponseStatus;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;
import org.befun.core.rest.view.ResourceViews;

import java.util.Date;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Setter
@Getter
public class SurveyResponseListDto extends BaseDTO {
    @JsonView(ResourceViews.Basic.class)
    private long id;

    @JsonView(ResourceViews.Basic.class)
    private long sequence;

    @JsonView(ResourceViews.Basic.class)
    private Date createTime;

    @JsonView(ResourceViews.Basic.class)
    private Date finishTime;

    @JsonView(ResourceViews.Basic.class)
    private int durationSeconds;

    @JsonView(ResourceViews.Basic.class)
    private ResponseStatus status;

    @JsonView(ResourceViews.Basic.class)
    private String channelName;

    @JsonView(ResourceViews.Basic.class)
    private Long channelId;

    @JsonView(ResourceViews.Basic.class)
    private String country;

    @JsonView(ResourceViews.Basic.class)
    private String province;

    @JsonView(ResourceViews.Basic.class)
    private String city;
}