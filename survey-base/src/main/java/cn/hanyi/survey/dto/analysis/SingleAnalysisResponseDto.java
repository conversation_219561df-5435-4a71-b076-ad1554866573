package cn.hanyi.survey.dto.analysis;

import cn.hanyi.survey.core.dto.lottery.MetaDto;
import lombok.*;
import org.befun.core.dto.BaseDTO;
import org.springframework.util.LinkedCaseInsensitiveMap;

import javax.persistence.Convert;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SingleAnalysisResponseDto<T> extends BaseDTO {
    private ArrayList<String> columns = new ArrayList<>();
    private ArrayList<T> rows = new ArrayList<>();
    private Extra extraData = new Extra();
    private MetaDto meta;


    public ArrayList<String> getColumns() {
        return rows.isEmpty() ?
                new ArrayList<>() :
                rows.stream().findAny().get() instanceof LinkedCaseInsensitiveMap ? (ArrayList<String>) ((Map) rows.stream().findAny().get()).keySet().stream().collect(Collectors.toList()) :
                        (ArrayList<String>) Arrays.stream(rows.stream().findAny().get().getClass().getDeclaredFields()).map(Field::getName).collect(Collectors.toList());
    }

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SingleCount {
        private String text;
        private Object value;
    }

    /**
     * 选项文本输入
     */
    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ItemTextInput {
        private Long qid;
        private String text;
        private Boolean enableTextInput;
    }

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Choice {
        private String text;
        private Integer value;
        private String name;
        private Double percent;
        private Boolean textInputView = false;

        public Double getPercent() {
            return percent == null ? 0 : Double.parseDouble(String.format("%.2f", percent * 100));
        }
    }

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Score {
        private String text;
        private Integer value;
        private Double percent;
        private String label;
    }

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Text {
        private Long responseId;
        private Integer sequence;
        private String cellValue;
    }

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Area {
        private String text;
        private Integer value;
        private Double percent;
        private List<Area> children;

    }


    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class StringValueTags {
        private Integer i_val;
        private String s_val;
        @Convert(converter = StringCommaListConverter.class)
        private List<String> tags = new ArrayList<>();
        private Integer cellScore;

        public List<String> getTags() {
            return tags == null ? new ArrayList<>() : tags;
        }
    }

    @Getter
    @Setter
    @NoArgsConstructor
    public static class ValuePercent {
        private String text;
        private Integer value;
        private String name;
        private Double percent;
        private String label;
        private Boolean textInputView;
        private List<ValuePercent> children = new ArrayList<>();

        public ValuePercent(String text, Integer value, Double percent) {
            this.text = text;
            this.value = value;
            this.percent = percent;
        }
        public ValuePercent(String text, Integer value, Double percent, String label) {
            this.text = text;
            this.value = value;
            this.percent = percent;
            this.label = label;
        }

        public ValuePercent(String text, Integer value, String name, Double percent, Boolean textInputView, List<ValuePercent> children) {
            this.text = text;
            this.value = value;
            this.name = name;
            this.percent = percent;
            this.textInputView = textInputView;
            this.children = children;
        }
    }

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Extra {
        private Integer count;
        private Double recommend;
        private Double average;
        private List<Recommender> recommender;

        public Extra(Integer count) {
            this.count = count;
        }

        public Extra(Integer count, Double average) {
            this.count = count;
            this.average = average;
        }
    }

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Recommender {
        private String text;
        private Double percent;
    }


}
