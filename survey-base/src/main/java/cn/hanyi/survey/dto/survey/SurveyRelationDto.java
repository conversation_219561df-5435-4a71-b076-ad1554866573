package cn.hanyi.survey.dto.survey;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
public class SurveyRelationDto {

    @Schema(description = "问卷id")
    private Long surveyId;

    @Schema(description = "1 问卷已删除 2 无权限 3 有权限")
    private Integer relation;

    @Schema(description = "问卷标题")
    private String title;

}
