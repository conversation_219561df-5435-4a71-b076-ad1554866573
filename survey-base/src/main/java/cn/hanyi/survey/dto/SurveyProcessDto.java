package cn.hanyi.survey.dto;

import cn.hanyi.survey.core.constant.survey.SurveyStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class SurveyProcessDto {
    @Schema(description = "配额是否完成同步")
    public Boolean completed = true;
    @Schema(description = "配额同步进度")
    public Integer process = 100;
    @Schema(description = "总数")
    public Integer total = 0;
    @Schema(description = "已完成")
    public Integer success = 0;
    @Schema(description = "问卷状态")
    public SurveyStatus status;
}
