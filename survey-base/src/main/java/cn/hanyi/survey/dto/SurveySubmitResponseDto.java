//package cn.hanyi.survey.dto;
//
//import cn.hanyi.survey.client.service.submit.SubmitContext;
//import lombok.AllArgsConstructor;
//import lombok.Getter;
//import lombok.NoArgsConstructor;
//import lombok.Setter;
//import org.befun.core.dto.BaseDTO;
//
///**
// * The class description
// *
// * <AUTHOR>
// */
//@Setter
//@Getter
//@AllArgsConstructor
//@NoArgsConstructor
//public class SurveySubmitResponseDto extends BaseDTO {
//    private static final long serialVersionUID = 6529685098267757690L;
//
//    private String clientId;
//    private Long responseId;
//
//    public SurveySubmitResponseDto(SubmitContext context) {
//        this.clientId = context.getData().getClientId();
//        this.responseId = context.getResponse().getId();
//    }
//}
