package cn.hanyi.survey.dto.index;

import cn.hanyi.survey.core.constant.survey.SurveyStatus;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

import java.util.List;


@Setter
@Getter
public class SimpleSurveyDto {
    @JsonView(ResourceViews.Basic.class)
    Long id;
    @JsonView(ResourceViews.Basic.class)
    String title;
    @JsonView(ResourceViews.Basic.class)
    SurveyStatus status;
    @JsonView(ResourceViews.Basic.class)
    List<SimpleSurveyQuestionDto> filterQuestions;
}
