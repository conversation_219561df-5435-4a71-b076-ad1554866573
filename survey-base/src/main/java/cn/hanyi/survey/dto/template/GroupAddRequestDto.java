package cn.hanyi.survey.dto.template;

import cn.hanyi.survey.core.constant.Template.TemplateType;
import cn.hanyi.survey.core.entity.template.TemplateGroup;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.CustomParamDto;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class GroupAddRequestDto extends CustomParamDto<TemplateGroup> {
    private Integer sequence;
    @NotEmpty
    private String title;
    @NotNull
    private TemplateType type;
}
