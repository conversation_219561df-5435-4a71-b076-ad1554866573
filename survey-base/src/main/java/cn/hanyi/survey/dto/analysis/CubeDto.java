package cn.hanyi.survey.dto.analysis;

import cn.hanyi.survey.core.utilis.SortDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;
import org.springframework.data.domain.Sort;

import java.util.ArrayList;
import java.util.List;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class CubeDto extends BaseDTO {
    private List<MeasureDto> measures = new ArrayList<>();
    private List<DimensionDto> dimensions = new ArrayList<>();
    private List<FilterDto> filters = new ArrayList<>();

    private int limit;
    private Sort sort;

    @JsonDeserialize(using = SortDeserializer.class)
    public void setSort(Sort sort) {
        this.sort = sort;
    }
}
