package cn.hanyi.survey.dto.survey;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.exception.BadRequestException;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Getter
@Setter
public class SurveyPlusOrderPriceDto {
    private int maxAmount;
    private int quantityMin;
    private int quantityMax;
    private int questionMin;
    private int questionMax;
    private List<QuestionMatch> countQuestion;
    private List<TypeMatch> genderMatch;
    private List<TypeMatch> ageMatch;
    private List<TypeMatch> educationMatch;
    private List<TypeMatch> locationMatch;

    public int amount(SurveyPlusConfigureDto info, Map<String, Integer> costAmountInfo) {
        if (info.getCountQuestion() < questionMin
                || info.getCountQuestion() > questionMax
                || info.getQuantity() < quantityMin
                || info.getQuantity() > quantityMax) {
            return -1;
        }
        long amount = 0;
        amount += addCostAmountInfo(costAmountInfo, "countQuestion", getQuestionPrice(countQuestion, info.getCountQuestion()));
        amount += addCostAmountInfo(costAmountInfo, "genderMatch", getTypeMatchMinPrice(genderMatch, info.getGenderMatch()));
        amount += addCostAmountInfo(costAmountInfo, "ageMatch", getTypeMatchMinPrice(ageMatch, info.getAgeMatch()));
        amount += addCostAmountInfo(costAmountInfo, "educationMatch", getTypeMatchMinPrice(educationMatch, info.getEducationMatch()));
        amount += addCostAmountInfo(costAmountInfo, "locationMatch", getTypeMatchMinPrice(locationMatch, info.getLocationMatch()));
        amount = amount * info.getQuantity();
        if (amount > maxAmount) {
            return -1;
        }
        return (int) amount;
    }

    private int addCostAmountInfo(Map<String, Integer> costAmountInfo, String key, int price) {
        costAmountInfo.put(key, price);
        return price;
    }

    private int getQuestionPrice(List<QuestionMatch> questionMatches, int match) {
        if (CollectionUtils.isNotEmpty(questionMatches)) {
            for (QuestionMatch questionMatch : questionMatches) {
                if (match >= questionMatch.min && match <= questionMatch.max) {
                    return questionMatch.price;
                }
            }
        }
        throw new BadRequestException("问题数价格未定义");
    }

    private int getTypeMatchMinPrice(List<TypeMatch> typeMatches, String match) {
        Set<Integer> prices = new HashSet<>();
        if (StringUtils.isNotEmpty(match)) {
            String[] as = match.split("/");
            for (String a : as) {
                int i = getTypeMatchPrice(typeMatches, a.trim());
                prices.add(i);
                if (i == 0L) {
                    break;
                }
            }
        }
        return prices.stream().min(Integer::compare).orElse(0);
    }

    private int getTypeMatchPrice(List<TypeMatch> typeMatches, String match) {
        if (CollectionUtils.isNotEmpty(typeMatches)) {
            for (TypeMatch typeMatch : typeMatches) {
                if (match.equals(typeMatch.name)) {
                    return typeMatch.price;
                }
            }
        }
        throw new BadRequestException(match + "价格未定义");
    }

    @Getter
    @Setter
    public static class QuestionMatch {
        private int min;
        private int max;
        private int price;
    }

    @Getter
    @Setter
    public static class TypeMatch {
        private String name;
        private int price;
    }
}
