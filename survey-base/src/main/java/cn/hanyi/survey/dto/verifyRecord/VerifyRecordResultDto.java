package cn.hanyi.survey.dto.verifyRecord;

import cn.hanyi.survey.core.constant.survey.SurveyVerifyStatus;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/9/1 17:03:26
 */

@Getter
@Setter
public class VerifyRecordResultDto {
    
    @JsonView(ResourceViews.Basic.class)
    private SurveyVerifyStatus status;

    @JsonView(ResourceViews.Basic.class)
    private String comment;

    @JsonView(ResourceViews.Basic.class)
    private Date createTime;

    @JsonView(ResourceViews.Basic.class)
    private SimpleUserInfo user;

}

