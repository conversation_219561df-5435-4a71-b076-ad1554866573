package cn.hanyi.survey.dto.template;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Setter
@Getter
public class TemplateConvertQuestionSequenceDto {
    @Schema(description = "问题Id")
    private Long questionId;
    @Schema(description = "问题序号")
    private BigDecimal sequence = BigDecimal.ZERO;
    @Schema(description = "问题展示code")
    private String code;
    @Schema(description = "问题name")
    private String name;
    @Schema(description = "问题title")
    private String title;
    @Schema(description = "题组code")
    private String groupCode;
}
