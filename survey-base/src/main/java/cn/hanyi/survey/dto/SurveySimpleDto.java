package cn.hanyi.survey.dto;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

import java.util.List;


@Getter
@Setter
public class SurveySimpleDto {

    @JsonView(ResourceViews.Basic.class)
    Long id;
    @JsonView(ResourceViews.Basic.class)
    String title;
    @JsonView(ResourceViews.Basic.class)
    List<SurveyQuestionSimpleDto> filterQuestions;

}
