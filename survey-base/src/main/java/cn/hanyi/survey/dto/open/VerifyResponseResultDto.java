package cn.hanyi.survey.dto.open;

import cn.hanyi.survey.core.constant.ResponseStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.befun.core.dto.BaseDTO;

import java.util.Date;

/**
 * <AUTHOR>
 * 验证答题有效性
 */
@Setter
@Getter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class VerifyResponseResultDto extends BaseDTO {
    @Schema(description = "问卷Id")
    private Long surveyId;
    @Schema(description = "答题Id")
    private Long responseId;
    @Schema(description = "自定义问卷编号")
    private String surveyCode;
    @Schema(description = "问卷名称")
    private String surveyTitle;
    @Schema(description = "答题状态")
    private ResponseStatus responseStatus;
    @Schema(description = "答题时间")
    private Date startTime;
    @Schema(description = "答题完成时间")
    private Date finishTime;
}
