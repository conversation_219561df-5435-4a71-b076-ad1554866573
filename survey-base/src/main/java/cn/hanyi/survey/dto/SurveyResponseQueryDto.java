package cn.hanyi.survey.dto;

import cn.hanyi.survey.core.constant.channel.SurveyCollectorMethod;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.query.ResourceCustomQueryDto;

import java.util.Date;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/11/10 16:19
 */
@Getter
@Setter
public class SurveyResponseQueryDto extends ResourceCustomQueryDto {
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createTime_lt;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createTime_gt;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date finishTime_lt;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date finishTime_gt;
    private Integer sequence;
    private String status_in;
    private SurveyCollectorMethod collectorMethod;
}
