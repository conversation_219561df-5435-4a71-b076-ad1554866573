package cn.hanyi.survey.dto.open;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;

/**
 * <AUTHOR>
 * 验证答题有效性
 */
@Setter
@Getter
public class VerifyResponseDto extends BaseDTO {
    @Schema(description = "问卷Id")
    private Long surveyId;
    @Schema(description = "答题Id", required = true)
    private Long responseId;
    @Schema(description = "自定义问卷编号")
    private String surveyCode;
}
