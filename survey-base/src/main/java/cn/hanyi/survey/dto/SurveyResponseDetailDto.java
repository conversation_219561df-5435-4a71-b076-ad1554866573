package cn.hanyi.survey.dto;

import cn.hanyi.survey.core.constant.ResponseStatus;
import cn.hanyi.survey.core.constant.channel.SurveyCollectorMethod;
import cn.hanyi.survey.core.entity.SurveyDto;
import cn.hanyi.survey.core.entity.SurveyResponse;
import cn.hanyi.survey.dto.open.ResponseSharedDetailDto;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.converter.HashMapConverter;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Convert;
import javax.persistence.Enumerated;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Setter
@Getter
public class SurveyResponseDetailDto extends BaseEntityDTO<SurveyResponse> {

    @JsonView(ResourceViews.Basic.class)
    private Long id;

    @Schema(description = "客户中心客户ID")
    @JsonView(ResourceViews.Basic.class)
    private Long customerId;

    @Schema(description = "外部用户ID")
    @JsonView(ResourceViews.Basic.class)
    private String externalUserId;

    @Schema(description = "客户端ID")
    @JsonView(ResourceViews.Basic.class)
    private String clientId;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "部门ID")
    private Long departmentId;

    @Schema(description = "部门名称")
    @JsonView(ResourceViews.Basic.class)
    private String departmentName;

    @Schema(description = "客户名称")
    @JsonView(ResourceViews.Basic.class)
    private String customerName;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "客户性别")
    private String customerGender;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "外部组织编号")
    private String departmentCode;

    @Schema(description = "外部企业ID")
    @JsonView(ResourceViews.Basic.class)
    private String externalCompanyId;

    @Schema(description = "默认参数")
    @JsonView(ResourceViews.Basic.class)
    private String defaultPa;

    @Schema(description = "默认参数")
    @JsonView(ResourceViews.Basic.class)
    private String defaultPb;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "默认参数")
    private String defaultPc;

    @Schema(description = "是否结束")
    @JsonView(ResourceViews.Basic.class)
    private Boolean isCompleted = false;

    private String ip;
    @JsonView(ResourceViews.Basic.class)
    private String country;
    @JsonView(ResourceViews.Basic.class)
    private String province;
    @JsonView(ResourceViews.Basic.class)
    private String city;

    @JsonView(ResourceViews.Basic.class)
    private String device;
    @JsonView(ResourceViews.Basic.class)
    private String os;
    @JsonView(ResourceViews.Basic.class)
    private String browser;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "答题持续时间(秒单位)")
    private Integer durationSeconds;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "总得分")
    private Integer totalScore;

    @JsonView(ResourceViews.Basic.class)
    private Date createTime;

    @Temporal(TemporalType.TIMESTAMP)
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "答题结束时间")
    private Date finishTime;

    @JsonView(ResourceViews.Basic.class)
    private String userAgent;

    @Convert(converter = HashMapConverter.class)
    @JsonView(ResourceViews.Basic.class)
    private Map<String, Object> parameters = new HashMap<>();

    @Enumerated
    @JsonView(ResourceViews.Basic.class)
    private ResponseStatus status = ResponseStatus.INIT;

    @Enumerated
    @Schema(description = "收集方法", example = "URL")
    @JsonView(ResourceViews.Basic.class)
    private SurveyCollectorMethod collectorMethod = SurveyCollectorMethod.LINK;

    @Schema(description = "渠道ID")
    @JsonView(ResourceViews.Basic.class)
    private Long channelId;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "答卷编号")
    private Long sequence;

    @Schema(description = "微信openid")
    @JsonView(ResourceViews.Basic.class)
    private String openid;

    @Schema(description = "答题数据")
    @JsonView(ResourceViews.Basic.class)
    private Map<String, Object> cellData;

    @Schema(description = "问题得分")
    @JsonView(ResourceViews.Basic.class)
    private Map<String, Integer> cellScore;

    @JsonView(ResourceViews.Basic.class)
    private String channelName;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "答题标签")
    private List<String> tags;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "分享答卷")
    private ResponseSharedDetailDto sharedToken;

    private SurveyDto survey;

}