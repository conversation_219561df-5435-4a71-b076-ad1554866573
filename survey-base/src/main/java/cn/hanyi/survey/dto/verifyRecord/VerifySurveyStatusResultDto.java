package cn.hanyi.survey.dto.verifyRecord;

import cn.hanyi.survey.core.constant.survey.SurveyStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2022/9/9 09:28:42
 */
@Getter
@Setter
public class VerifySurveyStatusResultDto {

    @Schema(description = "问卷状态")
    private SurveyStatus status;
    @Schema(description = "是否可以审核")
    private Boolean verify;
}
