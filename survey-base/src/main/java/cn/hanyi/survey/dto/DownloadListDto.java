package cn.hanyi.survey.dto;

import cn.hanyi.survey.core.constant.DownloadType;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.dto.usertask.UserTaskResponseDownloadDto;
import org.befun.core.rest.view.ResourceViews;
import org.befun.task.constant.TaskStatus;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Setter
@Getter
public class DownloadListDto {
    @JsonView(ResourceViews.Basic.class)
    private Long taskId;
    @JsonView(ResourceViews.Basic.class)
    private String surveyName;
    @JsonView(ResourceViews.Basic.class)
    private TaskStatus status;
    @JsonView(ResourceViews.Basic.class)
    private DownloadType fileType;
    @JsonView(ResourceViews.Basic.class)
    private Long fileSize;
    @JsonView(ResourceViews.Basic.class)
    private UserTaskResponseDownloadDto result;
    @JsonView(ResourceViews.Basic.class)
    private Date createTime;
    @JsonView(ResourceViews.Basic.class)
    private Integer processCount = 0;
    @JsonView(ResourceViews.Basic.class)
    private Integer totalCount = 0;
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "批量下载需返回上一次的答卷id用于重新下载")
    private List<Long> lastResponseIds = new ArrayList<>();

}
