package cn.hanyi.survey.dto;

import cn.hanyi.survey.core.constant.ResponseStatus;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;

import java.util.List;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/1/21 3:24 下午
 */
@Getter
@Setter
public class SurveyResponseIdsDto extends BaseDTO {
    private List<Long> ids;
    private ResponseStatus status;
    //批量处理 选择全部的时候 是true
    private Boolean processAll = false;
    private SurveyResponseQueryDto queryDto;

    public void appendIds(List<Long> ids) {
        if (this.processAll) this.ids = ids;
    }

}
