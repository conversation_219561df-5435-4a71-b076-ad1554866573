package cn.hanyi.survey.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class ChannelRefundInfo {
    //  {
    //      "type": "full",
    //      "unitPrice": 1,
    //      "quantity": 101,
    //      "recycle": 100,
    //      "serviceRate": 0.06,
    //      "refundAmount": 100
    //  }
    private String type;
    private Integer unitPrice;
    private Integer quantity;
    private Integer recycle;
    private Double serviceRate;
    private Long refundAmount;
    private String payType;
    private Date refundTime;

}
