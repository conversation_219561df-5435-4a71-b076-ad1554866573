package cn.hanyi.survey.dto;

import cn.hanyi.survey.core.constant.survey.SurveyStatus;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyGroupDto;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.auth.projection.SimpleUser;
import org.befun.core.dto.BaseResourcePermissionEntityDto;
import org.befun.core.dto.EntityWithGroupDTO;
import org.befun.core.rest.annotation.ResourceFieldCloneRule;
import org.befun.core.rest.view.ResourceViews;

import java.util.Date;


@Getter
@Setter
public class SurveySimpleListDto extends BaseResourcePermissionEntityDto<Survey>  implements EntityWithGroupDTO<SurveyGroupDto> {

    @JsonView(ResourceViews.Basic.class)
    Long id;
    @JsonView(ResourceViews.Basic.class)
    String title;
    @Schema(description = "问题数量")
    @JsonView(ResourceViews.Basic.class)
    private Integer numOfQuestions;
    @ResourceFieldCloneRule(ignore = true)
    @Schema(description = "问卷状态 0已停用")
    @JsonView(ResourceViews.Basic.class)
    private SurveyStatus status = SurveyStatus.STOPPED;

    @Schema(description = "答题数量")
    @JsonView(ResourceViews.Basic.class)
    private Integer numOfResponses;

    @JsonView(ResourceViews.Basic.class)
    Date modifyTime;
    @Schema(description = "修改人，可能为null")
    @JsonView(ResourceViews.Basic.class)
    private SimpleUser editorUser;

    @Schema(description = "创建人，可能为null")
    @JsonView(ResourceViews.Basic.class)
    private SimpleUser creator;

    @Schema(description = "是否有分享给其他人")
    @JsonView(ResourceViews.Basic.class)
    private Boolean hasShared;

    @Schema(description = "项目类型：1 问卷 2 目录")
    @JsonView(ResourceViews.Basic.class)
    private int itemType = 1;

    @Schema(description = "目录信息")
    @JsonView(ResourceViews.Basic.class)
    private SurveyGroupDto group;


    // 重写 hasShared get
    public Boolean getHasShared() {
        return CollectionUtils.isNotEmpty(getPermissionUsers())
                || CollectionUtils.isNotEmpty(getPermissionRoles())
                || CollectionUtils.isNotEmpty(getPermissionDepartments());
    }

}
