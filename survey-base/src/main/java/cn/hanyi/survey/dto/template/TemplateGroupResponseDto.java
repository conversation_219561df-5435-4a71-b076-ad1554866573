package cn.hanyi.survey.dto.template;

import cn.hanyi.survey.core.entity.template.TemplateSurvey;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 */

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class TemplateGroupResponseDto {
    private List<String> title; //组名
    private Page<TemplateSurvey> templateSurveys; //问卷模板
}
