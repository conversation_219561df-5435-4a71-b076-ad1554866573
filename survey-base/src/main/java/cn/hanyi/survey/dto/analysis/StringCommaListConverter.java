package cn.hanyi.survey.dto.analysis;

import org.springframework.util.StringUtils;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Converter
public class StringCommaListConverter implements AttributeConverter<List<String>, String> {

    public StringCommaListConverter() {
    }

    public String getSplitChar() {
        return ",";
    }

    @Override
    public String convertToDatabaseColumn(List<String> stringList) {
        return stringList != null ? String.join(this.getSplitChar(), stringList) : null;
    }

    @Override
    public List<String> convertToEntityAttribute(String string) {
        return StringUtils.isEmpty(string) ? Collections.emptyList() : Arrays.asList(string.split(this.getSplitChar()));
    }
}


