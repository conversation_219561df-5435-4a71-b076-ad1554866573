package cn.hanyi.survey.dto;

import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.core.entity.SurveyQuestionItem;
import cn.hanyi.survey.core.entity.SurveyResponseCell;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Setter
@Getter
@NoArgsConstructor
public class SurveyResponseCellContextDto extends BaseDTO {
    private static final long serialVersionUID = 6529685098267757690L;

    private Object value;
    private List<SurveyQuestionItem> items = new ArrayList<>();
    private SurveyQuestion question;
    private Survey survey;

//    public SurveyResponseCellContextDto(Survey survey, SurveyResponseCell cell) {
//        this.survey = survey;
//        this.question = cell.getQuestion();
//        this.items = cell.getQuestion().getItems().stream().collect(Collectors.toList());
//        this.value = cell.getValue();
//    }
//
//    public SurveyResponseCellContextDto(Survey survey, SurveyQuestion question, Object value) {
//        this.survey = survey;
//        this.question = question;
//        this.items = question.getItems().stream().collect(Collectors.toList());
//        this.value = value;
//    }
}
