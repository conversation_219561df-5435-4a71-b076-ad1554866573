package cn.hanyi.survey.dto.survey;

import cn.hanyi.survey.core.entity.SurveyQuestion;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/10/24 17:34
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class SurveyImportDto {

    @Schema(description = "欢迎语")
    private String welcomingRemark = "感谢您能抽出几分钟时间来参加本次答题，我们马上开始吧！";

    @Schema(description = "结束语")
    private String concludingRemark = "{\"text\":\"<p class=\\\"ql-align-center\\\" style=\\\"text-align:center;\\\"><img src=\\\"https://assets.surveyplus.cn/surveylite/static/empty-end.svg\\\" /></p><p class=\\\"ql-align-center\\\" style=\\\"color:#999;padding-top:5px;text-align:center;\\\">问卷到此结束，感谢您的参与!</p>\",\"link\":\"\",\"type\":\"text\"}";

    @Schema(description = "非正常结束语")
    private String abnormalConcludingRemark = "{\"text\":\"<p class=\\\"ql-align-center\\\"  style=\\\"text-align:center;\\\"><img src=\\\"https://assets.surveyplus.cn/surveylite/static/empty-end.svg\\\" /></p><p class=\\\"ql-align-center\\\" style=\\\"color:#999;padding-top:5px;text-align:center;\\\">非常抱歉，你不适合本次调查的人群条件，感谢您的参与!</p>\",\"link\":\"\",\"type\":\"text\"}";

    @Schema(description = "问卷别名")
    private String title;

    @Schema(description = "问卷标题")
    private String realTitle;

    private List<SurveyQuestion> questions;

    private Long groupId;

}
