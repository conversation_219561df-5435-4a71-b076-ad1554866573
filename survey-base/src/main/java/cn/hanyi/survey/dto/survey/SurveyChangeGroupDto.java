package cn.hanyi.survey.dto.survey;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Getter
@Setter
public class SurveyChangeGroupDto {

    @NotNull
    @Schema(description = "移入未分组时，为0", required = true)
    private Long targetGroupId;

    @NotEmpty
    @Schema(description = "要移动的问卷id", required = true)
    private List<Long> surveyIds;
}
