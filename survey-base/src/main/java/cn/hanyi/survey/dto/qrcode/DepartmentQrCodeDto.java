package cn.hanyi.survey.dto.qrcode;

import lombok.Getter;
import lombok.Setter;
import org.befun.auth.dto.DepartmentTreeDto;
import org.befun.auth.dto.TreeDto;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class DepartmentQrCodeDto implements TreeDto<DepartmentQrCodeDto> {
    private Long id;
    private String title;
    private Long pid;
    private Long answersNum;
    private String shortLink;

    private List<DepartmentQrCodeDto> subDepartments = new ArrayList<>();

    public static DepartmentQrCodeDto mapFrom(DepartmentTreeDto department) {
        if (department == null) {
            return null;
        }
        DepartmentQrCodeDto tree = new DepartmentQrCodeDto();
        tree.setId(department.getId());
        tree.setTitle(department.getTitle());
        tree.setPid(department.getPid());
        return tree;
    }

    @Override
    public List<DepartmentQrCodeDto> children() {
        return subDepartments;
    }

    @Override
    public void addChild(DepartmentQrCodeDto departmentQrCodeDto) {
        subDepartments.add(departmentQrCodeDto);
    }
}
