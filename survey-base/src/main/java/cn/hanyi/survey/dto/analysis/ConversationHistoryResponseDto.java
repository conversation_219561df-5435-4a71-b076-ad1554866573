package cn.hanyi.survey.dto.analysis;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/14 14:29:02
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ConversationHistoryResponseDto {

    @Schema(description = "类型-数据")
    private List<ConversationHistoryMessageResponseDto> report;

    @Schema(description = "类型-数据")
    private List<ConversationHistoryMessageResponseDto> chat;

}