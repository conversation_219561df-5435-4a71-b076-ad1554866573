package cn.hanyi.survey.dto.analysis;

import com.google.common.base.Strings;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.befun.core.dto.BaseDTO;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FilterDto extends BaseDTO {
    @Schema(description = "纬度类型")
    private String name;

    @Schema(description = "操作符号, 目前支持 eq")
    private String operator = "eq";

    @Schema(description = "数值")
    private Object value;

    public boolean isValid() {
        return !Strings.isNullOrEmpty(name) && value != null && !Strings.isNullOrEmpty(operator);
    }
}
