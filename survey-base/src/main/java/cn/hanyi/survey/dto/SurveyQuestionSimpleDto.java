package cn.hanyi.survey.dto;

import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.projection.SimpleQuestionItem;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

import java.util.List;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class SurveyQuestionSimpleDto {
    @JsonView(ResourceViews.Basic.class)
    Long id;
    @JsonView(ResourceViews.Basic.class)
    String title;
    @JsonView(ResourceViews.Basic.class)
    QuestionType type;
    @JsonView(ResourceViews.Basic.class)
    List<SimpleQuestionItem> questionItems;

}
