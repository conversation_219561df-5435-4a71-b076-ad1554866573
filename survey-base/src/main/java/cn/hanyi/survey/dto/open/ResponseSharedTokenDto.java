package cn.hanyi.survey.dto.open;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;

import java.util.Date;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class ResponseSharedTokenDto extends BaseDTO {

    private Long surveyId;
    private Long responseId;
    private String token;
    private Date expireTime;

    public ResponseSharedTokenDto(String token, Date expireTime) {
        this.token = token;
        this.expireTime = expireTime;
    }
}
