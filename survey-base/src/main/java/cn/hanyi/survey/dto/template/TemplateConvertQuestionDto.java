package cn.hanyi.survey.dto.template;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class TemplateConvertQuestionDto {
    @Schema(description = "问卷Id")
    private Long surveyId;
    @Schema(description = "模板问题列表")
    private List<TemplateConvertQuestionSequenceDto> questions;
}
