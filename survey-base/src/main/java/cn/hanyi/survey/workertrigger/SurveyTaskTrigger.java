package cn.hanyi.survey.workertrigger;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class SurveyTaskTrigger implements ISurveyTaskTrigger {

    @Override
    public boolean emptyImpl() {
        return false;
    }

    @Autowired(required = false)
    private List<ISurveyTaskConsumer> eventConsumers;

    @Override
    public List<ISurveyTaskConsumer> getConsumers() {
        return eventConsumers;
    }

}
