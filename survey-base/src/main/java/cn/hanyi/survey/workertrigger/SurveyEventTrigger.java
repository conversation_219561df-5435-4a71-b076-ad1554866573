package cn.hanyi.survey.workertrigger;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class SurveyEventTrigger implements ISurveyEventTrigger {

    @Autowired(required = false)
    private List<ISurveyEventConsumer> eventConsumers;

    @Override
    public List<ISurveyEventConsumer> getConsumers() {
        return eventConsumers;
    }

}
