package cn.hanyi.survey.service.download.question;

import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.core.entity.SurveyResponseCell;
import cn.hanyi.survey.service.download.IDownloadQuestionTypeParser;
import cn.hanyi.survey.service.download.dto.DownloadColumnQuestion;
import cn.hanyi.survey.service.download.dto.DownloadColumnQuestionGroup;
import cn.hanyi.survey.service.download.dto.ResponseDownloadContext;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.IntStream;

@Component
public class DownloadQuestionTypeParser_026_LOCATION implements IDownloadQuestionTypeParser {

    @Override
    public QuestionType type() {
        return QuestionType.LOCATION;
    }

    @Override
    public DownloadColumnQuestionGroup buildGroup(ResponseDownloadContext context, SurveyQuestion question) {
        DownloadColumnQuestionGroup group = new DownloadColumnQuestionGroup(question, question.getItems(), question.getColumns());
        List<String> codes = List.of("locationType", "longitude", "latitude", "location");
        List<String> labels = List.of("数据来源", "经度", "纬度", "位置名称");
        IntStream.range(0, 4).forEach(i -> {
            group.getColumns().add(new DownloadColumnQuestion_LOCATION_item(context, group,
                    group.getCode() + "_" + group.getTitle() + "_" + labels.get(i),
                    group.getCode() + "_" + (i == 0 ? "datasource" : codes.get(i)),
                    i == 1 || i == 2, codes.get(i)));
        });
        return group;
    }

    public static class DownloadColumnQuestion_LOCATION_item extends DownloadColumnQuestion {

        private final boolean doubleValue;
        private final String key;

        public DownloadColumnQuestion_LOCATION_item(ResponseDownloadContext context, DownloadColumnQuestionGroup group, String label, String code, boolean doubleValue, String key) {
            super(context, group, label, code);
            this.doubleValue = doubleValue;
            this.key = key;
        }

        @Override
        public Object getCode(SurveyResponseCell cell) {
            Map<String, Object> value = cell.getJsonValue();
            if (value != null) {
                String code = Optional.ofNullable(value.get(key)).map(Object::toString).orElse(null);
                if (doubleValue && NumberUtils.isCreatable(code)) {
                    return Double.parseDouble(code);
                }
                return code;
            }
            return null;
        }

        @Override
        public Object getLabel(SurveyResponseCell cell) {
            return getCode(cell);
        }
    }

}
