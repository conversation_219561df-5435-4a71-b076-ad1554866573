package cn.hanyi.survey.service.download.question;

import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.core.entity.SurveyQuestionItem;
import cn.hanyi.survey.core.entity.SurveyResponseCell;
import cn.hanyi.survey.service.download.IDownloadQuestionTypeParser;
import cn.hanyi.survey.service.download.dto.DownloadColumnQuestionGroup;
import cn.hanyi.survey.service.download.dto.DownloadColumnQuestion_item;
import cn.hanyi.survey.service.download.dto.ResponseDownloadContext;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;

@Component
public class DownloadQuestionTypeParser_023_RANKING implements IDownloadQuestionTypeParser {

    @Override
    public QuestionType type() {
        return QuestionType.RANKING;
    }

    @Override
    public DownloadColumnQuestionGroup buildGroup(ResponseDownloadContext context, SurveyQuestion question) {
        DownloadColumnQuestionGroup group = new DownloadColumnQuestionGroup(question, question.getItems(), question.getColumns());
        AtomicInteger index = new AtomicInteger(1);
        group.getQuestionItems().forEach(i -> {
            int itemIndex = index.getAndIncrement();
            // 增加选项列，每个选项作为一列
            group.getColumns().add(new DownloadColumnQuestion_RANKING_item(context, group, itemIndex, i));
        });
        return group;
    }

    public static class DownloadColumnQuestion_RANKING_item extends DownloadColumnQuestion_item {

        public DownloadColumnQuestion_RANKING_item(ResponseDownloadContext context, DownloadColumnQuestionGroup group, int itemIndex, SurveyQuestionItem item) {
            super(context, group, itemIndex, item);
        }

        @Override
        public Object getCode(SurveyResponseCell cell) {
            return Optional.ofNullable(cell.getJsonValue()).map(m -> m.get(item.getValue())).filter(i -> i instanceof Number).map(j -> ((Number) j).intValue()).orElse(null);
        }

        @Override
        public Object getLabel(SurveyResponseCell cell) {
            return getCode(cell);
        }
    }

}
