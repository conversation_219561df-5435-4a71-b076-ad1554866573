package cn.hanyi.survey.service;

import cn.hanyi.survey.client.cache.bean.CacheApiKey;
import cn.hanyi.survey.client.cache.hash.ApiKeyCacheHelper;
import cn.hanyi.survey.client.service.SurveyDisturbRuleService;
import cn.hanyi.survey.client.service.submit.limiter.BeforeLimiterChannel;
import cn.hanyi.survey.core.constant.QuotaChannelType;
import cn.hanyi.survey.core.constant.ResponseStatus;
import cn.hanyi.survey.core.constant.channel.ChannelType;
import cn.hanyi.survey.core.constant.error.SurveyErrorCode;
import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.constant.survey.SurveyQuotaStatus;
import cn.hanyi.survey.core.constant.survey.SurveyStatus;
import cn.hanyi.survey.core.dto.SurveyLinkDto;
import cn.hanyi.survey.core.dto.SurveyResponseFinishTimeOnly;
import cn.hanyi.survey.core.dto.SurveyWithGroupTreeDto;
import cn.hanyi.survey.core.dto.ext.SurveyEmbedExtDto;
import cn.hanyi.survey.core.entity.*;
import cn.hanyi.survey.core.exception.SurveyErrorException;
import cn.hanyi.survey.core.projection.SimpleSurvey;
import cn.hanyi.survey.core.projection.SimpleSurveyWithGroup;
import cn.hanyi.survey.core.repository.*;
import cn.hanyi.survey.core.utilis.AESUtils;
import cn.hanyi.survey.dto.SurveyProcessDto;
import cn.hanyi.survey.dto.index.SimpleSurveyDto;
import cn.hanyi.survey.dto.index.SimpleSurveyQuestionDto;
import cn.hanyi.survey.dto.survey.SurveyCloneDto;
import cn.hanyi.survey.dto.survey.SurveyImportDto;
import cn.hanyi.survey.service.word.IDownloadSurvey;
import cn.hanyi.survey.service.word.StringBufferHelper;
import cn.hanyi.survey.workertrigger.ISurveyEventTrigger;
import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.core.util.CharsetUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.poifs.filesystem.DirectoryEntry;
import org.apache.poi.poifs.filesystem.DocumentEntry;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.befun.auth.annotations.AddTenantContext;
import org.befun.auth.constant.OrganizationConfigType;
import org.befun.auth.constant.TenantContext.TenantFields;
import org.befun.auth.dto.orgconfig.OrgConfigDto;
import org.befun.auth.entity.Organization;
import org.befun.auth.entity.User;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.OrganizationConfigService;
import org.befun.auth.service.OrganizationService;
import org.befun.auth.service.SystemNotificationService;
import org.befun.auth.service.UserService;
import org.befun.core.constant.EntityScopeStrategyType;
import org.befun.core.constant.EntityScopeStrategyTypes;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.exception.BadRequestException;
import org.befun.core.exception.EntityNotFoundException;
import org.befun.core.exception.OverLimitException;
import org.befun.core.rest.context.DeepEmbeddedFieldContext;
import org.befun.core.rest.context.EmbeddedFieldContext;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.BaseService;
import org.befun.core.utils.EntityUtility;
import org.befun.core.utils.JsonHelper;
import org.befun.core.utils.RestUtils;
import org.befun.extension.entity.Link;
import org.befun.extension.service.FileService;
import org.befun.extension.service.LinkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.PostConstruct;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.ByteArrayInputStream;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static org.befun.core.rest.context.FieldRelationType.ONE_TO_MANY;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class SurveyService extends BaseService<Survey, SurveyDto, SurveyRepository> {

    @Autowired
    SurveyResponseRepository responseRepository;

    @Autowired
    SurveyResponseCellRepository responseCellRepository;

    @Autowired
    SurveyLogicRepository logicRepository;

    @Autowired
    SurveyQuestionRepository surveyQuestionRepository;

    @Lazy
    @Autowired
    QuotaService quotaService;

    @Lazy
    @Autowired
    ChannelService channelService;

    @Autowired
    ResponseService responseService;

    @Autowired
    UserService userService;

    @Autowired
    private SurveyGroupRepository surveyGroupRepository;

    @Autowired
    private SurveyGroupService surveyGroupService;

    @Autowired
    private QuestionService questionService;

    @Autowired
    private SurveyQuestionItemRepository surveyQuestionItemRepository;

    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private ISurveyEventTrigger surveyEventTrigger;

    @Autowired
    private OrganizationConfigService organizationConfigService;

    @Autowired
    private SurveyLotteryRepository surveyLotteryRepository;

    @Autowired
    private OrganizationConfigService orgConfigService;

    @Autowired
    private SurveyDisturbRuleService surveyDisturbRuleService;

    @Autowired
    private SurveyLotteryService surveyLotteryService;

    @Value("${survey.cem-domain:}")
    private String cemDomain;

    @Value("${survey.survey-url-prefix.path:lite}")
    private String surveyUrlPrefixPath;

    @Autowired
    private LinkService linkService;

    @Autowired(required = false)
    private List<IDownloadSurvey> downloadSurveys;

    private Map<QuestionType, IDownloadSurvey> downloadSurveyMap = new HashMap<>();

    @Autowired
    protected ApiKeyCacheHelper apiKeyCacheHelper;

    @Autowired
    private SurveyContentAuditService surveyContentAuditService;

    @Autowired(required = false)
    private FileService fileService;

    @Autowired
    private SystemNotificationService systemNotificationService;

    @Autowired(required = false)
    private BeforeLimiterChannel beforeLimiterChannel;

    @PostConstruct
    public void init() {
        Optional.ofNullable(downloadSurveys).ifPresent(x -> x.forEach(y -> downloadSurveyMap.put(y.type(), y)));
    }

    @Override
    protected void addCustomRelations(Map<String, EmbeddedFieldContext> relationMaps) {
        // 把SurveyResponse模拟成Survey的下一级
        relationMaps.put("responses", new EmbeddedFieldContext(null, "surveyId", ONE_TO_MANY));
        // 把SurveyChannel模拟成Survey的下一级
        EmbeddedFieldContext channel = new EmbeddedFieldContext(null, "sid", ONE_TO_MANY);
        relationMaps.put("channel", channel);
        // 把SurveyChannelRecord模拟成SurveyChannel的下一级
        DeepEmbeddedFieldContext records = new DeepEmbeddedFieldContext(null, ONE_TO_MANY, "channelId", channel);
        channel.deepRelationMaps.put("records", records);

        EmbeddedFieldContext lottery = new EmbeddedFieldContext(null, "sid", ONE_TO_MANY);
        relationMaps.put("lottery", lottery);

        EmbeddedFieldContext crossAnalysis = new EmbeddedFieldContext(null, "sid", ONE_TO_MANY);
        relationMaps.put("crossAnalysis", crossAnalysis);
    }

    @Override
    public List<SurveyDto> mapToDto(List<Survey> entity) {
        entity.forEach(questionService::graphQuestion);
        return super.mapToDto(entity);
    }

    @Override
    public SurveyDto mapToDto(Survey entity) {
        questionService.graphQuestion(entity);
        SurveyDto dto = super.mapToDto(entity);
        //dto.setLink(getOrCreateLink(entity));
        organizationService.getById(dto.getOrgId()).ifPresent(org -> {
            dto.setEnableLogoChange(organizationService.parseOrgOptionalLimit(org).getLogoChangeLimit());
            dto.setEnableMultiLanguage(organizationService.parseOrgOptionalLimit(org).getMultiLanguageLimit());
        });
        fillSurveyUrl(dto);
        return dto;
    }

    private void fillSurveyUrl(SurveyDto dto) {

        AtomicReference<String> clientUrl = new AtomicReference<>();

        Optional.ofNullable(orgConfigService.getConfig(dto.getOrgId(), OrganizationConfigType.clientInfo))
                .flatMap(configDto -> Optional.ofNullable(configDto.getClientInfo()))
                .ifPresent(clientInfo -> {
                    if (StringUtils.isNotEmpty(clientInfo.getSurveyPrefixUrl())) {
                        clientUrl.set(clientInfo.getSurveyPrefixUrl());
                    }
                });

        Optional.ofNullable(clientUrl.get()).ifPresentOrElse(
                clientUrlStr -> dto.setLink(new SurveyLinkDto(clientUrlStr + "/" + dto.getId())),
                () -> {
                    HttpServletRequest request =
                            ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();
                    String host = request.getHeader("Origin") == null ? request.getHeader("host") : request.getHeader("Origin");
                    UrlBuilder urlBuilder = UrlBuilder.ofHttp(host == null ? request.getServerName() : host, CharsetUtil.CHARSET_UTF_8);

                    String scheme = request.getScheme();
                    String serverName = urlBuilder.getHost();
                    int port = urlBuilder.getPort();
                    String url = "%s://%s%s";
                    String portStr = "";
                    if (!List.of(80, 443, -1).contains(port)) {
                        portStr += ":" + port;
                    }
                    String prefix = String.format(url, scheme, serverName, portStr);
                    clientUrl.set(prefix + "/" + surveyUrlPrefixPath);
                }
        );

        dto.setLink(new SurveyLinkDto(clientUrl.get() + "/" + dto.getId()));
    }

    @Override
    @AddTenantContext({TenantFields.ISAdmin})
    public Page<SurveyDto> findAll(ResourceEntityQueryDto<SurveyDto> queryDto) {
        return super.findAll(queryDto);
    }

    @Override
    public <S extends BaseEntityDTO<Survey>> SurveyDto updateOne(long id, S change) {
        return super.updateOne(id, change);
    }

    /**
     * 获取问卷-不存在报错
     *
     * @param surveyId
     * @return
     */
    public Survey requireSurvey(@NotNull Long surveyId) {
        Optional<Survey> surveyOptional = repository.findById(surveyId);
        Survey survey = surveyOptional.orElse(null);

        if (survey == null) {
            throw new SurveyErrorException(SurveyErrorCode.SURVEY_DELETE);
        }

        return survey;
    }

    /**
     * 获取问卷-可为null
     *
     * @param surveyId
     * @return
     */
    public Survey getSurvey(@NotNull Long surveyId) {
        Optional<Survey> surveyOptional = repository.findById(surveyId);
        return surveyOptional.orElse(null);
    }

    /**
     * 获取问卷-可为null
     *
     * @param surveyId
     * @return
     */
    @Transactional
    public Survey getSurveyWithQuestions(@NotNull Long surveyId) {
        Optional<Survey> surveyOptional = repository.findById(surveyId);
        surveyOptional.ifPresent(i -> {
            i.getQuestions().forEach(j -> {
                j.getItems();
                j.getColumns();
            });
        });
        return surveyOptional.orElse(null);
    }

    /**
     * 获取问卷:可获取别的用户下的问卷
     *
     * @param surveyId
     * @return
     */
    public Survey get(@NotNull Long surveyId) {
        Optional<Survey> surveyOptional = repository.getOneById(surveyId);
        return surveyOptional.orElse(null);
    }


    @Override
    public void afterMapToDto(List<Survey> entity, List<SurveyDto> dto) {
        super.afterMapToDto(entity, dto);
        Set<Long> updateUserIds = new HashSet<>();
        Set<Long> groupIds = new HashSet<>();
        dto.forEach(survey -> {
            Survey e = survey.getEntity();
            Long numOfResponse = responseRepository.countBySurveyIdAndStatus(e.getId(), ResponseStatus.FINAL_SUBMIT);
            survey.setNumOfResponses(numOfResponse == null ? 0 : numOfResponse.intValue());
            SurveyResponseFinishTimeOnly finishTime = responseRepository.findTopBySurveyIdAndStatusOrderByFinishTimeDesc(e.getId(), ResponseStatus.FINAL_SUBMIT);
            survey.setLastResponseTime(finishTime == null ? null : finishTime.getFinishTime());
            Optional.ofNullable(survey.getEditorId()).ifPresent(updateUserIds::add);
            Optional.ofNullable(survey.getUserId()).ifPresent(updateUserIds::add);
            Optional.ofNullable(survey.getGroupId()).ifPresent(groupIds::add);
        });
        //未登录的用户 不展示创建者信息
        if (TenantContext.getCurrentTenant() != null) {
            Map<Long, SimpleUser> userMap = userService.getGroupMapByIds(new ArrayList<>(updateUserIds), User::getId, SimpleUser::fromUser);
            Map<Long, SurveyGroupDto> groupMap = surveyGroupService.getGroupMapByIds(new ArrayList<>(groupIds), SurveyGroup::getId);
            dto.forEach(survey -> {
                Optional.ofNullable(survey.getEditorId()).ifPresent(i -> survey.setEditorUser(userMap.get(i)));
                Optional.ofNullable(survey.getUserId()).ifPresent(i -> survey.setCreator(userMap.get(i)));
                Optional.ofNullable(survey.getGroupId()).ifPresent(i -> survey.setGroup(groupMap.get(i)));
            });
        }
    }

    /**
     * 校验问卷配额 状态
     *
     * @param survey
     * @return
     */
    public Boolean checkSurveyQuota(@NotNull Survey survey) {
        if ((survey.getEnableQuota() || survey.getEnableAdminxQuota()) && !survey.getQuotas().isEmpty())
            return true;
        else
            return false;
    }

    /**
     * 校验社区问卷配额 AdminX
     *
     * @param survey
     * @return
     */
    public Boolean checkSurveyAdminxQuota(@NotNull Survey survey) {
        if (survey.getEnableAdminxQuota() && !survey.getQuotas().isEmpty())
            return true;
        else
            return false;
    }

    /**
     * 插入题型，按照sequence的位置自动排序后面所有的题型
     *
     * @param survey
     * @param question
     * @return
     */
    public SurveyDto insertQuestion(Survey survey, SurveyQuestion question) {
        EntityUtility.fillUpReference(question);
        question.setSurvey(survey);
        questionService.increaseSequence(survey.getId(), question.getSequence(), 1);
        questionService.save(question);
        questionService.graphQuestion(survey);
        return mapToDto(survey);
    }

    /**
     * 校验问卷审核状态
     *
     * @param status
     */
    public void checkSurveyVerifyStatus(SurveyStatus status) {
        Boolean surveyVerify = organizationConfigService.getOrDefaultConfig(OrganizationConfigType.surveyVerify).getSurveyVerify();
        //问卷审核开启，问卷状态只有在停止状态和已驳回状态才能添加问题
        if ((surveyVerify && !SurveyStatus.STOPPED.equals(status))) {
            if (!SurveyStatus.REJECTED.equals(status)) {
                throw new BadRequestException("问卷状态已变更");
            }
        }
        //问卷审核关闭，问卷状态启用中不能添加问题，其他状态可以
        if ((!surveyVerify && SurveyStatus.COLLECTING.equals(status))) {
            throw new BadRequestException("问卷状态已变更");
        }
    }


    /**
     * clone survey (deep, check BaseEntity->clone)
     *
     * @param
     * @return
     */
    public SurveyDto clone(Long sid, SurveyCloneDto dto) {
        Survey survey = this.requireSurvey(sid);
        Survey surveyCopy = (Survey) survey.clone();
        surveyCopy.setUserId(TenantContext.getCurrentUserId());
        clearUnnecessaryData(surveyCopy);
        updateSurvey(surveyCopy, dto);
        repository.save(surveyCopy);
        return mapToDto(surveyCopy);
    }

    private void updateSurvey(Survey survey, SurveyCloneDto dto) {
        if (dto.getGroupId() != null) {
            survey.setGroupId(dto.getGroupId());
        }
        if (dto.getSurveyTitle() != null) {
            survey.setTitle(dto.getSurveyTitle());
        }
    }

    /**
     * 复制问卷到指定账号
     *
     * @param survey
     * @return
     */
    public Boolean copy(Survey survey, Long userId, Long orgId) {
        Survey surveyCopy = (Survey) survey.clone();
        surveyCopy.setUserId(userId);
        surveyCopy.setOrgId(orgId);
        surveyCopy.setGroupId(null);
        clearUnnecessaryData(surveyCopy);
        surveyCopy.setTitle(surveyCopy.getTitle().replace("_复制", "_模板"));
        repository.save(surveyCopy);
        return true;
    }

    /**
     * 复制时不需要的数据置空
     */
    private void clearUnnecessaryData(Survey survey) {
        survey.setSurveyCode(null);
        if (survey.getQuotas().size() > 0) {
            survey.setQuotaStatus(SurveyQuotaStatus.CALCULATE_INIT);
            survey.setEnableQuota(true);
        } else {
            survey.setQuotaStatus(SurveyQuotaStatus.CALCULATE_INIT);
            survey.setEnableQuota(false);
        }
        survey.getQuotas().removeIf(x -> x.getChannelType() == QuotaChannelType.SURVEY_PLUS);
        survey.getQuotas().stream().forEach(quota -> {
            quota.setExpressionHash(null);
        });
        survey.setGroupId(null); // v1.10.4 复制问卷清空目录信息
        survey.setResponseFinishNum(0);
        //结束页
        survey.setPersonalizedRemarks(List.of());
        survey.setConcludingRemark(parseRemark(survey.getConcludingRemark()));
        survey.setAbnormalConcludingRemark(parseRemark(survey.getAbnormalConcludingRemark()));

    }

    /**
     * 处理结束页，只保留结束页内容，不保留链接
     *
     * @param remark
     * @return
     */
    private String parseRemark(String remark) {
        Map<String, Object> map = null;
        try {
            map = JsonHelper.toMap(remark);
            map.put("type", "text");
            //link不保存模板
            map.put("link", null);
            return JsonHelper.toJson(map);
        } catch (Exception e) {
            throw new BadRequestException("结束语解析错误：" + remark);
        }
    }


    /**
     * 创建问卷
     *
     * @param survey
     * @return
     */
    public SurveyDto create(Survey survey) {
        EntityUtility.fillUpReference(survey);
        checkSurveyCode(survey);
        return mapToDto(repository.save(survey));
    }


    public SurveyDto updateOne(long id, Map<String, Object> change) {
        Survey survey = requireSurvey(id);
        Optional.ofNullable(change.get("logo")).ifPresent(logo -> {
            if (survey.getLogo() != null && !logo.equals(survey.getLogo())) {
                fileService.delete(survey.getLogo());
            }
        });
        EntityUtility.mergeEntityWithData(survey, change);
        checkSurveyCode(survey);
        surveyEventTrigger.surveyUpdate(id);
        return mapToDto(repository.save(survey));
    }

    /**
     * 企业下问卷编号唯一
     *
     * @param survey
     * @return
     */
    private void checkSurveyCode(Survey survey) {
        if (survey.getSurveyCode() != null && !survey.getSurveyCode().isEmpty()) {
            Optional<Survey> surveyOptional = repository.findFirstBySurveyCodeAndOrgId(survey.getSurveyCode(), TenantContext.getCurrentTenant());
            if (surveyOptional.isPresent() &&
                    !surveyOptional.get().getId().equals(survey.getId()) &&
                    survey.getSurveyCode().equals(surveyOptional.get().getSurveyCode())) {
                throw new BadRequestException(SurveyErrorCode.SURVEY_CODE_REPEAT.getMessage());
            }
        }
    }


    /**
     * 问卷下clientId的问卷和答题数据数据
     *
     * @param survey
     * @param clientId
     * @return
     */
//    public SurveyDto checkAndGetSurvey(Survey survey, String clientId, SurveySubmitRequestDto requestDto) {
//
//        //如果问卷链接有渠道号 对问卷渠道进行校验
//        SurveyChannel surveyChannel = new SurveyChannel();
//        if (requestDto.getChannelId() != null) {
//            surveyChannel = channelService.submitWithChannel(survey, clientId, requestDto.getChannelId(), requestDto.getOpenid());
//        }
//
//        responseService.saveAccessClient(survey, clientId, requestDto);
//
//        LinkedHashMap<String, Object> cellData = new LinkedHashMap<>();
//
//        Optional<SurveyResponse> surveyResponseOptional = responseService.clientIdData(survey, clientId);
//
//        if (!surveyResponseOptional.isEmpty()) {
//
//            SurveyResponse surveyResponse = surveyResponseOptional.get();
//
//            if (surveyResponse.getIsCompleted() || surveyResponse.getStatus() == ResponseStatus.EARLY_COMPLETED) {
//                throw new SurveyErrorException(SurveyErrorCode.ALREADY_SUBMIT);
//            }
//            cellData = (LinkedHashMap<String, Object>) responseService.buildSubmitCellData(survey.getId(), surveyResponse.getId());
//        }
//
//        SurveyDto dto = mapToDto(survey);
//
//        dto.setCellData(cellData);
//        dto.setChannel(channelService.mapToDto(surveyChannel));
//
//        return dto;
//
//    }


    /**
     * 删除问卷
     *
     * @param id
     * @return
     */
    @org.springframework.transaction.annotation.Transactional()
    public Boolean deleteOne(long id) {
        Optional<Survey> object = repository.findById(id);
        if (!object.isPresent()) {
            throw new EntityNotFoundException();
        } else {
            repository.deleteSoft(object.get());
        }
        List<SurveyLottery> lotteryList = surveyLotteryRepository.findBySid(id);
        if (!lotteryList.isEmpty()) {
            for (SurveyLottery lottery : lotteryList) {
                surveyLotteryService.rollbackMoney(lottery);
            }
        }

        surveyEventTrigger.surveyDelete(id);
        return true;
    }


    /**
     * 获取embed
     *
     * @param id
     * @return
     */
    public SurveyEmbedExtDto getEmbed(Long id, Map<String, Object> params, HttpServletRequest request) throws Exception {
        Optional<Survey> surveyOptional = repository.findById(id);
        if (!surveyOptional.isPresent()) {
            throw new EntityNotFoundException();
        }


        CacheApiKey apiKey = apiKeyCacheHelper.get(surveyOptional.get().getOrgId());
        params.forEach((key, v) -> {
            String valueP = Objects.toString(v, null);
            if (valueP != null && valueP.startsWith("@") && valueP.endsWith("@")) {
                try {
                    PropertyUtils.setProperty(params, key, AESUtils.decrypt(apiKey.getSecret(), valueP.substring(1, valueP.length() - 1)));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });

        String externalUserId = Objects.toString(params.get("externalUserId"), null);
        //如果externalUserId 加密了 解密
        if (externalUserId != null && externalUserId.startsWith("@") && externalUserId.endsWith("@")) {
            if (apiKey != null && apiKey.getSecret() != null) {
                externalUserId = AESUtils.decrypt(apiKey.getSecret(), externalUserId.substring(1, externalUserId.length() - 1));
            }
        }

        //如果问卷链接有渠道号 获取渠道配置信息的SurveyEmbed
        Object channelId = params.get("channelId");
        //有clientId 需要查询下该答卷的填答状态
        String clientId = Objects.toString(params.get("clientId"), null);
        String ip = RestUtils.getClientIpAddress(request);
        Boolean isComplete = responseService.getResponseStatusByClientId(surveyOptional.get(), clientId);

        if (channelId == null || !StringUtils.isNumeric(channelId.toString())) {
            SurveyEmbed embed = surveyOptional.get().getEmbed();
            if (embed instanceof SurveyEmbedExtDto) {
                SurveyEmbedExtDto embedExt = (SurveyEmbedExtDto) embed;
                embedExt.setSurveyStatus(surveyOptional.get().getStatus());
                embedExt.setIsComplete(isComplete);
                return embedExt;
            } else {
                return new SurveyEmbedExtDto();
            }
        }

        SurveyChannel surveyChannel = channelService.requireChannel(Long.parseLong(channelId.toString()));
        if (!Arrays.asList(ChannelType.INJECT_WEB, ChannelType.MP, ChannelType.APP).contains(surveyChannel.getType()) || !surveyChannel.getSid().equals(id))
            throw new EntityNotFoundException();
        //回收数量满了、设备限制、ip限制 不弹出问卷 免打扰  false:表示可以弹出问卷,true:表示问卷免打扰
        Boolean doNotDisturb = responseService.checkResponseStatus(surveyOptional.get(), surveyChannel, isComplete, ip);

        SurveyEmbedExtDto embed = new SurveyEmbedExtDto();
        if (!surveyChannel.getConfigure().isEmpty()) {
            embed = JsonHelper.toObject(surveyChannel.getConfigure(), SurveyEmbedExtDto.class);
            embed.setSurveyStatus(surveyOptional.get().getStatus());
        }
        embed.setIsComplete(isComplete);
        embed.setChannelStatus(surveyChannel.getStatus());
        // 触发限制不写入免扰记录
        if (!doNotDisturb) {
            //用户是否触发免打扰
            doNotDisturb = surveyDisturbRuleService.isDisturbSurvey(surveyOptional.get().getId(), surveyOptional.get().getOrgId(), doNotDisturb, externalUserId, surveyChannel);
            if (doNotDisturb) {
                //增加免打扰记录
                surveyDisturbRuleService.addSurveyDisturbRecord(surveyOptional.get().getId(), surveyOptional.get().getOrgId(), surveyChannel.getId(), externalUserId, null);
            }
        }

        embed.setDoNotDisturb(doNotDisturb);
        embed.setCloseBtnColor(surveyOptional.get().getStyle().getCloseBtnColor());

        beforeLimiterChannel.WhiteBlackListCheck(embed, externalUserId, Objects.toString(params.get("externalCompanyId")), params);
        return embed;
    }

    /**
     * 发布问卷
     * <p>
     * 如果开启了配额需要做配额计算
     *
     * @param sid
     * @return
     */
    public SurveyProcessDto publish(Long sid, boolean manualAuditIfFail) {
        Survey survey = requireWithFilter(sid);
        SurveyStatus.checkToCollecting(survey.getStatus());
        surveyContentAuditService.checkContentAudit(survey, manualAuditIfFail);

        final Boolean surveyVerify = organizationConfigService.getOrDefaultConfig(OrganizationConfigType.surveyVerify).getSurveyVerify();
        //后台问卷审核开启且问卷没有审核通过不能发布
        if (surveyVerify && !SurveyStatus.APPROVAL.equals(survey.getStatus())) {
            throw new BadRequestException("管理员已开启问卷审核流程,请先提交问卷审核！");
        }

        //开启了配额 并且 配额不为空 且 配额状态是等待同步中 且 有效答卷数量大于0
        SurveyProcessDto surveyProcessDto = quotaService.calculateQuota(survey, true, false);
        if (surveyProcessDto != null) {
            return surveyProcessDto;
        } else {
            surveyProcessDto = new SurveyProcessDto();
        }

        survey.setStatus(SurveyStatus.COLLECTING);
        survey.setQuotaStatus(SurveyQuotaStatus.CALCULATE_INIT);
        save(survey);
        surveyProcessDto.setStatus(SurveyStatus.COLLECTING);
        surveyEventTrigger.surveyPublish(survey.getId());
        return surveyProcessDto;
    }

    /**
     * 暂停问卷
     *
     * @param sid
     * @return
     */
    public SurveyDto stop(Long sid) {
        Survey survey = requireWithFilter(sid);
        //只有启用状态才能停止
        if (!SurveyStatus.COLLECTING.equals(survey.getStatus())) {
            throw new BadRequestException();
        }
        survey.setStatus(SurveyStatus.STOPPED);
        survey.setQuotaStatus(SurveyQuotaStatus.CALCULATE_INIT);
        survey = save(survey);
        surveyEventTrigger.surveyStop(survey.getId());
        return mapToDto(survey);
    }

    /**
     * 保存survey对象的修改
     *
     * @param survey
     * @return
     */
    public Survey save(Survey survey) {
        return repository.save(survey);
    }


    /**
     * 删除问卷逻辑
     *
     * @param id
     */
    public void deleteLogic(Long id) {
        logicRepository.deleteById(id);
    }


    public boolean changeGroup(Long targetGroupId, List<Long> surveyIds) {
        if (targetGroupId != 0) {
            surveyGroupService.checkIsCurrentOrg(surveyGroupService.require(targetGroupId));
        }
        Long userId = TenantContext.getCurrentUserId();
        List<Survey> list = repository.findAllById(surveyIds);
        list.forEach(i -> {
            checkIsCurrentOrg(i);
            if (userId == null || !userId.equals(i.getUserId())) {
                throw new BadRequestException();
            }
            i.setGroupId(targetGroupId);
        });
        repository.saveAll(list);
        return true;
    }

//    /**
//     * 答卷回收数量限制
//     *
//     * @param survey
//     */
//    public void responseLimit(Survey survey) {
//        if (!survey.getEnableResponseLimit()) {
//            return;
//        }
//        Long numOfResponse = responseService.numOfResponse(survey, ResponseStatus.FINAL_SUBMIT);
//        if (survey.getResponseAmount() != null && survey.getResponseAmount() <= numOfResponse)
//            throw new CommonException(SurveyErrorCode.CHANNEL_TYPE_COMPLETE.getMessage());
//    }

//    /**
//     * 问卷答题时间限制
//     *
//     * @param survey
//     */
//    public void submitDateLimit(Survey survey) {
//        if (!survey.getEnableDateLimit()) {
//            return;
//        }
//        String msg = survey.getStartTime() != null && new Date().before(survey.getStartTime()) ? "当前问卷的答题时间还未开始" :
//                survey.getEndTime() != null && new Date().after(survey.getEndTime()) ? "当前问卷的答题时间已过期" : null;
//
//        if (msg != null) throw new DateLimitException(msg);
//    }

    /**
     * 返回simple问卷内容
     *
     * @return
     */
    public List<SimpleSurveyDto> findSimpleSurvey(String type) {
        Boolean surveyVerify = organizationConfigService.getOrDefaultConfig(OrganizationConfigType.surveyVerify).getSurveyVerify();
        if ("".equals(type)) {
            return new ArrayList<>();
        }

        //获取问卷id和标题
        List<SimpleSurvey> simpleSurveys = repository.findAllBy(Sort.by("modifyTime").descending());

        //保存问卷id
        List<Long> surveyIds = new ArrayList<>();
        //所有问卷
        List<Survey> surveys = new ArrayList<>();
        simpleSurveys.forEach(x -> {
            surveyIds.add(x.getId());

            Survey survey = new Survey();
            survey.setId(x.getId());
            surveys.add(survey);
        });

        //查询问题
        List<Integer> types = new ArrayList<>();
        //all不过滤题型
        if (!"all".equals(type) && type != null) {
            try {
                types = Arrays.asList(type.split(",")).stream().map(Integer::parseInt).collect(Collectors.toList());
            } catch (Exception e) {
                log.error("type格式不正确：{}", type);
                return new ArrayList<>();
            }
        }
        List<SurveyQuestion> questionsList = surveyQuestionRepository.findBySurveyIn(surveys);
        //List<SurveyQuestion> questionGroupSort = QuestionsUtils.questionsFilterGroup(questionsList);
        types.add(22);
        List<Integer> finalTypes = types;
        List<SurveyQuestion> questionResult = questionsList.stream().filter(q -> finalTypes.contains(q.getType().ordinal())).collect(Collectors.toList());

        //保存问题id
        List<Long> questioIds = new ArrayList<>();
        questionResult.forEach(x -> questioIds.add(x.getId()));

        //查询simple选项
        List<Map<String, Object>> simpleQuestionItemResult = surveyQuestionItemRepository.findAllSimpleQuestionItem(questioIds);

        //使用问题id分组
        Map<Long, List<Map<String, Object>>> items = simpleQuestionItemResult.stream().collect(Collectors.groupingBy(x -> Long.parseLong(x.get("q_id").toString())));

        //单选题、评价题问题平均分，Map<qid,平均分>
//        final List<Integer> questionTypes = List.of(3, 25);
//        List<Map<Long, Object>> questionAvgScore = responseCellRepository.findAllQuestionAvgScore(surveyIds, questionTypes);
//        Map<Long, List<Map<Long, Object>>> scoreMap = questionAvgScore.stream().collect(Collectors.groupingBy(x -> Long.parseLong(x.get("q_id").toString())));

        //保存问题和选项
        List<SimpleSurveyQuestionDto> questionDtos = new ArrayList<>();
        questionResult.stream().forEach(q -> {
            SimpleSurveyQuestionDto dto = new SimpleSurveyQuestionDto();
            dto.setId(q.getId());
            dto.setSid(q.getSurvey().getId());
            dto.setTitle(q.getTitle());
            dto.setType(q.getType().name());
            dto.setSequence(q.getSequence());
            dto.setGroupCode(q.getGroupCode());
            dto.setName(q.getName());
            dto.setCode(q.getCode());
            //dto.setIsScore((Boolean) q.get("is_score"));
            Boolean isScore = q.getIsScore();
            Long qid = q.getId();
//            if (scoreMap.containsKey(qid))
//                //问题平均分,未打开选项分值等于0分
//                dto.setAvgScore(isScore ? Double.parseDouble(scoreMap.get(qid).get(0).get("avgScore").toString()) : 0.00);
            List<Map<String, Object>> list = items.get(q.getId());
            dto.setQuestionItems(list);
            questionDtos.add(dto);
        });
        //返回结果
        List<SimpleSurveyDto> resultList = new ArrayList<>();

        //问卷id分组
        Map<Long, List<SimpleSurveyQuestionDto>> questions = questionDtos.stream().collect(Collectors.groupingBy(SimpleSurveyQuestionDto::getSid));
        //保存问题到问卷中
        simpleSurveys.stream().forEach(q -> {
            SimpleSurveyDto simpleSurveyDto = new SimpleSurveyDto();
            simpleSurveyDto.setId(q.getId());
            simpleSurveyDto.setTitle(q.getTitle());
            simpleSurveyDto.setStatus(getStatus(q, surveyVerify));
            List<SimpleSurveyQuestionDto> dtos = questions.get(q.getId());
            if (dtos != null) {
                //题目排序
                dtos = questionsDtoFilterGroup(dtos);
                dtos = dtos.stream().filter(x -> !QuestionType.GROUP.name().equals(x.getType())).collect(Collectors.toList());
            }

            if ("all".equals(type) || type == null) {
                //等于null显示空数组[]
                dtos = dtos == null ? new ArrayList<>() : dtos;
                simpleSurveyDto.setFilterQuestions(dtos);
                resultList.add(simpleSurveyDto);
            } else {
                //问卷下没有问题不返回
                if (dtos != null) {
                    simpleSurveyDto.setFilterQuestions(dtos);
                    resultList.add(simpleSurveyDto);
                }
            }
        });
        return resultList;
    }


    public List<SurveyWithGroupTreeDto> findSimpleSurveyWithGroupTree(String type) {
        //我的审核
        if ("audit".equals(type)) {
            return findByVerify();
        }
        List<SurveyWithGroupTreeDto> simpleSurveyResult = new ArrayList<>();
        List<Long> excludeSurveyId = new ArrayList<>();
        //文件夹
        List<SurveyGroup> surveyGroups = scopeQuery(SurveyGroup.class, getStrategyType(type), () -> surveyGroupRepository.findAll(Sort.by("modifyTime").descending()));
        surveyGroups.forEach(x -> excludeSurveyId.add(x.getId()));
        List<SimpleSurveyWithGroup> surveys = null;
        if ("share".equals(type)) {
            //与我分享的数据需要查询文件夹下的问卷
            surveys = scopeQuery(Survey.class, EntityScopeStrategyType.GROUP_CORPORATION, () -> repository.findBy(Sort.by("modifyTime").descending()));
        } else {
            if ("all".equals(type) && !TenantContext.getCurrentIsAdmin()) {
                //我的问卷、与我共享下的所有问卷，包括文件夹下
                surveys = scopeQuery(Survey.class, EntityScopeStrategyType.OWNER_GROUP_CORPORATION, () -> repository.findBy(Sort.by("modifyTime").descending()));
            } else {
                surveys = scopeQuery(Survey.class, getStrategyType(type), () -> repository.findBy(Sort.by("modifyTime").descending()));
            }
        }
        Map<Long, List<SimpleSurveyWithGroup>> surveyMap = surveys.stream().filter(x -> x.getGroupId() != null).collect(Collectors.groupingBy(x -> x.getGroupId()));
        //记录文件夹和问卷
        surveyGroups.stream().forEach(x -> {
            SurveyWithGroupTreeDto dto = new SurveyWithGroupTreeDto();
            dto.setId(x.getId());
            dto.setTitle(x.getTitle());
            dto.setItemType(2);
            List<SimpleSurveyWithGroup> simpleSurveyWithGroups = surveyMap.get(x.getId());
            dto.setSurveyList(simpleSurveyWithGroups == null ? List.of() : simpleSurveyWithGroups);
            simpleSurveyResult.add(dto);
        });
        //记录问卷
        surveys.stream().filter(x -> !excludeSurveyId.contains(x.getGroupId())).forEach(x -> {
            SurveyWithGroupTreeDto dto = new SurveyWithGroupTreeDto();
            dto.setId(x.getId());
            dto.setTitle(x.getTitle());
            simpleSurveyResult.add(dto);
        });
        return simpleSurveyResult;
    }

    /**
     * 我的审核
     *
     * @param
     * @return
     */
    private List<SurveyWithGroupTreeDto> findByVerify() {
        List<SurveyWithGroupTreeDto> simpleSurveyResult = new ArrayList<>();

        final OrgConfigDto orDefaultConfig = organizationConfigService.getOrDefaultConfig(OrganizationConfigType.surveyVerify);
        if (!orDefaultConfig.getSurveyVerify()) {
            throw new BadRequestException("管理员已取消问卷审核流程");
        }
        // 非管理员，则设置为 邀请协作 查询策略
        EntityScopeStrategyTypes strategyType = EntityScopeStrategyTypes.getInstance("SURVEY", "surveyVerifyFilter");
        TenantContext.addCustomEntityScopeStrategy(Survey.class, strategyType);
        List<Survey> surveyList = repository.findAll();
        TenantContext.clearCustomEntityScopeStrategy(Survey.class);
        surveyList.forEach(x -> {
            SurveyWithGroupTreeDto dto = new SurveyWithGroupTreeDto();
            dto.setId(x.getId());
            dto.setTitle(x.getTitle());
            simpleSurveyResult.add(dto);
        });
        return simpleSurveyResult;
    }

    public EntityScopeStrategyTypes getStrategyType(String type) {
        switch (type) {
            case "all":
                if (TenantContext.getCurrentIsAdmin()) {
                    return EntityScopeStrategyType.ORGANIZATION;
                } else {
                    return EntityScopeStrategyType.OWNER_CORPORATION;
                }
            case "owner":
                return EntityScopeStrategyType.OWNER;
            case "share":
                return EntityScopeStrategyType.CORPORATION;
            default:
                throw new BadRequestException();
        }
    }

    public <Q> Q scopeQuery(Class clazz, EntityScopeStrategyTypes scope, Supplier<Q> query) {
        TenantContext.addCustomEntityScopeStrategy(clazz, scope);
        Q result = query.get();
        TenantContext.clearCustomEntityScopeStrategy(clazz);
        return result;
    }

    /**
     * 根据问卷审核配置获取问卷状态
     *
     * @param survey
     * @param surveyVerify
     * @return
     */
    public SurveyStatus getStatus(SimpleSurvey survey, Boolean surveyVerify) {
        return survey.getStatus();
//        if (surveyVerify) {
//            return survey.getStatus();
//        } else {
//            if (survey.getStatus().equals(SurveyStatus.COLLECTING)) {
//                return SurveyStatus.COLLECTING;
//            } else {
//                return SurveyStatus.STOPPED;
//            }
//        }
    }

    private List<SimpleSurveyQuestionDto> questionsDtoFilterGroup(List<SimpleSurveyQuestionDto> questions) {
        List<SimpleSurveyQuestionDto> questionsOrderList = new ArrayList<>();
        if (questions.stream().noneMatch(q -> QuestionType.GROUP.name().equals(q.getType()))) {
            return questions.stream().sorted(Comparator.comparing(SimpleSurveyQuestionDto::getSequence)).collect(Collectors.toList());
        }

        questions.stream()
                .filter(q -> QuestionType.GROUP.name().equals(q.getType()))
                .sorted(Comparator.comparing(SimpleSurveyQuestionDto::getSequence))
                .forEach(
                        group -> questions.stream()
                                .filter(q -> group.getName().equals(q.getGroupCode()))
                                .sorted(Comparator.comparing(SimpleSurveyQuestionDto::getSequence))
                                .forEach(questionsOrderList::add)
                );
        return questionsOrderList;
    }

    /**
     * 判断是否有创建问卷的额度
     */
    public void checkSurveyLimit() {
        Organization org = organizationService.requireCurrent();
        int limit = Optional.ofNullable(organizationService.parseOrgOptionalLimit(org).getSurveysLimit()).orElse(0);
        String sql = String.format("select count(1) from survey where org_id = %d and deleted = %d ", org.getId(), 0);
        int exist = Optional.ofNullable(jdbcTemplate.queryForObject(sql, Integer.class)).orElse(0);
        if (limit - exist <= 0) {
            throw new OverLimitException("问卷数量已达到满额，如需添加新问卷，请升级版本。");
        }
    }

    /**
     * 文本导入问卷
     *
     * @param importDto
     * @return
     */
    public SurveyDto importSurvey(SurveyImportDto importDto) {
        Assert.notNull(importDto.getTitle(), "问卷标题不能为空");

        Long userId = TenantContext.getCurrentUserId();
        Long orgId = TenantContext.getCurrentTenant();

        Survey survey = new Survey();
        survey.setTitle(importDto.getTitle());
        survey.setRealTitle(importDto.getRealTitle());
        survey.setWelcomingRemark(importDto.getWelcomingRemark());
        survey.setConcludingRemark(importDto.getConcludingRemark());
        survey.setAbnormalConcludingRemark(importDto.getAbnormalConcludingRemark());
        survey.setUserId(userId);
        survey.setOrgId(orgId);
        if (importDto.getGroupId() != null && importDto.getGroupId() > 0l) survey.setGroupId(importDto.getGroupId());

        Optional.ofNullable(importDto.getQuestions()).ifPresent(l -> {
            l.forEach(q -> {
                q.setSurvey(survey);
                Optional.ofNullable(q.getItems()).ifPresent(ll -> {
                    ll.forEach(i -> {
                        i.setQuestion(q);
                    });
                    q.setItems(ll);
                });
                Optional.ofNullable(q.getColumns()).ifPresent(lc -> {
                    lc.forEach(c -> {
                        c.setQuestion(q);
                    });
                    q.setColumns(lc);
                });
            });
            survey.setQuestions(l);
        });
        repository.save(survey);
        return mapToDto(survey);
    }

    public void export(Long sid, HttpServletRequest request, HttpServletResponse response) {
        StringBufferHelper sb = new StringBufferHelper(new StringBuffer());
        try {
            Survey survey = repository.findById(sid).orElseThrow(() -> new BadRequestException("抱歉，您访问的问卷不存在"));

            List<SurveyQuestion> groups = survey.getQuestions().stream().filter(x -> x.getType().equals(QuestionType.GROUP)).collect(Collectors.toList());
            Map<String, List<SurveyQuestion>> questionMap = survey.getQuestions().stream().filter(x -> x.getGroupCode() != null).collect(Collectors.groupingBy(BaseQuestion::getGroupCode));

            sb.appendSurveyTitle(survey.getTitle());

            sb.appendNewLine(survey.getWelcomingRemark() + "[欢迎语]");
            for (SurveyQuestion group : groups) {
                sb.append_h(group.getTitle());
                for (SurveyQuestion question : questionMap.get(group.getName())) {
                    IDownloadSurvey iDownloadSurvey = downloadSurveyMap.get(question.getType());
                    if (ObjectUtils.isNotEmpty(iDownloadSurvey)) {
                        sb.appendNewLine(iDownloadSurvey.buildQuestion(question));
                    } else {
                        itemQuestion(sb, question);
                    }
                    sb.append("<br/>");
                }
            }
            sb.append_h("结束页");

            sb.append_p(survey.getConcludingRemark() + "[正常完成]");

            sb.append_p(survey.getAbnormalConcludingRemark() + "[提前完成]");

            //SimpleDateFormat sdf = new SimpleDateFormat("MMddHHmmss");
            exportHtmlToWord(request, response, sb.toString(), survey.getTitle());
            log.info("export word success");
        } catch (Exception e1) {
            log.error("export word fail", e1.getMessage());
        }

    }

    private void itemQuestion(StringBufferHelper sb, SurveyQuestion question) {
        sb.appendNewLine(question.getCode() + "." + question.getTitle() + "[" + question.getType().getText() + "]");
        for (int i = 0; i < question.getItems().size(); i++) {
            //选项
            List<SurveyQuestionItem> items = question.getItems();
            sb.appendNewLine(+(i + 1) + "." + items.get(i).getText());
        }
    }

    public static void exportHtmlToWord(HttpServletRequest request, HttpServletResponse response, String content, String fileName) throws Exception {
        //图片转为base64方法
        //String imagebase64 = getImageStr(imagePath);
        // 拼接html格式内容
        StringBuffer sbf = new StringBuffer();
        // 这里拼接一下html标签,便于word文档能够识别
        sbf.append("<html " +
                "xmlns:v=\"urn:schemas-microsoft-com:vml\" xmlns:o=\"urn:schemas-microsoft-com:office:office\" xmlns:w=\"urn:schemas-microsoft-com:office:word\" xmlns:m=\"http://schemas.microsoft.com/office/2004/12/omml\" xmlns=\"http://www.w3.org/TR/REC-html40\"" + //将版式从web版式改成页面试图
                ">");
        sbf.append("<head>" +
                "<!--[if gte mso 9]><xml><w:WordDocument><w:View>Print</w:View><w:TrackMoves>false</w:TrackMoves><w:TrackFormatting/><w:ValidateAgainstSchemas/><w:SaveIfXMLInvalid>false</w:SaveIfXMLInvalid><w:IgnoreMixedContent>false</w:IgnoreMixedContent><w:AlwaysShowPlaceholderText>false</w:AlwaysShowPlaceholderText><w:DoNotPromoteQF/><w:LidThemeOther>EN-US</w:LidThemeOther><w:LidThemeAsian>ZH-CN</w:LidThemeAsian><w:LidThemeComplexScript>X-NONE</w:LidThemeComplexScript><w:Compatibility><w:BreakWrappedTables/><w:SnapToGridInCell/><w:WrapTextWithPunct/><w:UseAsianBreakRules/><w:DontGrowAutofit/><w:SplitPgBreakAndParaMark/><w:DontVertAlignCellWithSp/><w:DontBreakConstrainedForcedTables/><w:DontVertAlignInTxbx/><w:Word11KerningPairs/><w:CachedColBalance/><w:UseFELayout/></w:Compatibility><w:BrowserLevel>MicrosoftInternetExplorer4</w:BrowserLevel><m:mathPr><m:mathFont m:val=\"Cambria Math\"/><m:brkBin m:val=\"before\"/><m:brkBinSub m:val=\"--\"/><m:smallFrac m:val=\"off\"/><m:dispDef/><m:lMargin m:val=\"0\"/> <m:rMargin m:val=\"0\"/><m:defJc m:val=\"centerGroup\"/><m:wrapIndent m:val=\"1440\"/><m:intLim m:val=\"subSup\"/><m:naryLim m:val=\"undOvr\"/></m:mathPr></w:WordDocument></xml><![endif]-->" +
                "</head>");
        sbf.append("<body>");
        // 富文本内容
        sbf.append(content);
        sbf.append("</body></html>");

        // 必须要设置编码,避免中文就会乱码
        byte[] b = sbf.toString().getBytes("GBK");
        // 将字节数组包装到流中
        ByteArrayInputStream bais = new ByteArrayInputStream(b);
        POIFSFileSystem poifs = new POIFSFileSystem();
        DirectoryEntry directory = poifs.getRoot();
        // 这代码不能省略,否则导出乱码。
        DocumentEntry documentEntry = directory.createDocument("WordDocument", bais);
        //输出文件
        request.setCharacterEncoding("utf-8");
        // 导出word格式
        response.setContentType("application/msword");
        response.addHeader("Content-Disposition", "attachment;filename=" +
                new String(fileName.getBytes("GB2312"), "iso8859-1") + ".doc");
        ServletOutputStream ostream = response.getOutputStream();
        poifs.writeFilesystem(ostream);
        bais.close();
        ostream.close();
    }

    public SurveyLinkDto getOrCreateLink(Survey entity) {
        Link link = null;
        if (entity.getLinkId() != null && entity.getLinkId() > 0) {
            link = linkService.getLink(entity.getLinkId());
        }
        if (link == null) {
            link = linkService.createLink(entity.getId(), null);
            entity.setLinkId(link.getId());
            repository.save(entity);
        }
        SurveyLinkDto r = new SurveyLinkDto();
        r.setOriginUrl(linkService.toOriginUrl(link));
        r.setShortUrl(linkService.toShortUrl(link));
        r.setShortCode(linkService.toShortCode(link));
        return r;
    }

    /**
     * 统计调研家社区问卷题目数量
     * 矩阵类题型（如矩阵单选、矩阵量表、滑动条），如果有多个测量项，题目数按多题计算（即一个测量项算一题）
     * 备注说明题型不参与题目数量计算（注意分页是不算题型的，也不参与题目数计算）
     * 其他题型（不一一枚举）按一题计算
     *
     * @return
     */
    public int countQuestion(Long surveyId) {
        AtomicInteger count = new AtomicInteger();
        if (surveyId == null || surveyId == null) return 0;
        Survey survey = requireSurvey(surveyId);
        if (survey.getQuestions() == null || survey.getQuestions().size() == 0) return 0;
        survey.getQuestions().forEach(question -> {
            switch (question.getType()) {
                case MATRIX_SCORE:
                case MATRIX_CHOICE:
                case MATRIX_SLIDER:
                    count.addAndGet(question.getItems().size());
                    break;
                case GROUP:
                case EMPTY:
                case SEPARATOR:
                case MARK:
                    break;
                default:
                    count.incrementAndGet();
            }
        });
        return count.get();
    }

    @Transactional
    public boolean changeOwner(long surveyId, long targetUserId) {
        Long userId = TenantContext.getCurrentUserId();
        Survey survey = requireWithFilter(surveyId);
        Long currentUserId = survey.getUserId();
        if (currentUserId != null && currentUserId.equals(userId)) {
            User fromUser = userService.requireWithFilter(userId);
            User targetUser = userService.requireWithFilter(targetUserId);
            survey.setUserId(targetUserId);
            survey.setGroupId(0l);
            repository.save(survey);
            Map<String, Object> params = new HashMap<>();
            params.put("surveyId", survey.getId());
            params.put("surveyTitle", survey.getTitle());
            params.put("fromUsername", fromUser.getTruename());
            systemNotificationService.doSendInboxMessage("cem", targetUser, "survey-change-owner", params);
            return true;
        } else {
            throw new BadRequestException("当前用户不可以操作");
        }
    }

    public void updateResponseNum(long surveyId, int add) {
        String sql = String.format("update survey set response_finish_num = GREATEST(0, response_finish_num + (%s)) where id = %s", add, surveyId);
        jdbcTemplate.update(sql);
    }

    /**
     * 校验token获取到的orgId和surveyId是否一致
     *
     * @param surveyId
     */
    public void checkSurveyByOrgId(Long surveyId) {
        //根据surveyId + 登录的orgId 检验
        if (!repository.existsByIdAndOrgId(surveyId, TenantContext.getCurrentTenant())) {
            throw new SurveyErrorException(SurveyErrorCode.LOGIN_REQUIRE);
        }
    }

    @Async
    @Transactional
    public void resetSurveyOwner(Long orgId, List<Long> surveyIds, Long targetUserId) {
        if (orgId != null && targetUserId != null && CollectionUtils.isNotEmpty(surveyIds)) {
            repository.updateUserIdByOrgIdAndIdIn(targetUserId, orgId, surveyIds);
        }
    }
}

























