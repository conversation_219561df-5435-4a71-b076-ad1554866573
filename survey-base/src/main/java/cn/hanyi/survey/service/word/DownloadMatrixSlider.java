package cn.hanyi.survey.service.word;

import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.core.entity.SurveyQuestionItem;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/25 11:28:27
 */
@Component
public class DownloadMatrixSlider implements IDownloadSurvey{

    @Override
    public QuestionType type() {
        return QuestionType.MATRIX_SLIDER;
    }

    /*
           完全不可能 0-100 非常有可能 不适用
            1.学习
            2.工作
            3.社交
            4.娱乐
     */
    @Override
    public String buildQuestion(SurveyQuestion question) {
        StringBuffer res = new StringBuffer();
        res.append(question.getCode()).append(".").append(question.getTitle()).append("["+question.getType().getText()+"]").append("<br/>");

        List<String> labels = question.getLabels();
        res.append(labels.get(0)).append(question.getMin()+"-"+question.getMax()).append(labels.get(1));
        if(question.getInapplicable()){
            res.append(" 不适用");
        }
        List<SurveyQuestionItem> items = question.getItems();
        for (int i = 0; i < items.size(); i++) {
            res.append("<br/>").append(i+1).append(".").append(items.get(i).getText());
        }
        return res.toString();
    }
}















