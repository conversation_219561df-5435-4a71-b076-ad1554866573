package cn.hanyi.survey.service.word;

import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import org.befun.auth.dto.DepartmentTreeDto;
import org.befun.auth.service.DepartmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/3/28 11:28:27
 */
@Component
public class DownloadOrganize implements IDownloadSurvey {

    @Autowired
    private DepartmentService departmentService;

    @Override
    public QuestionType type() {
        return QuestionType.ORGANIZE;
    }

    /*
            广东/深圳/万象城 1 店
            广东/佛山/万达 1 店
     */
    @Override
    public String buildQuestion(SurveyQuestion question) {
        DepartmentTreeDto organize = departmentService.treeByOrg(question.getSurvey().getOrgId());
        StringBuffer res = new StringBuffer();
        res.append(question.getCode()).append(".").append(question.getTitle()).append("["+question.getType().getText()+"]").append("<br/>");
        return res.append(getOrganize(organize)).toString();
    }

    public StringBuffer getOrganize(DepartmentTreeDto root) {
        StringBuffer result = new StringBuffer();
        if (root == null) return result;
        getPath(root, result, root.getTitle());
        return result;
    }

    public void getPath(DepartmentTreeDto root, StringBuffer result, String str) {
        if (root.getSubDepartments().size() <= 0) {
            result.append(str).append("<br/>");
            return;
        }
        if (root.getSubDepartments().size() > 0) {
            for (DepartmentTreeDto dto : root.getSubDepartments()) {
                getPath(dto, result, str + "/" + dto.getTitle());
            }
        }

    }
}















