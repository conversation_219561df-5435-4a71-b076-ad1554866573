package cn.hanyi.survey.service.channel;

import cn.hanyi.cem.core.dto.task.progress.TaskPageableDto;
import cn.hanyi.common.file.storage.FileInfo;
import cn.hanyi.common.file.storage.FileStorageService;
import cn.hanyi.ctm.entity.Customer;
import cn.hanyi.survey.core.constant.channel.ChannelType;
import cn.hanyi.survey.core.dto.channel.ChannelSelectCustomerDto;
import cn.hanyi.survey.core.dto.task.UploadFileDto;
import com.alibaba.excel.EasyExcel;
import com.glaforge.i18n.io.CharsetToolkit;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.io.input.BOMInputStream;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.exception.BadRequestException;
import org.befun.core.utils.JsonHelper;
import org.befun.core.utils.RegHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;

@Slf4j
@Service
public class ChannelSendFileHelper extends ChannelSendDataHelper {

    @Autowired
    private FileStorageService fileStorageService;

    private static final String nameHeader = "姓名";
    private static final String msgHeader = "手机号码";
    private static final String emailHeader = "邮箱地址";


    @Value("${survey.csv-file-max:1}")
    private Long csvFileMax;

    @Override
    public long countCustomers(Long orgId, Long userId, ChannelType channelType, ChannelSelectCustomerDto select) {
        if (StringUtils.isEmpty(select.getFileUrl())) {
            return 0;
        }
        List<Customer> customers = new ArrayList<>();
        parseExcelFile(false, channelType, select.getFileUrl(), new HashMap<>(), c -> Optional.ofNullable(c).ifPresent(customers::add));
        return customers.size();
    }

    @Override
    public void foreachCustomers(Long orgId, Long userId, ChannelType channelType, int pageSize, ChannelSelectCustomerDto select, Consumer<List<Customer>> consumerCustomers) {
        if (StringUtils.isNotEmpty(select.getFileUrl())) {
            List<Customer> allCustomers = new ArrayList<>();
            parseExcelFile(false, channelType, select.getFileUrl(), new HashMap<>(), allCustomers::add);
            pageable(allCustomers.size(), pageSize, (page, size) -> TaskPageableDto.splitPageList(allCustomers, page, size), consumerCustomers);
        }
    }

    public ResourceResponseDto<UploadFileDto> uploadSurvey0(ChannelType channelType, MultipartFile file) {
        if (file.getSize() > (csvFileMax * 1024 * 1024)) {
            throw new BadRequestException("文件必须少于1M");
        }
        FileInfo fileInfo = fileStorageService.of(file)
                .setObjectId("0")
                .upload();
        try {
            LinkedHashMap<String, String> headers = new LinkedHashMap<>();
            List<LinkedHashMap<String, Object>> customers = validFile(channelType, fileInfo.getUrl(), headers);
            UploadFileDto dto = new UploadFileDto();
            dto.setUrl(fileInfo.getUrl());
            dto.setCustomers(customers);
            dto.setHeaders(headers);
            return new ResourceResponseDto<>(dto);
        } catch (BadRequestException e) {
            ResourceResponseDto<UploadFileDto> dto = new ResourceResponseDto<>();
            dto.setCode(e.getCode());
            dto.setMessage(e.getMessage());
            return dto;
        }
    }

    public List<LinkedHashMap<String, Object>> validFile(ChannelType channelType, String fileUrl, LinkedHashMap<String, String> headers) {
        List<LinkedHashMap<String, Object>> customers = new ArrayList<>();
        AtomicInteger sort = new AtomicInteger(1);
        parseExcelFile(true, channelType, fileUrl, headers, i -> Optional.ofNullable(i).ifPresent(c -> {
            LinkedHashMap<String, Object> customer = new LinkedHashMap<>();
            Map<String, Object> cMap = JsonHelper.toMap(c);
            customer.put("sort", sort.getAndIncrement());
            headers.forEach((k, v) -> {
                customer.put(k, cMap.get(k));
            });
            customer.putAll(c.getContentParams());
            customers.add(customer);
        }));

        return customers;
    }

    @Validated
    private void parseExcelFile(boolean validate, ChannelType channelType, String fileUrl, Map<String, String> headers, Consumer<Customer> consumer) {
        if (StringUtils.isNotEmpty(fileUrl)) {
            try (InputStream is = new ByteArrayInputStream(fileStorageService.download(fileUrl).bytes())) {
                List<Map<Integer, String>> headersList = new ArrayList<>();
                Field[] customerFields = Customer.class.getDeclaredFields();
                EasyExcel.read(is, new SendFileListener(headersList, channelType, dataList -> {
                    if (headersList.size() < 2
                            || dataList.isEmpty()
                            || headersList.size() != dataList.get(0).size()
                            || !nameHeader.equals(headersList.get(0).get(0))
                            || !(channelType == ChannelType.PHONE_MSG ? msgHeader.equals(headersList.get(1).get(1)) : emailHeader.equals(headersList.get(1).get(1)))
                    ) {
                        if (validate) {
                            throw new BadRequestException("上传文件列名字段错误，请检查！");
                        } else {
                            return;
                        }
                    }

                    log.info("上传客户数:{}", dataList.size());
                    headers.put("username", "姓名");
                    if (channelType == ChannelType.PHONE_MSG) {
                        headers.put("mobile", "手机号码");
                    } else if (channelType == ChannelType.EMAIL) {
                        headers.put("email", "邮箱地址");
                    }

                    for (Map<Integer, String> data : dataList) {
                        Customer customer = new Customer();

                        customer.setUsername(data.get(0));
                        if (channelType == ChannelType.PHONE_MSG) {
                            if(!RegHelper.isMobile(data.get(1))){
                                continue;
                            }
                            customer.setMobile(data.get(1));
                        } else if (channelType == ChannelType.EMAIL) {
                            if(!RegHelper.isEmail(data.get(1))){
                                continue;
                            }
                            customer.setEmail(data.get(1));
                        }
                        Map<String, String> params = new HashMap<>();

                        for (int i = 2; i < headersList.size(); i++) {
                            Map<Integer, String> header = headersList.get(i);
                            String key = header.get(i);
                            String value = data.get(i);
                            if (StringUtils.isNotEmpty(key) && StringUtils.isNotEmpty(value)) {
                                params.put(key, value);
                            }

                            if (headers.size() != headersList.size()) {
                                headers.put(key, key);
                            }
                        }

                        params.forEach((k, v) -> {

                            if (Arrays.stream(customerFields).anyMatch(field -> field.getName().equals(k))) {
                                Field field = null;
                                try {
                                    field = customer.getClass().getDeclaredField(k);
                                    field.setAccessible(true);
                                    field.set(customer, v);
                                    field.setAccessible(false);
                                } catch (Exception e) {
                                    log.warn("系统参数:{} 设置失败，跳过", k);
                                }
                            } else {
                                customer.getUrlCustomParams().put(k.replace("_", ""), v);
                                customer.getContentParams().put(k, v);
                            }

                        });
                        consumer.accept(customer);
                    }


                })).sheet().doRead();
            } catch (IOException e) {
                log.error("计算客户数（上传文件）失败：解析文件失败");
            }
        }

    }

    @Deprecated
    private void parseCsvFile(boolean validate, ChannelType channelType, String fileUrl, Map<String, String> headers, Consumer<Customer> consumer) {
        if (StringUtils.isNotEmpty(fileUrl)) {
            try (InputStream is = new ByteArrayInputStream(fileStorageService.download(fileUrl).bytes())) {
                AtomicInteger no = new AtomicInteger();
                AtomicBoolean hasP1 = new AtomicBoolean();
                AtomicBoolean hasP2 = new AtomicBoolean();
                AtomicBoolean hasP3 = new AtomicBoolean();
                MutablePair<Integer, Integer> index = MutablePair.of(-1, -1);// 手机号|邮箱的列数，姓名的列数
                Charset charset = guessCharset(is);
                CSVParser parser = CSVParser.parse(new BOMInputStream(is), charset, CSVFormat.DEFAULT);
                parser.forEach(i -> {
                    List<String> line = i.toList();
                    if (no.get() == 1) {
                        parseHeader(validate, channelType, line, index, headers);
                    }
                    if (no.getAndIncrement() == 0) {
                        parseHeaderExtParams(validate, line, headers, hasP1, hasP2, hasP3);
                        return;
                    }
                    // 没有解析出header，跳过
                    if (index.left == -1 || index.right == -1) {
                        return;
                    }
                    // 此行没有数据，或者数据小于2个，跳过
                    if (line.size() < 2) {
                        return;
                    }
                    // 手机号|邮箱列没有数据，跳过
                    String account = line.get(index.left);
                    if (StringUtils.isEmpty(account)) {
                        return;
                    }
                    Customer customer = new Customer();
                    if (channelType == ChannelType.PHONE_MSG) {
                        customer.setMobile(account);
                    } else if (channelType == ChannelType.EMAIL) {
                        customer.setEmail(account);
                    }
                    customer.setUsername(line.get(index.right));
                    Map<String, String> params = new HashMap<>();
                    String key, value;
                    if (hasP1.get() && line.size() >= 3
                            && StringUtils.isNotEmpty((key = headers.get("p1")))
                            && StringUtils.isNotEmpty((value = line.get(2)))) {
                        params.put(key, value);
                    }
                    if (hasP2.get() && line.size() >= 4
                            && StringUtils.isNotEmpty((key = headers.get("p2")))
                            && StringUtils.isNotEmpty((value = line.get(3)))) {
                        params.put(key, value);
                    }
                    if (hasP3.get() && line.size() >= 5
                            && StringUtils.isNotEmpty((key = headers.get("p3")))
                            && StringUtils.isNotEmpty((value = line.get(4)))) {
                        params.put(key, value);
                    }
                    if (!params.isEmpty()) {
                        customer.setContentParams(params);
                    }
                    consumer.accept(customer);
                });
            } catch (IOException e) {
                log.error("计算客户数（上传文件）失败：解析文件失败");
            }
        }
    }

    private void parseHeaderExtParams(boolean validate,
                                      List<String> line,
                                      Map<String, String> headers,
                                      AtomicBoolean hasP1,
                                      AtomicBoolean hasP2,
                                      AtomicBoolean hasP3) {
        if (line != null && line.size() >= 2) {
            if (line.size() >= 3 && StringUtils.isNotEmpty(line.get(2))) {
                headers.put("p1", line.get(2));
                hasP1.set(true);
                if (line.size() >= 4 && StringUtils.isNotEmpty(line.get(3))) {
                    headers.put("p2", line.get(3));
                    hasP2.set(true);
                    if (line.size() >= 5 && StringUtils.isNotEmpty(line.get(4))) {
                        headers.put("p3", line.get(4));
                        hasP3.set(true);
                    }
                }
            }
            return;
        }
        if (validate) {
            throw new BadRequestException("上传文件列名字段错误，请检查！");
        } else {
            log.warn("上传文件列名字段错误，请检查！line={}", line);
        }
    }

    private void parseHeader(boolean validate,
                             ChannelType channelType,
                             List<String> line,
                             MutablePair<Integer, Integer> index,
                             Map<String, String> headers) {
        String v1, v2;
        if (line != null
                && line.size() >= 2
                && StringUtils.isNotEmpty(v1 = line.get(0))
                && StringUtils.isNotEmpty(v2 = line.get(1))
        ) {
            if (channelType == ChannelType.PHONE_MSG) {
                if (RegHelper.isMobile(v2)) {
                    index.left = 1;
                    index.right = 0;
                } else if (RegHelper.isMobile(v1)) {
                    index.left = 0;
                    index.right = 1;
                }
                if (index.left >= 0 && index.right >= 0) {
                    headers.put("username", "姓名");
                    headers.put("mobile", "手机号码");
                    return;
                }
            } else if (channelType == ChannelType.EMAIL) {
                if (RegHelper.isEmail(v2)) {
                    index.left = 1;
                    index.right = 0;
                } else if (RegHelper.isEmail(v1)) {
                    index.left = 0;
                    index.right = 1;
                }
                if (index.left >= 0 && index.right >= 0) {
                    headers.put("username", "姓名");
                    headers.put("email", "邮箱");
                    return;
                }
            }
        }
        if (validate) {
            throw new BadRequestException("上传文件列名字段错误，请检查！");
        } else {
            log.warn("上传文件列名字段错误，请检查！line={}", line);
        }
    }

    private Charset guessCharset(InputStream is) {
        Charset charset = null;
        try {
            byte[] buffer = new byte[1024];
            if (is.read(buffer) != -1) {
                CharsetToolkit toolkit = new CharsetToolkit(buffer);
                toolkit.setDefaultCharset(Charset.forName("GBK"));
                is.reset();
                charset = toolkit.guessEncoding();
            }
        } catch (Exception e) {
            //ignore
        }
        return charset == null ? StandardCharsets.UTF_8 : charset;
    }

}
