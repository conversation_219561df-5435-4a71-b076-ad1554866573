package cn.hanyi.survey.service;

import cn.hanyi.survey.core.constant.analysis.GptConversationStatus;
import cn.hanyi.survey.core.constant.analysis.GptConversationType;
import cn.hanyi.survey.core.entity.GptConversationHistory;
import cn.hanyi.survey.core.entity.GptConversationHistoryDto;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.repository.GptConversationHistoryRepository;
import cn.hanyi.survey.dto.analysis.ConversationHistoryMessageResponseDto;
import cn.hanyi.survey.dto.analysis.ConversationHistoryResponseDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.service.BaseService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class GptConversationHistoryService extends BaseService<GptConversationHistory, GptConversationHistoryDto, GptConversationHistoryRepository> {

    @Value("${gpt.history.limit:5}")
    private Integer historyLimit;

    public ConversationHistoryResponseDto getHistory(Survey survey) {
        List<GptConversationHistory> reportHistories = repository.findBySurveyAndType(survey, GptConversationType.REPORT, PageRequest.of(0, 1, Sort.by(Sort.Direction.DESC, "sequence")));
        List<GptConversationHistory> chatHistories = repository.findBySurveyAndType(survey, GptConversationType.CHAT, PageRequest.of(0, historyLimit, Sort.by(Sort.Direction.DESC, "sequence")));

        List<ConversationHistoryMessageResponseDto> report = new ArrayList<>();
        List<ConversationHistoryMessageResponseDto> chat = new ArrayList<>();

        reportHistories.stream().map(
                        history -> report.add(new ConversationHistoryMessageResponseDto("AI", history.getOutput(), history.getCreateTime(), history.getStatus())))
                .findFirst();

        // chatHistories根据sequence正序
        chatHistories.sort((o1, o2) -> o1.getSequence().compareTo(o2.getSequence()));

        for (GptConversationHistory history : chatHistories) {
            chat.add(new ConversationHistoryMessageResponseDto(
                    "HUANG", history.getInput(), history.getCreateTime(), GptConversationStatus.FINISHED
            ));
            if (StringUtils.isNotEmpty(history.getOutput()) && history.getStatus().equals(GptConversationStatus.FINISHED)) {
                chat.add(new ConversationHistoryMessageResponseDto(
                        "AI", history.getOutput(), history.getModifyTime(), history.getStatus()
                ));
            }

        }

        return new ConversationHistoryResponseDto(report, chat);

    }


}
