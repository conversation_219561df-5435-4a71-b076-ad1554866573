/*
package cn.hanyi.survey.service.download.ext;

import cn.hanyi.survey.core.constant.question.QuestionRandomType;
import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.core.entity.SurveyQuestionItem;
import cn.hanyi.survey.core.entity.SurveyRandomResult;
import cn.hanyi.survey.core.entity.SurveyResponse;
import cn.hanyi.survey.core.repository.SurveyRandomResultRepository;
import cn.hanyi.survey.service.download.IDownloadExtHelper;
import cn.hanyi.survey.service.download.dto.DownloadColumnRandom;
import cn.hanyi.survey.service.download.dto.DownloadColumnRandomGroup;
import cn.hanyi.survey.service.download.dto.ResponseDownloadContext;
import cn.hanyi.survey.service.download.dto.ResponseExportRandomFile;
import org.apache.commons.collections.CollectionUtils;
import org.befun.core.entity.BaseEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Component
public class DownloadRandomItemResultHelper implements IDownloadExtHelper<ResponseExportRandomFile> {

    @Autowired
    private SurveyRandomResultRepository surveyRandomResultRepository;

    private static List<QuestionType> questionTypes = List.of(QuestionType.SINGLE_CHOICE, QuestionType.COMBOBOX, QuestionType.EVALUATION, QuestionType.SCORE_EVALUATION);


    @Override
    public DownloadExtType extType() {
        return DownloadExtType.random_result;
    }

    @Override
    public ResponseExportRandomFile buildFile(ResponseDownloadContext context) {
        Long randomResult = surveyRandomResultRepository.existsRandomResult(context.getSurveyId());
        if (randomResult != null) {
            ResponseExportRandomFile file = new ResponseExportRandomFile(context.getSafeTitle() + "_随机结果" + context.getDownloadType().getSuffix());
            DownloadColumnRandomGroup randomGroup = new DownloadColumnRandomGroup();
            buildGroup(context, file, randomGroup);
            file.setRandomGroup(randomGroup);
            return file;
        }
        return null;
    }

    //构建每个问题的索引和标题
    private void buildGroup(ResponseDownloadContext context, ResponseExportRandomFile file, DownloadColumnRandomGroup randomGroup) {
        boolean existItem = surveyRandomResultRepository.existsBySurveyIdAndType(context.getSurveyId(), QuestionRandomType.ITEM);
        if (existItem) {
            for (SurveyQuestion question : context.getOriginSortQuestions()) {
                randomGroup.getColumnItemMap().put(question.getId(), new DownloadColumnRandom(file.getRandomSize(), question.getCode() + "_display"));
                file.plusRandomSize();

                //只记录支持随机选项的问题
                if (questionTypes.contains(question.getType())) {
                    List<SurveyQuestionItem> items = question.getItems().stream().
                            sorted(Comparator.comparing(SurveyQuestionItem::getSequence)).collect(Collectors.toList());
                    file.getQuestionItemMap().put(question.getId(), items);
                    for (SurveyQuestionItem item : items) {
                        String title = String.format("%s_%s_%s", question.getCode(), item.getText(), "display");
                        randomGroup.getColumnItemMap().put(item.getId(), new DownloadColumnRandom(file.getRandomItemSize(), title));
                        file.plusItemRandomSize();
                    }
                }
            }
        }
    }

    @Override
    public void download(ResponseDownloadContext context, ResponseExportRandomFile file, List<SurveyResponse> responseList, boolean sequential) {
        List<SurveyRandomResult> records;
        if (sequential) {
            long min = responseList.get(0).getId();
            long max = responseList.get(responseList.size() - 1).getId();
            records = getByRIdRange(context.getSurveyId(), min, max);
        } else {
            List<Long> rIds = responseList.stream().map(BaseEntity::getId).collect(Collectors.toList());
            records = getByRIds(context.getSurveyId(), rIds);
        }
        if (CollectionUtils.isNotEmpty(responseList)) {
            responseList.forEach(i -> fillId(file, i));
            responseList.forEach(i -> fillRandomItemId(file, i));
        }
        if (CollectionUtils.isNotEmpty(records)) {
            records.forEach(i -> fillValue(file, i));
            records.forEach(i -> fillRandomItemValue(file, i));
        }
    }

    private void fillId(ResponseExportRandomFile file, SurveyResponse response) {
        Object[] row = new Object[file.getRandomSize()];
        row[0] = response.getId().toString();
        file.getRandomRowMap().put(response.getId(), row);
    }

    private void fillRandomItemId(ResponseExportRandomFile file, SurveyResponse response) {
        Object[] row = new Object[file.getRandomItemSize()];
        row[0] = response.getId().toString();
        file.getRandomItemRowMap().put(response.getId(), row);
    }

    private void fillValue(ResponseExportRandomFile file, SurveyRandomResult record) {
        if (record.getResponseId() == null && record.getQuestionId() != null) {
            return;
        }
        DownloadColumnRandom columnRandom = file.getRandomGroup().getColumnMap().get(record.getQuestionId());
        if (columnRandom == null) {
            return;
        }
        Object[] row = file.getRandomRowMap().get(record.getResponseId());
        if (row == null) {
            return;
        }
        //答卷行 问题索引对应的值  填充excel表中的值
        row[columnRandom.getIndex()] = columnRandom.getCode(record);
    }

    private void fillRandomItemValue(ResponseExportRandomFile file, SurveyRandomResult record) {
        if (record.getResponseId() == null && record.getQuestionId() != null) {
            return;
        }

        List<SurveyQuestionItem> sortItems = file.getQuestionItemMap().get(record.getQuestionId());
        //每个选项对应的索引和标题
        Optional.ofNullable(sortItems).ifPresent(items -> {
            items.stream().forEach(item -> {
                DownloadColumnRandom columnRandom = file.getRandomGroup().getColumnItemMap().get(item.getId());
                if (columnRandom == null) {
                    return;
                }
                Object[] row = file.getRandomItemRowMap().get(record.getResponseId());
                if (row == null) {
                    return;
                }
                //答卷行 问题索引对应的值  填充excel表中的值
                row[columnRandom.getIndex()] = columnRandom.getItemCode(record, item);
            });
        });
    }

    @Override
    public void copyColumns(ResponseDownloadContext context, ResponseExportRandomFile fileRandom) {
        //临时记录的标题
        List<String> originHeaders = fileRandom.getRandomGroup().originHeaders();
        List<List<String>> formatHeaders = fileRandom.getHeaders();

        //临时保存的每个答卷对应的数据
        Collection<Object[]> originRows = fileRandom.getRandomRowMap().values();
        List<List<Object>> formatRows = fileRandom.getRows();
        //List<Integer> deleteColumns = fileRandom.getRandomGroup().deleteColumns();
        IntStream.range(0, fileRandom.getRandomSize()).forEach(i -> {
            String header = originHeaders.get(i);
            formatHeaders.add(List.of(header));
        });

        List<String> originItemHeaders = fileRandom.getRandomGroup().originItemHeaders();
        List<List<String>> formatItemHeaders = fileRandom.getItemHeader();
        IntStream.range(0, fileRandom.getRandomItemSize()).forEach(i -> {
            String header = originItemHeaders.get(i);
            formatItemHeaders.add(List.of(header));
        });
        //随机答卷的数量
        originRows.forEach(originRow -> {
            List<Object> row = new ArrayList<>();
            //一行的答卷
            IntStream.range(0, fileRandom.getRandomSize()).forEach(i -> {
                Object v = originRow[i];
                row.add(v == null ? 1 : v);
            });
            formatRows.add(row);
        });

        Collection<Object[]> originItemRows = fileRandom.getRandomItemRowMap().values();
        List<List<Object>> formatItemRows = fileRandom.getItemRows();
        originItemRows.forEach(originItemRow -> {
            List<Object> row = new ArrayList<>();
            //一行的答卷
            IntStream.range(0, fileRandom.getRandomItemSize()).forEach(i -> {
                Object v = originItemRow[i];
                row.add(v == null ? 1 : v);
            });
            formatItemRows.add(row);
        });

        fileRandom.getRandomRowMap().clear();
        fileRandom.getRandomItemRowMap().clear();
    }

    private List<SurveyRandomResult> getByRIdRange(Long surveyId, long minRId, long maxRId) {
        return surveyRandomResultRepository.findBySurveyIdAndResponseIdBetween(surveyId, minRId, maxRId);
    }

    private List<SurveyRandomResult> getByRIds(Long surveyId, List<Long> rIds) {
        return surveyRandomResultRepository.findBySurveyIdAndResponseIdIn(surveyId, rIds);
    }
}
*/
