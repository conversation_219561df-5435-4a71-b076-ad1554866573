package cn.hanyi.survey.service.download.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.LinkedHashMap;

@Getter
@Setter
public class ResponseExportBehaviorFile extends ResponseExportFile {

    // 构建行为数据的临时数据
    private int maxPage = 1;
    private final LinkedHashMap<Long, Object[]> behaviorRowMap = new LinkedHashMap<>();

    public ResponseExportBehaviorFile(String fileName) {
        super(fileName);
    }


}
