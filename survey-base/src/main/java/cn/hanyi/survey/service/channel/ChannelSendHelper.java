package cn.hanyi.survey.service.channel;

import cn.hanyi.ctm.dto.SurveyLinkDto;
import cn.hanyi.ctm.dto.TemplateInfoDto;
import cn.hanyi.ctm.dto.customer.CustomerClientIdParamDto;
import cn.hanyi.ctm.entity.Customer;
import cn.hanyi.ctm.entity.ThirdPartyCustomer;
import cn.hanyi.ctm.service.CustomerAnswersService;
import cn.hanyi.ctm.service.CustomerHistoryRecordService;
import cn.hanyi.ctm.service.CustomerMessageService;
import cn.hanyi.ctm.service.CustomerService;
import cn.hanyi.survey.core.constant.ReplyStatus;
import cn.hanyi.survey.core.constant.channel.ChannelResendCondition;
import cn.hanyi.survey.core.constant.channel.ChannelSendStatus;
import cn.hanyi.survey.core.constant.channel.ChannelStatus;
import cn.hanyi.survey.core.constant.channel.ChannelType;
import cn.hanyi.survey.core.constant.survey.SendStatus;
import cn.hanyi.survey.core.dto.SurveySendRecordExtParamDto;
import cn.hanyi.survey.core.dto.channel.ChannelSelectCustomerDto;
import cn.hanyi.survey.core.dto.channel.ChannelSendCustomerDto;
import cn.hanyi.survey.core.dto.channel.CountSendDto;
import cn.hanyi.survey.core.entity.SurveyChannel;
import cn.hanyi.survey.core.entity.SurveySendRecord;
import cn.hanyi.survey.core.projection.SimpleSurvey;
import cn.hanyi.survey.core.repository.SurveyChannelRepository;
import cn.hanyi.survey.core.repository.SurveyRepository;
import cn.hanyi.survey.core.repository.SurveySendRecordRepository;
import cn.hanyi.survey.workertrigger.ISurveyTaskTrigger;
import cn.hanyi.survey.workertrigger.dto.CustomerSendEmailDto;
import cn.hanyi.survey.workertrigger.dto.CustomerSendSmsDto;
import cn.hanyi.survey.workertrigger.dto.CustomerSendWechatDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.constant.UserTaskType;
import org.befun.auth.service.UserTaskService;
import org.befun.core.constant.QueryOperator;
import org.befun.core.dto.query.ResourceQueryCriteria;
import org.befun.core.exception.BadRequestException;
import org.befun.core.exception.BusinessException;
import org.befun.core.exception.EntityNotFoundException;
import org.befun.core.rest.query.GenericSpecification;
import org.befun.core.template.TemplateEngine;
import org.befun.core.utils.DateHelper;
import org.befun.core.utils.EnumHelper;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.entity.Link;
import org.befun.extension.service.LinkService;
import org.befun.extension.service.NativeSqlHelper;
import org.befun.extension.sms.ISmsAccountService;
import org.befun.task.entity.TaskProgress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.hanyi.survey.core.constant.channel.ChannelType.*;

@Slf4j
@Service
public class ChannelSendHelper {

    @Autowired
    private SurveyRepository surveyRepository;
    @Autowired
    private SurveyChannelRepository channelRepository;
    @Autowired
    private SurveySendRecordRepository surveySendRecordRepository;
    @Autowired
    private List<ChannelSendDataHelper> channelSendSelectHelpers;
    @Autowired
    private NativeSqlHelper nativeSqlHelper;
    @Autowired
    private CustomerService customerService;
    @Autowired
    private CustomerAnswersService customerAnswersService;
    @Autowired
    private CustomerHistoryRecordService customerHistoryRecordService;
    @Autowired
    private CustomerMessageService customerMessageService;
    @Autowired
    private ISurveyTaskTrigger surveyTaskTrigger;
    @Autowired
    private ISmsAccountService smsAccountService;
    @Autowired
    private UserTaskService userTaskService;
    @Autowired
    private LinkService linkService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 计算选择的客户数量
     */
    public long countSelectCustomers(Long orgId, Long userId, ChannelType channelType, ChannelSelectCustomerDto select) {
        long count = 0;
        for (ChannelSendDataHelper helper : channelSendSelectHelpers) {
            count += helper.countCustomers(orgId, userId, channelType, select);
        }
        return count;
    }

    /**
     * worker 回调此方法创建具体的发送任务
     */
    @Transactional
    public void send(Long orgId, Long userId, Long taskProgressId, Long surveyId, Long channelId, int pageSize, ChannelSendCustomerDto data) {
        SurveyChannel channel = requireChannel(surveyId, channelId);
        Date sendTime = data.parseSendTime();
        Duration delay = sendTime.after(new Date()) ? Duration.between(LocalDateTime.now(), DateHelper.toLocalDateTime(sendTime)) : null;
        String surveyName = Optional.ofNullable(surveyRepository.findSimpleById(surveyId)).map(SimpleSurvey::getTitle).orElse("");
        if (channel.getType() == PHONE_MSG) {
            TemplateInfoDto template = requireSmsTemplate(data.getThirdPartTemplateId(), data.getContent());
            smsCounter(orgId, userId, taskProgressId, template, (planSmsCostOnce) -> {
                foreachSelectCustomers(orgId, userId, channel.getType(), pageSize, data, customer -> {
                    sendCustomer(orgId, userId, surveyId, surveyName, taskProgressId, planSmsCostOnce, channel, sendTime, delay, customer, template);
                });
            });
        } else if (channel.getType() == EMAIL) {
            TemplateInfoDto template = requireEmailTemplate(data.getThirdPartTemplateId(), data.getContent());
            foreachSelectCustomers(orgId, userId, channel.getType(), pageSize, data, customer -> {
                sendCustomer(orgId, userId, surveyId, surveyName, taskProgressId, 0, channel, sendTime, delay, customer, template);
            });
        } else if (channel.getType() == WECHAT_SERVICE) {
            Map<Long, TemplateInfoDto> templateMap = requireWechatTemplateMap(data.getThirdPartTemplateId(), data.getWechatTemplateId(), data.getContent());
            Map<String, Long> wechatAppIdMap = templateMap.values().stream().collect(Collectors.toMap(TemplateInfoDto::getWeChatAppId, TemplateInfoDto::getWechatConfigId, (o1, o2) -> o1));
            foreachSelectCustomers(orgId, userId, channel.getType(), pageSize, data, customer -> {
                // 加载客户的微信信息到 customer 中
                loadCustomerWechatInfo(orgId, customer, wechatAppIdMap, templateMap);
                TemplateInfoDto template = getWechatTemplate(templateMap, customer);
                sendCustomer(orgId, userId, surveyId, surveyName, taskProgressId, 0, channel, sendTime, delay, customer, template);
            });
        }
    }

    private void sendCustomer(Long orgId, Long userId,
                              Long surveyId, String surveyName,
                              Long taskProgressId,
                              int planSmsCost,
                              SurveyChannel channel,
                              Date sendTime, Duration delay,
                              Customer customer,
                              @Nullable TemplateInfoDto template) {
        try {
            SurveySendRecord record = buildRecord(surveyId, surveyName, channel, customer, template, sendTime);
            if (customer.getId() != null) {
                customerAnswersService.addBySurveyChannel(customer, surveyId, surveyName, channel.getType().mapToCustomerInteractionType(), record.getClientId());
                customerHistoryRecordService.addBySendSurveyChannel(userId, customer.getId(), surveyId, surveyName, channel.getName(), channel.getType().getText());
            }
            if (record.getSendStatus() == SendStatus.SEND_FAIL) {
                // 如果已经设置发送失败，就不再添加发送任务了
                return;
            }
            if (channel.getType() == PHONE_MSG) {
                smsAccountService.addSmsTask(orgId, taskProgressId, record.getId(), planSmsCost);
            }
            triggerSendTask(orgId, userId, taskProgressId, channel, record, template, delay);
        } catch (Throwable e) {
            log.error("构建渠道发送记录失败, channelId={}, customerId={}, customerName={}", channel.getId(), customer.getId(), customer.getUsername(), e);
        }
    }

    /**
     * 添加发送任务到worker
     */
    public void triggerSendTask(Long orgId, Long userId,
                                Long taskProgressId, SurveyChannel channel, SurveySendRecord record,
                                TemplateInfoDto template, Duration delay) {
        if (channel.getType() == PHONE_MSG) {
            CustomerSendSmsDto dto = new CustomerSendSmsDto(orgId, userId, taskProgressId, record.getId(),
                    null, record.getContent(), record.getAccount(),
                    template.getSmsTemplateId(), template.getSmsTemplateName(), template.getSmsSignId(), template.getSmsRealSign(), delay, null);
            surveyTaskTrigger.customerSendSms(dto);
        } else if (channel.getType() == WECHAT_SERVICE) {
            CustomerSendWechatDto dto = new CustomerSendWechatDto(orgId, userId, taskProgressId, record.getId(),
                    template.getWechatConfigId(), template.getWeChatAppId(), template.getWeChatTemplateId(),
                    record.getAccount(), record.getContent(), record.getSendUrl(), delay, null);
            surveyTaskTrigger.customerSendWechat(dto);
        } else if (channel.getType() == EMAIL) {
            String sender = "";
            String subject = "";
            if (record.getExtParamsDto() != null) {
                sender = record.getExtParamsDto().getEmailParams().getOrDefault("sender", "");
                subject = record.getExtParamsDto().getEmailParams().getOrDefault("subject", "");
            }
            CustomerSendEmailDto dto = new CustomerSendEmailDto(orgId, userId, taskProgressId, record.getId(), null,
                    record.getAccount(), sender, subject, record.getContent(),
                    delay, null);
            surveyTaskTrigger.customerSendEmail(dto);
        }
    }

    private void smsCounter(Long orgId, Long userId, Long taskProgressId, TemplateInfoDto template, Consumer<Integer> foreachItems) {
        smsAccountService.buildSmsTask(orgId, userId, taskProgressId, template.fullSmsTemplate(), foreachItems);
    }

    private void smsCounter(Long orgId, Long userId, Long taskProgressId, String fullSmsTemplate, Consumer<Integer> foreachItems) {
        smsAccountService.buildSmsTask(orgId, userId, taskProgressId, fullSmsTemplate, foreachItems);
    }

    // 设置 customer#wechatParams#configId
    // 设置 customer#wechatParams#appId
    // 设置 customer#wechatParams#openId
    private void loadCustomerWechatInfo(long orgId, Customer customer, Map<String, Long> wechatAppIdMap, Map<Long, TemplateInfoDto> templateMap) {
        if (customer.getId() != null) {
            // 如果客户是数据库中的客户，则去查询 ThirdPartyCustomer
            ThirdPartyCustomer thirdPartyCustomer = customerMessageService.getWechatCustomer(customer);
            if (thirdPartyCustomer != null) {
                customer.getWechatParams().setOpenId(thirdPartyCustomer.getOpenId());
                customer.getWechatParams().setConfigId(thirdPartyCustomer.getThirdpartyAuthId());
                customer.getWechatParams().setAppId(Optional.ofNullable(templateMap.get(thirdPartyCustomer.getThirdpartyAuthId())).map(TemplateInfoDto::getWeChatAppId).orElse(null));
                return;
            }
        } else {
            // 如果客户是通过外部导入的客户，则使用外部导入的appId,openId，查询出configId，并尝试关联数据库中的客户
            Long configId = wechatAppIdMap.get(customer.getWechatParams().getAppId());
            if (configId != null) {
                customer.getWechatParams().setConfigId(configId);
                Customer searchCustomer = customerMessageService.getWechatCustomer(orgId, configId, customer.getWechatParams().getOpenId());
                if (searchCustomer != null) {
                    customer.setId(searchCustomer.getId());
                }
                return;
            }
        }
        // 未加载到微信客户信息, 设置空值
        customer.getWechatParams().setOpenId("");
        customer.getWechatParams().setAppId("");
    }

    /**
     * 遍历所有选择的客户
     */
    private void foreachSelectCustomers(Long orgId, Long userId, ChannelType channelType, int pageSize, ChannelSelectCustomerDto select, Consumer<Customer> consumerCustomer) {
        for (ChannelSendDataHelper helper : channelSendSelectHelpers) {
            helper.foreachCustomers(orgId, userId, channelType, pageSize, select, customers -> {
                if (CollectionUtils.isNotEmpty(customers)) {
                    customers.forEach(consumerCustomer);
                }
            });
        }
    }

    /**
     * 构建渠道发送记录
     */
    private SurveySendRecord buildRecord(Long surveyId, String surveyName, SurveyChannel channel, Customer customer,
                                         @Nullable TemplateInfoDto template, Date sendTime) {
        // 快速失败：如果没有模版信息，还是会创建发送记录，但是状态是直接失败，不尝试发送了
        boolean failure = template == null;
        String result = "";
        SurveySendRecord record = new SurveySendRecord();
        SurveySendRecordExtParamDto extParam = new SurveySendRecordExtParamDto();
        if (channel.getType() == PHONE_MSG) {
            record.setAccount(customer.getMobile());
        } else if (channel.getType() == WECHAT_SERVICE) {
            record.setAccount(customer.getWechatParams().getOpenId());
            if (StringUtils.isEmpty(customer.getWechatParams().getOpenId())
                    || StringUtils.isEmpty(customer.getWechatParams().getAppId())
                    || customer.getWechatParams().getConfigId() == null) {
                // 没有查询到关联的微信用户信息，直接失败，不再继续发送，
                failure = true;
                result = "没有查询到关联的微信用户信息";
            }
            if (template != null) {
                extParam.setWechatConfigId(template.getWechatConfigId());
                extParam.setWechatAppId(template.getWeChatAppId());
                if (!template.isWeChatAuthorized()) {
                    // 公众号已经取消授权了，直接失败，不再继续发送，
                    failure = true;
                    result = "公众号已取消授权";
                }
            }
        } else if (channel.getType() == EMAIL) {
            record.setAccount(customer.getEmail());
            if (template != null) {
                extParam.addEmailParam("sender", template.getEmailSender()); // 第一次构建record时，sender从请求参数中获取，一定存在
            }
        }
        record.setClientId(UUID.randomUUID().toString());
        record.setSurveyId(surveyId);
        record.setCustomerId(customer.getId());
        record.setName(customer.getUsername());
        record.setChannelId(channel.getId());
        SurveyLinkDto link = buildSurveyUrl(surveyId, channel.getId(), channel.getType(), record.getClientId(), customer);
        record.setSendUrl(link.getOriginUrl());
        record.setLinkId(link.getLinkId());
        // 设置发送内容
        if (template != null) {
            record.setThirdpartyTemplateId(template.getThirdpartyTemplateId());
            buildContent(record, extParam, surveyName, channel.getType(), customer, template, link);
        } else {
            record.setContent("");
            result = "未找到模版信息";
        }
        record.setSendCount(1);
        record.setSendTime(sendTime);
        if (failure) {
            record.setSendStatus(SendStatus.SEND_FAIL);
        } else {
            record.setSendStatus(SendStatus.UN_SEND);
        }
        record.setStatus(ChannelSendStatus.UN_COMPLETE);
        record.setReplyStatus(ReplyStatus.UN_VISIT);
        record.setType(channel.getType());
        record.setSendId("");
        record.setResult(result);
        if (MapUtils.isNotEmpty(customer.getContentParams())) {
            extParam.setCustomerParams(customer.getContentParams());
        }
        record.setExtParamsDto(extParam);
        record.setExtParams(JsonHelper.toJson(extParam));
        surveySendRecordRepository.save(record);
        return record;
    }

    /**
     * 构建渠道发送内容
     */
    private void buildContent(SurveySendRecord record, SurveySendRecordExtParamDto extParam,
                              String surveyName, ChannelType channelType, Customer customer,
                              TemplateInfoDto template, SurveyLinkDto link) {
        String text = null;
        if (channelType == PHONE_MSG) {
            text = buildSmsMessage(template, customer, surveyName, link);
        } else if (channelType == WECHAT_SERVICE) {
            text = buildWechatMessage(template, customer, surveyName, link);
        } else if (channelType == EMAIL) {
            text = buildEmailMessage(template, extParam, customer, surveyName, link);
        }
        record.setContent(text);
    }

    private String buildSmsMessage(TemplateInfoDto template, Customer customer, String surveyName, SurveyLinkDto link) {
        String contentTemplate = template.getSmsContent();
        Map<String, Object> parameters = customerMessageService.buildNativeParams(link, surveyName, customer, null);
        return TemplateEngine.renderTextTemplate(contentTemplate, parameters);
    }

    private String buildWechatMessage(TemplateInfoDto template, Customer customer, String surveyName, SurveyLinkDto link) {
        Map<String, Object> parameters = customerMessageService.buildNativeParams(link, surveyName, customer, null);
        return customerMessageService.buildWechatContent(template.getWeChatTemplateId(), customer.getWechatParams().getOpenId(), link.getOriginUrl(), template.getWeChatContent(), parameters);
    }

    private String buildEmailMessage(TemplateInfoDto template, SurveySendRecordExtParamDto extParam, Customer customer, String surveyName, SurveyLinkDto link) {
        Map<String, Object> data = new HashMap<>();
        data.put("subject", template.getEmailTitle());
        data.put("content", template.getEmailContent());
        Map<String, Object> parameters = customerMessageService.buildNativeParams(link, surveyName, customer, null);
        data = TemplateEngine.renderJsonTemplate(data, parameters);
        extParam.addEmailParam("subject", (String) data.get("subject"));
        return (String) data.get("content");
    }

    /**
     * 构建渠道发送地址
     */
    public SurveyLinkDto buildSurveyUrl(Long surveyId, Long channelId, ChannelType channelType, String clientId, Customer customer) {
        Map<String, Object> params = new HashMap<>();
        params.put("channelId", channelId);
        params.put("clientId", clientId);
        params.put("collectorMethod", channelType.toString());
        if (customer.getId() != null) {
            params.put("customerId", customer.getId());
        }
        if (StringUtils.isNotEmpty(customer.getExternalUserId())) {
            params.put("externalUserId", customer.getExternalUserId());
        }
        if (customer.getUrlInternalParams() != null) {
            Optional.ofNullable(customer.getUrlInternalParams().getDepartmentId()).ifPresent(i -> params.put("departmentId", i));
            Optional.ofNullable(customer.getUrlInternalParams().getDepartmentCode()).filter(StringUtils::isNotEmpty).ifPresent(i -> params.put("departmentCode", i));
            Optional.ofNullable(customer.getUrlInternalParams().getExternalCompanyId()).filter(StringUtils::isNotEmpty).ifPresent(i -> params.put("externalCompanyId", i));
            Optional.ofNullable(customer.getUrlInternalParams().getExpireTime()).map(DateHelper::parseDateTime).ifPresent(i -> {
                params.put("expireTime", DateHelper.format(i, DateHelper.DATE_TIME_FORMATTER2));
                stringRedisTemplate.opsForHash().put(CustomerClientIdParamDto.clientIdParamKey(clientId), "expireTime", DateHelper.format(i, DateHelper.DATE_TIME_FORMATTER));
            });
        }
        if (MapUtils.isNotEmpty(customer.getUrlCustomParams())) {
            Map<String, Object> parameters = new HashMap<>();
            customer.getUrlCustomParams().forEach((k, v) -> {
                parameters.put(k, v);
            });
            if (!parameters.isEmpty()) {
                params.put("parameters", parameters);
            }
        }
        Link link = linkService.createLink(surveyId, params);
        return wrapSurveyLink(link);
    }

    private SurveyLinkDto wrapSurveyLink(Link link) {
        SurveyLinkDto dto = new SurveyLinkDto();
        dto.setLinkId(link.getId());
        dto.setOriginUrl(linkService.toOriginUrl(link));
        dto.setShortUrl(linkService.toShortUrl(link));
        dto.setShortCode(linkService.toShortCode(link));
        return dto;
    }

    /**
     * 定时重新发送
     * worker 接收到delay任务时执行
     */
    @Transactional
    public void resendByDelay(Long orgId, Long userId, Long surveyId, SurveyChannel channel, List<ChannelResendCondition> resendConditions) {
        if (CollectionUtils.isEmpty(resendConditions)) {
            return;
        }
        GenericSpecification<SurveySendRecord> specification = queryResendFilter(surveyId, channel.getId(), resendConditions);
        long count = surveySendRecordRepository.count(specification);
        if (count <= 0) {
            return;
        }
        Map<Long, TemplateInfoDto> templateMap = new HashMap<>();
        Set<Long> notExist = new HashSet<>();
        TaskProgress progress = userTaskService.createTask(orgId, userId, UserTaskType.sendSurveyChannel, (int) count, null, channel.getId());
        String surveyName = Optional.ofNullable(surveyRepository.findSimpleById(surveyId)).map(SimpleSurvey::getTitle).orElse("");
        if (channel.getType() == PHONE_MSG) {
            checkResendSmsCost(orgId, (int) count, 67);
            String smsCostLog = String.format("渠道问卷定时重发：%s-%s（%s）", surveyName, channel.getName(), channel.getType().getText());
            smsCounter(orgId, userId, progress.getId(), smsCostLog, (planSmCost) -> {
                foreachResendRecord(specification, record -> {
                    TemplateInfoDto template = getTemplateByResend(templateMap, notExist, record, thirdpartyTemplateId -> getSmsTemplate(thirdpartyTemplateId, new HashMap<>()));
                    resendCustomer(orgId, userId, surveyName, progress.getId(), smsAccountService.calcNumberByText(record.getContent()), channel, record, false, customer -> template);
                });
            });
        } else if (channel.getType() == EMAIL) {
            foreachResendRecord(specification, record -> {
                TemplateInfoDto template = getTemplateByResend(templateMap, notExist, record, thirdpartyTemplateId -> getEmailTemplate(thirdpartyTemplateId, new HashMap<>()));
                resendCustomer(orgId, userId, surveyName, progress.getId(), 0, channel, record, false, customer -> template);
            });
        } else if (channel.getType() == WECHAT_SERVICE) {
            foreachResendRecord(specification, record -> {
                TemplateInfoDto template = getTemplateByResend(templateMap, notExist, record, thirdpartyTemplateId -> getWechatTemplate(thirdpartyTemplateId, null));
                resendCustomer(orgId, userId, surveyName, progress.getId(), 0, channel, record, false, customer -> template);
            });
        }
    }

    private GenericSpecification<SurveySendRecord> queryResendFilter(Long surveyId, Long chanelId, List<ChannelResendCondition> resendConditions) {
        GenericSpecification<SurveySendRecord> specification = new GenericSpecification<>();
        specification.add(new ResourceQueryCriteria("surveyId", surveyId));
        specification.add(new ResourceQueryCriteria("channelId", chanelId));
        Map<String, Set<Object>> statusMap = new HashMap<>();
        resendConditions.forEach(i -> {
            statusMap.computeIfAbsent(i.getPropertyName(), k -> new HashSet<>()).add(EnumHelper.parse(i.getStatusType().getEnumConstants(), i.name()));
        });
        statusMap.forEach((k, v) -> {
            if (v.size() == 1) {
                specification.add(new ResourceQueryCriteria(k, v.toArray()[0]));
            } else if (v.size() > 0) {
                specification.add(new ResourceQueryCriteria(k, v, QueryOperator.IN));
            }
        });
        return specification;
    }

    private void foreachResendRecord(GenericSpecification<SurveySendRecord> specification, Consumer<SurveySendRecord> resend) {
        boolean hasNext;
        int page = 0;
        do {
            List<SurveySendRecord> records = surveySendRecordRepository.findPageList(specification, PageRequest.of(page, 1000));
            if (CollectionUtils.isNotEmpty(records)) {
                hasNext = true;
                page++;
                records.forEach(resend);
            } else {
                hasNext = false;
            }
        } while (hasNext);
    }

    private TemplateInfoDto getTemplateByResend(Map<Long, TemplateInfoDto> templateMap, Set<Long> notExist, SurveySendRecord record, Function<Long, TemplateInfoDto> loadTemplateByThirdpartyTemplateId) {
        Long thirdpartyTemplateId = record.getThirdpartyTemplateId();
        if (thirdpartyTemplateId == null) {
            return null;
        }
        TemplateInfoDto template = templateMap.get(thirdpartyTemplateId);
        if (template == null) {
            if (!notExist.contains(thirdpartyTemplateId)) {
                template = loadTemplateByThirdpartyTemplateId.apply(thirdpartyTemplateId);
                if (template != null) {
                    templateMap.put(thirdpartyTemplateId, template);
                } else {
                    notExist.add(thirdpartyTemplateId);
                }
            }
        }
        return template;
    }

    /**
     * 校验短信额度是否足够
     */
    private void checkResendSmsCost(Long orgId, int count, int smsLength) {
        int availableBalance = smsAccountService.balance(orgId);
        CountSendDto cost = new CountSendDto(count, smsLength, availableBalance);
        if (cost.getAllCost() > 0 && cost.getBalance() >= cost.getAllCost()) {
            return;
        }
        throw new BadRequestException("短信余量不足");
    }

    /**
     * 重新发送指定的记录
     */
    @Transactional
    public void resend(Long orgId, Long userId, Long surveyId, List<Long> logIds, SurveyChannel channel, Long thirdPartTemplateId, Long wechatTemplateId, Map<String, Object> contentTemplate) {
        if (CollectionUtils.isNotEmpty(logIds)) {
            TaskProgress progress = userTaskService.createTask(orgId, userId, UserTaskType.sendSurveyChannel, logIds.size(), null, channel.getId());
            String surveyName = Optional.ofNullable(surveyRepository.findSimpleById(surveyId)).map(SimpleSurvey::getTitle).orElse("");
            if (channel.getType() == PHONE_MSG) {
                TemplateInfoDto template = requireSmsTemplate(thirdPartTemplateId, contentTemplate);
                checkResendSmsCost(orgId, logIds.size(), template.getSmsLength());
                smsCounter(orgId, userId, progress.getId(), template, (planSmsCost) -> {
                    foreachRecordIds(logIds, record -> {
                        resendCustomer(orgId, userId, surveyName, progress.getId(), planSmsCost, channel, record, true, customer -> template);
                    });
                });
            } else if (channel.getType() == EMAIL) {
                TemplateInfoDto template = requireEmailTemplate(thirdPartTemplateId, contentTemplate);
                foreachRecordIds(logIds, record -> {
                    resendCustomer(orgId, userId, surveyName, progress.getId(), 0, channel, record, true, customer -> template);
                });
            } else if (channel.getType() == WECHAT_SERVICE) {
                Map<Long, TemplateInfoDto> templateMap = requireWechatTemplateMap(thirdPartTemplateId, wechatTemplateId, contentTemplate);
                foreachRecordIds(logIds, record -> {
                    resendCustomer(orgId, userId, surveyName, progress.getId(), 0, channel, record, true, customer -> getWechatTemplate(templateMap, customer));
                });
            }
        }
    }

    private void foreachRecordIds(List<Long> logIds, Consumer<SurveySendRecord> resend) {
        logIds.forEach(logId -> surveySendRecordRepository.findById(logId).ifPresent(resend));
    }

    private void resendCustomer(Long orgId, Long userId, String surveyName,
                                long taskProgressId, int planSmsCost, SurveyChannel channel, SurveySendRecord record,
                                boolean updateContent, Function<Customer, TemplateInfoDto> getTemplate) {
        SurveySendRecordExtParamDto extParamDto = buildResendExtParams(record);
        Customer customer = buildResendCustomer(channel.getType(), record, extParamDto);
        TemplateInfoDto template = getTemplate.apply(customer);
        updateRecordAndContent(surveyName, record, channel, customer, extParamDto, template, updateContent);
        if (record.getSendStatus() == SendStatus.SEND_FAIL) {
            return;
        }
        if (channel.getType() == PHONE_MSG) {
            smsAccountService.addSmsTask(orgId, taskProgressId, record.getId(), planSmsCost);
        }
        triggerSendTask(orgId, userId, taskProgressId, channel, record, template, null);
    }

    private SurveySendRecordExtParamDto buildResendExtParams(SurveySendRecord record) {
        SurveySendRecordExtParamDto extParamDto = JsonHelper.toObject(record.getExtParams(), SurveySendRecordExtParamDto.class);
        if (extParamDto == null) {
            extParamDto = new SurveySendRecordExtParamDto();
        }
        return extParamDto;
    }

    private Customer buildResendCustomer(ChannelType type, SurveySendRecord record, SurveySendRecordExtParamDto extParamDto) {
        Customer customer;
        if (record.getCustomerId() != null && record.getCustomerId() > 0) {
            customer = customerService.get(record.getCustomerId());
        } else {
            customer = new Customer();
        }
        if (customer == null) {
            return null;
        }
        if (type == PHONE_MSG) {
            customer.setMobile(record.getAccount());
        } else if (type == EMAIL) {
            customer.setEmail(record.getAccount());
        } else if (type == WECHAT_SERVICE) {
            customer.getWechatParams().setOpenId(record.getAccount());
            customer.getWechatParams().setConfigId(extParamDto.getWechatConfigId());
            customer.getWechatParams().setAppId(extParamDto.getWechatAppId());
        }
        customer.setUsername(record.getName());
        if (MapUtils.isNotEmpty(extParamDto.getCustomerParams())) {
            customer.setContentParams(extParamDto.getCustomerParams());
        }
        return customer;
    }

    /**
     * 重新发送时更新发送内容
     */
    private void updateRecordAndContent(String surveyName, SurveySendRecord record, SurveyChannel channel, @Nullable Customer customer,
                                        SurveySendRecordExtParamDto extParamDto, @Nullable TemplateInfoDto template, boolean updateContent) {
        boolean failure = customer == null || template == null;
        // 如果linkId不存在，则可能是旧的数据，这里通过sendUrl重新生成一个link
        Link resendLink = record.getLinkId() != null && record.getLinkId() > 0 ? linkService.getLink(record.getLinkId()) : null;
        if (resendLink == null) {
            resendLink = linkService.createLink(record.getSendUrl(), true);
            record.setLinkId(resendLink.getId());
        }
        if (template != null) {
            record.setThirdpartyTemplateId(template.getThirdpartyTemplateId());
            if (updateContent && customer != null) {
                SurveyLinkDto link = wrapSurveyLink(resendLink);
                buildContent(record, extParamDto, surveyName, channel.getType(), customer, template, link);
            }
            // 如果是微信渠道，已经取消授权了，标记失败
            if (channel.getType() == WECHAT_SERVICE && !template.isWeChatAuthorized()) {
                failure = true;
                record.setResult("公众号已经取消授权");
            }
        }
        if (failure) {
            record.setSendStatus(SendStatus.SEND_FAIL);
        } else {
            record.setSendStatus(SendStatus.UN_SEND);
        }
        Date sendTime = new Date();
        if (record.getSendTime() == null || record.getSendTime().before(sendTime)) {
            record.setSendTime(sendTime);
        }
        record.setSendCount(record.getSendCount() == null ? 1 : (record.getSendCount() + 1));
        surveySendRecordRepository.save(record);
    }

    /**
     * 校验发送模板，直接从请求参数{content}中获得替换后的数据，设置到template中
     */
    public void checkTemplate(Long thirdTemplateId, Long wechatTemplateId, ChannelType channelType, Map<String, Object> content) {
        if (channelType == PHONE_MSG) {
            requireSmsTemplate(thirdTemplateId, content);
            return;
        } else if (channelType == WECHAT_SERVICE) {
            requireWechatTemplateMap(thirdTemplateId, wechatTemplateId, content);
            return;
        } else if (channelType == EMAIL) {
            requireEmailTemplate(thirdTemplateId, content);
            return;
        }
        throw new BadRequestException("消息模版已被删除，请重新选择新模版");
    }

    public TemplateInfoDto requireSmsTemplate(Long thirdTemplateId, Map<String, Object> content) {
        Object replaceContent = content.get("content");
        return customerMessageService.getSmsTemplate(thirdTemplateId, replaceContent == null ? null : replaceContent.toString());
    }

    public TemplateInfoDto getSmsTemplate(Long thirdTemplateId, Map<String, Object> content) {
        try {
            return requireSmsTemplate(thirdTemplateId, content);
        } catch (Throwable e) {
            log.error("getSmsTemplate", e);
        }
        return null;
    }

    public Map<Long, TemplateInfoDto> requireWechatTemplateMap(Long thirdTemplateId, Long wechatTemplateId, Map<String, Object> content) {
        Map<Long, TemplateInfoDto> templateMap = null;
        if (wechatTemplateId != null) {
            templateMap = customerMessageService.getWeChatTemplateMap(wechatTemplateId, content);
        } else if (thirdTemplateId != null) {
            // 兼容旧方案，使用 thirdTemplateId
            TemplateInfoDto templateInfoDto = customerMessageService.getWeChatTemplate(thirdTemplateId, content);
            if (templateInfoDto != null) {
                templateMap = new HashMap<>();
                templateMap.put(templateInfoDto.getWechatConfigId(), templateInfoDto);
            }
        }
        if (MapUtils.isEmpty(templateMap)) {
            throw new BusinessException("消息模版已被删除，请重新选择新模版");
        }
        return templateMap;
    }


    public TemplateInfoDto getWechatTemplate(Map<Long, TemplateInfoDto> wechatTemplateMap, Customer customer) {
        if (customer == null) {
            return null;
        }
        return wechatTemplateMap.get(customer.getWechatParams().getConfigId());
    }

    public TemplateInfoDto getWechatTemplate(Long thirdTemplateId, Map<String, Object> content) {
        try {
            return customerMessageService.getWeChatTemplate(thirdTemplateId, content);
        } catch (Throwable e) {
            log.error("getWechatTemplate", e);
        }
        return null;
    }

    public TemplateInfoDto requireEmailTemplate(Long thirdTemplateId, Map<String, Object> content) {
        String sender = (String) content.get("sender"); // 重新发送时，这里会是null
        String replaceTitle = (String) content.get("title");
        String replaceText = (String) content.get("content");
        return customerMessageService.getEmailTemplate(thirdTemplateId, sender, replaceTitle, replaceText);
    }

    public TemplateInfoDto getEmailTemplate(Long thirdTemplateId, Map<String, Object> content) {
        try {
            return requireEmailTemplate(thirdTemplateId, content);
        } catch (Throwable e) {
            log.error("getEmailTemplate", e);
        }
        return null;
    }

    public SurveyChannel requireChannel(Long surveyId, Long channelId) {
        SurveyChannel channel = channelRepository.findById(channelId).orElse(null);
        if (channel == null || !channel.getSid().equals(surveyId)) {
            throw new EntityNotFoundException(SurveyChannel.class);
        } else if (channel.getStatus() == ChannelStatus.COMPLETE) {
            throw new BadRequestException(String.format("渠道(%s)已关闭", channel.getName()));
        }
        return channel;
    }
}
