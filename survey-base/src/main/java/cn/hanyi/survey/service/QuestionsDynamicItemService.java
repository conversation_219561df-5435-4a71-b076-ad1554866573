package cn.hanyi.survey.service;

import cn.hanyi.survey.core.entity.SurveyQuestionItem;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.PreparedStatement;
import java.util.List;

@Service
public class QuestionsDynamicItemService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    public List<SurveyQuestionItem> findAllByQuestion(Long questionId) {
        String sql = "SELECT id, value, text FROM survey_question_item WHERE q_id = ? AND is_dynamic = 1";
        return jdbcTemplate.query(sql, (rs, rowNum) -> {
            SurveyQuestionItem item = new SurveyQuestionItem();
            item.setId(rs.getLong("id"));
            item.setValue(rs.getString("value"));
            item.setText(rs.getString("text"));
            return item;
        }, questionId);
    }

    public SurveyQuestionItem findOneByQuestionAndValue(Long questionId, String value) {
        String sql = "SELECT id, value, text FROM survey_question_item WHERE q_id = ? AND value = ? AND is_dynamic = 1 limit 1";
        List<SurveyQuestionItem> list = jdbcTemplate.query(sql, (rs, rowNum) -> {
            SurveyQuestionItem item = new SurveyQuestionItem();
            item.setId(rs.getLong("id"));
            item.setValue(rs.getString("value"));
            item.setText(rs.getString("text"));
            return item;
        }, questionId, value);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    @Transactional
    public void update(Long id, String text) {
        String sql = "UPDATE survey_question_item SET text = ? WHERE id = ?";
        jdbcTemplate.update((con -> {
            PreparedStatement statement = con.prepareStatement(sql);
            statement.setString(1, text);
            statement.setLong(2, id);
            return statement;
        }));
    }

    @Transactional
    public void add(Long questionId, String value, String text) {
        String sql = "INSERT INTO survey_question_item (q_id, value, text, configure, is_dynamic) VALUES (?, ?, ?, ?, ?)";
        jdbcTemplate.update((con -> {
            PreparedStatement statement = con.prepareStatement(sql);
            statement.setLong(1, questionId);
            statement.setString(2, value);
            statement.setString(3, text);
            statement.setString(4, "");
            statement.setInt(5, 1);
            return statement;
        }));
    }

    @Transactional
    public void deleteAll(Long questionId) {
        String sql = "DELETE FROM survey_question_item WHERE q_id = (?)";
        jdbcTemplate.update((con -> {
            PreparedStatement statement = con.prepareStatement(sql);
            statement.setLong(1, questionId);
            return statement;
        }));
    }

}
