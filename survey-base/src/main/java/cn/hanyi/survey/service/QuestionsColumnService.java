package cn.hanyi.survey.service;

import cn.hanyi.cem.core.constant.QuestionItemType;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.core.entity.SurveyQuestionColumn;
import cn.hanyi.survey.core.entity.SurveyQuestionColumnDto;
import cn.hanyi.survey.core.repository.SurveyQuestionColumnRepository;
import cn.hanyi.survey.workertrigger.ISurveyEventTrigger;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.befun.core.dto.ResourceBatchUpdateRequestDto;
import org.befun.core.dto.ResourceUpdateItemRequestDto;
import org.befun.core.entity.BaseEntity;
import org.befun.core.service.CustomDeepEmbeddedService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class QuestionsColumnService extends CustomDeepEmbeddedService<SurveyQuestionColumn, SurveyQuestionColumnDto, SurveyQuestionColumnRepository> {

    @Autowired
    private ISurveyEventTrigger surveyEventTrigger;

    @Autowired
    private QuestionService questionService;

    @Override
    protected Object requireParent(long l) {
        return questionService.require(l);
    }


    @Override
    public Boolean deleteOneDeepEmbeddedMany(Long entityId, String embeddedMapperBy, Long embeddedId, String deepEmbeddedMapperBy, Long deepId) {
        surveyEventTrigger.questionItemDelete(entityId, embeddedId, deepId, QuestionItemType.ITEM.name());
        return super.deleteOneDeepEmbeddedMany(entityId, embeddedMapperBy, embeddedId, deepEmbeddedMapperBy, deepId);
    }

    @Override
    public void delete(SurveyQuestionColumn entity) {
        var question = entity.getQuestion();
        surveyEventTrigger.questionItemDelete(question.getSurvey().getId(), question.getId(), entity.getId(), QuestionItemType.COLUMN.name());
        super.delete(entity);
    }

    @Override
    public void deleteAll(Iterable<SurveyQuestionColumn> entities) {
        if (!IterableUtils.isEmpty(entities)) {
            var question = entities.iterator().next().getQuestion();
            entities.forEach(c -> {
                surveyEventTrigger.questionItemDelete(question.getSurvey().getId(), question.getId(), c.getId(), QuestionItemType.COLUMN.name());
            });
        }

        super.deleteAll(entities);
    }

    public List<SurveyQuestionColumnDto> insertQuestionColumns(SurveyQuestion question, List<SurveyQuestionColumnDto> columns) {
        List<SurveyQuestionColumn> cs = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(columns)) {
            columns.forEach(c -> {
                SurveyQuestionColumn ce = new SurveyQuestionColumn();
                crudService.mapToEntity(c, ce);
                ce.setQuestion(question);
                cs.add(ce);
            });
            repository.saveAll(cs);
        }
        return mapToDto(cs);
    }

    @Transactional
//    @Override
    public List<SurveyQuestionColumnDto> batchUpdate(Long rootId, String rootFieldNameInEmbedded, Long embeddedId, String embeddedFieldNameInDeep, ResourceBatchUpdateRequestDto<SurveyQuestionColumnDto> batchChangeDto) {

        ArrayList<SurveyQuestionColumn> createEntities = new ArrayList<>();

        SurveyQuestion question = questionService.requireQuestion(embeddedId);
        Iterator<ResourceUpdateItemRequestDto<SurveyQuestionColumnDto>> iterator = batchChangeDto.getChanges().iterator();
        while (iterator.hasNext()) {
            ResourceUpdateItemRequestDto<SurveyQuestionColumnDto> change = iterator.next();
            if (change.getData().getId() == null || change.getData().getId() < 1) {
                SurveyQuestionColumn entity = mapperService.map(change.getData(), SurveyQuestionColumn.class);
                entity.setQuestion(question);
                createEntities.add(entity);
                iterator.remove();
            }
        }
        List<SurveyQuestionColumn> created = repository.saveAll(createEntities);
        List<SurveyQuestionColumnDto> updated = new ArrayList<>();

        if (!batchChangeDto.getChanges().isEmpty()) {
            updated.addAll(super.batchUpdateDeepEmbeddedMany(rootId, rootFieldNameInEmbedded, embeddedId, embeddedFieldNameInDeep, batchChangeDto));
        }

        ArrayList<Long> deleteIds = new ArrayList<>();
        deleteIds.addAll(created.stream().map(BaseEntity::getId).collect(Collectors.toList()));
        deleteIds.addAll(updated.stream().map(SurveyQuestionColumnDto::getId).collect(Collectors.toList()));

        if (batchChangeDto.getChanges().isEmpty()) {
            deleteIds.add(-1L);
        }

        repository.deleteByQuestionIdAndIdNotIn(embeddedId, deleteIds);

        List<SurveyQuestionColumn> columns = repository.findByQuestionIdOrderBySequenceAsc(embeddedId);
        return mapToDto(columns);
    }

    public SurveyQuestionColumnDto update(Long id, Map<String, Object> data) {
        SurveyQuestionColumn column = require(id);

        if (data.containsKey("visibleIf")) {
            column.setVisibleIf(data.get("visibleIf") == null ? null : data.get("visibleIf").toString());
        }
        return updateOneDeepEmbeddedMany(
                column.getQuestion().getSurvey().getId(),
                "survey",
                column.getQuestion().getId(),
                "question",
                column.getId(),
                mapperService.map(data, SurveyQuestionColumnDto.class)
        );
    }


}
