package cn.hanyi.survey.service.word;

/**
 * <AUTHOR>
 * @date 2023/4/10 14:23:45
 */
public class StringBufferHelper {

    private StringBuffer sb;

    public StringBufferHelper(StringBuffer sb) {
        this.sb = sb;
    }

    public StringBuffer append(String context) {
        return sb.append(context);
    }

    public StringBuffer appendSurveyTitle(String context) {
        return sb.append("<p style=\"text-align:center;font-weight:bold;\">").append(context).append("</p>");
    }

    public StringBuffer append_p(String context) {
        return sb.append("<p>").append(context).append("</p>");
    }

    public StringBuffer append_h(String context) {
        return sb.append("<h2>").append(context).append("</h2>");
    }

    public StringBuffer appendNewLine(String context) {
        return sb.append("<br/>").append(context);
    }

    @Override
    public String toString() {
        return sb.toString();
    }

    public static void main(String[] args) {
        StringBuffer s = new StringBuffer();
        s.append("aaa").append("\n").append("bbb");
        System.out.println(s.toString());
    }
}
