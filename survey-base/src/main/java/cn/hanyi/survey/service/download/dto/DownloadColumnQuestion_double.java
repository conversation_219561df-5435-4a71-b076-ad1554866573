package cn.hanyi.survey.service.download.dto;

import cn.hanyi.survey.core.entity.SurveyResponseCell;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DownloadColumnQuestion_double extends DownloadColumnQuestion {

    public DownloadColumnQuestion_double(ResponseDownloadContext context, DownloadColumnQuestionGroup group) {
        super(context, group);
    }

    @Override
    public Object getCode(SurveyResponseCell cell) {
        return cell.getDoubleValue();
    }

    @Override
    public Object getLabel(SurveyResponseCell cell) {
        return cell.getDoubleValue();
    }
}