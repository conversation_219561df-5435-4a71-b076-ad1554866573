package cn.hanyi.survey.service.word;

import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/28 11:28:27
 */
@Component
public class DownloadScore implements IDownloadSurvey{

    @Override
    public QuestionType type() {
        return QuestionType.SCORE;
    }

    /*
            非常不满意 1 2 3 4 5 6 7 8 9 10 非常满意 不适用
     */
    @Override
    public String buildQuestion(SurveyQuestion question) {
        StringBuffer sb = new StringBuffer();

        List<String> labels = question.getLabels();
        for (int i = question.getMin(); i <= question.getMax(); i++) {
            sb.append(i).append(" ");
        }
        StringBuffer res = new StringBuffer();
        res.append(question.getCode()).append(".").append(question.getTitle()).append("["+question.getType().getText()+"]").append("<br/>");
        res.append(labels.get(0)).append(sb).append(labels.get(1));
        if(question.getInapplicable()){
            res.append(" 不适用");
        }
        return res.toString();
    }
}
