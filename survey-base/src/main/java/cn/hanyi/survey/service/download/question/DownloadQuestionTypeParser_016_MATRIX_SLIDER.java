package cn.hanyi.survey.service.download.question;

import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.core.entity.SurveyQuestionItem;
import cn.hanyi.survey.core.entity.SurveyResponseCell;
import cn.hanyi.survey.service.download.IDownloadQuestionTypeParser;
import cn.hanyi.survey.service.download.dto.DownloadColumnQuestion;
import cn.hanyi.survey.service.download.dto.DownloadColumnQuestionGroup;
import cn.hanyi.survey.service.download.dto.DownloadColumnQuestion_item;
import cn.hanyi.survey.service.download.dto.ResponseDownloadContext;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;

@Component
public class DownloadQuestionTypeParser_016_MATRIX_SLIDER implements IDownloadQuestionTypeParser {

    @Override
    public QuestionType type() {
        return QuestionType.MATRIX_SLIDER;
    }

    @Override
    public DownloadColumnQuestionGroup buildGroup(ResponseDownloadContext context, SurveyQuestion question) {
        DownloadColumnQuestionGroup group = new DownloadColumnQuestionGroup(question, question.getItems(), question.getColumns());
        AtomicInteger index = new AtomicInteger(1);
        group.getQuestionItems().forEach(i -> {
            int itemIndex = index.getAndIncrement();
            // 增加选项列，每个选项作为一列
            group.getColumns().add(new DownloadColumnQuestion_MATRIX_SLIDER_item(context, group, itemIndex, i));
        });
        //开启显示加总值，增加列
        if (question.getEnableTotal()) {
            group.getColumns().add(new DownloadColumnQuestion_MATRIX_SLIDER_item_total(context, group));
        }
        return group;
    }

    public static class DownloadColumnQuestion_MATRIX_SLIDER_item extends DownloadColumnQuestion_item {

        public DownloadColumnQuestion_MATRIX_SLIDER_item(ResponseDownloadContext context, DownloadColumnQuestionGroup group, int itemIndex, SurveyQuestionItem item) {
            super(context, group, itemIndex, item);
        }

        @Override
        public Object getCode(SurveyResponseCell cell) {
            return Optional.ofNullable(cell.getJsonValue()).map(m -> m.get(item.getValue())).filter(i -> i instanceof Number).map(j -> ((Number) j).doubleValue()).orElse(null);
        }

        @Override
        public Object getLabel(SurveyResponseCell cell) {
            return getCode(cell);
        }
    }

    public static class DownloadColumnQuestion_MATRIX_SLIDER_item_total extends DownloadColumnQuestion {
        public DownloadColumnQuestion_MATRIX_SLIDER_item_total(ResponseDownloadContext context, DownloadColumnQuestionGroup group) {
            super(context, group, context.getColumnSize(), group.getCode() + "_" + group.getTitle() + "_加总值", group.getCode() + "_sum");
        }

        @Override
        public Object getCode(SurveyResponseCell cell) {
            if (Objects.nonNull(cell.getJsonValue())) {
                return cell.getJsonValue().values().stream().filter(x -> x instanceof Number).mapToDouble(i -> ((Number) i).doubleValue()).sum();
            }
            return null;
        }

        @Override
        public Object getLabel(SurveyResponseCell cell) {
            return getCode(cell);
        }
    }

}
























