package cn.hanyi.survey.service.download.question;

import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.service.download.IDownloadQuestionTypeParser;
import cn.hanyi.survey.service.download.dto.DownloadColumnQuestionGroup;
import cn.hanyi.survey.service.download.dto.DownloadColumnQuestion_double;
import cn.hanyi.survey.service.download.dto.DownloadColumnQuestion_int;
import cn.hanyi.survey.service.download.dto.ResponseDownloadContext;
import org.springframework.stereotype.Component;

@Component
public class DownloadQuestionTypeParser_007_NUMBER implements IDownloadQuestionTypeParser {

    @Override
    public QuestionType type() {
        return QuestionType.NUMBER;
    }

    @Override
    public DownloadColumnQuestionGroup buildGroup(ResponseDownloadContext context, SurveyQuestion question) {
        DownloadColumnQuestionGroup group = new DownloadColumnQuestionGroup(question, question.getItems(), question.getColumns());
        group.getColumns().add(new DownloadColumnQuestion_double(context, group));
        return group;
    }

}
