package cn.hanyi.survey.service.download.dto;

import cn.hanyi.survey.core.entity.SurveyResponseCell;
import lombok.Getter;
import lombok.Setter;

import java.util.Arrays;
import java.util.Objects;
import java.util.stream.Collectors;

@Getter
@Setter
public class DownloadColumnQuestion_dynamic_item extends DownloadColumnQuestion {

    public DownloadColumnQuestion_dynamic_item(ResponseDownloadContext context, DownloadColumnQuestionGroup group) {
        super(context, group);
    }

    @Override
    public Object getCode(SurveyResponseCell cell) {
        String s = cell.getStrValue();
        if (s != null) {
            String[] ss = s.split(";");
            return Arrays.stream(ss).map(i -> Objects.toString(group.getItemIndexMap().get(i), null)).filter(Objects::nonNull).collect(Collectors.joining(","));
        }
        return null;
    }

    @Override
    public Object getLabel(SurveyResponseCell cell) {
        String s = cell.getStrValue();
        if (s != null) {
            String[] ss = s.split(";");
            return Arrays.stream(ss).map(i -> group.getItemTextMap().get(i)).filter(Objects::nonNull).collect(Collectors.joining(","));
        }
        return null;
    }
}