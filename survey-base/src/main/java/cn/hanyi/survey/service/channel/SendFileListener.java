package cn.hanyi.survey.service.channel;

import cn.hanyi.survey.core.constant.channel.ChannelType;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.read.listener.PageReadListener;

import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

public class Send<PERSON>ileListener extends PageReadListener<Map<Integer, String>> {

    private ChannelType channelType;

    private List<Map<Integer, String>> headers;

    public SendFileListener(List<Map<Integer, String>> headers, ChannelType channelType, Consumer<List<Map<Integer, String>>> consumer) {
        super(consumer);
        this.channelType = channelType;
        this.headers = headers;
    }
    public SendFileListener(List<Map<Integer, String>> headers, Consumer<List<Map<Integer, String>>> consumer , Integer batchCount) {
        super(consumer, batchCount);
        this.headers = headers;
    }

    @Override
    public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
        headMap.forEach((k, v) -> {
            String value = v.getStringValue();
            headers.add(Map.of(k, value));
        });
        super.invokeHead(headMap, context);
    }
}
