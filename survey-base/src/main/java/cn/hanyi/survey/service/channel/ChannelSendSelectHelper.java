package cn.hanyi.survey.service.channel;

import cn.hanyi.ctm.entity.Customer;
import cn.hanyi.ctm.service.CustomerService;
import cn.hanyi.survey.core.constant.channel.ChannelType;
import cn.hanyi.survey.core.dto.channel.ChannelSelectCustomerDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.service.DepartmentService;
import org.befun.extension.nativesql.SqlBuilder;
import org.befun.extension.service.NativeSqlHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static cn.hanyi.survey.core.constant.channel.ChannelType.*;

@Slf4j
@Service
public class ChannelSendSelectHelper extends ChannelSendDataHelper {

    @Autowired
    private NativeSqlHelper nativeSqlHelper;
    @Autowired
    private CustomerService customerService;
    @Autowired
    private DepartmentService departmentService;

    /**
     * 计算选择的客户数量
     */
    @Override
    public long countCustomers(Long orgId, Long userId, ChannelType channelType, ChannelSelectCustomerDto select) {
        SqlBuilder sqlBuilder = sqlBuilder(orgId, userId, channelType, select);
        if (sqlBuilder != null) {
            String sql = sqlBuilder.buildCountSql();
            log.info("count select customers sql: {}", sql);
            return nativeSqlHelper.count(sql);
        }
        return 0;
    }

    /**
     * 遍历选择的客户
     */
    @Override
    public void foreachCustomers(Long orgId, Long userId, ChannelType channelType, int pageSize, ChannelSelectCustomerDto select, Consumer<List<Customer>> consumerCustomers) {
        SqlBuilder sqlBuilder = sqlBuilder(orgId, userId, channelType, select);
        if (sqlBuilder != null) {
            String sql = sqlBuilder.buildCountSql();
            log.info("count select customers sql: {}", sql);
            long count = nativeSqlHelper.count(sql);
            pageable(count, pageSize, (page, size) -> {
                sqlBuilder.limit(page, size);
                return nativeSqlHelper.queryList(sqlBuilder, Customer::getId, (ignore, ids) -> customerService.getByIds(ids));
            }, consumerCustomers);
        }
    }

    public SqlBuilder sqlBuilder(Long orgId, Long userId, ChannelType channelType, ChannelSelectCustomerDto select) {
        if (!select.getAll()
                && !select.getNoDepartment()
                && CollectionUtils.isEmpty(select.getDepartmentIds())
                && CollectionUtils.isEmpty(select.getGroupIds())
                && CollectionUtils.isEmpty(select.getSelectedCustomerIds())
                && CollectionUtils.isEmpty(select.getExcludeCustomerIds())
        ) {
            return null;
        }
        boolean hasMobile = channelType == PHONE_MSG;
        boolean hasWechat = channelType == WECHAT_SERVICE;
        boolean hasEmail = channelType == EMAIL;
        // 排除的客户
        String excludeIds = joinListLong(select.getExcludeCustomerIds());
        // 选择的客户
        String includeIds = joinListLong(select.getSelectedCustomerIds());
        // 选择的组
        String groupIds = joinListLong(select.getGroupIds());
        // 选择的部门
        String departmentIds = joinListLong(select.filterDepartmentIds());
        String allDepartmentIdsJoinString = customerService.getAllDepartmentIdsJoinString(orgId, userId, null);
        SqlBuilder sqlBuilder = SqlBuilder.select("select distinct c.id,1 type from customer c")
                .countSelect("select count(distinct c.id) from customer c")
                .join("left join customer_group_relation cgr on c.id=cgr.customer_id").alwaysTrue()
                .where("c.org_id=%d", orgId).alwaysTrue()
                .where("c.is_delete=0").alwaysTrue()
                .where("c.mobile is not null and c.mobile != ''").ifTrue(hasMobile)
                .where("c.email is not null and c.email != ''").ifTrue(hasEmail)
                .where("c.thirdparty_customer_id is not null").ifTrue(hasWechat)
                .where("c.id not in (%s)", excludeIds).ifTrue(StringUtils.isNotEmpty(excludeIds));
        if (select.getAll()) { // 选择了全部，则只查询指定用户的所有客户
            sqlBuilder.where("(c.department_id in (%s))", allDepartmentIdsJoinString).alwaysTrue();
        } else {
            // 没有选择全部，其他的条件是 or 的关系
            sqlBuilder.where(or -> {
                if (select.getNoDepartment()) {
                    or.or("(c.department_id = 0 and cgr.id is null)");
                }
                if (StringUtils.isNotEmpty(groupIds)) {
                    // 分组条件需要把部门条件设置为全部
                    or.or("(cgr.group_id in (%s) and c.department_id in (%s))", groupIds, allDepartmentIdsJoinString);
                }
                if (StringUtils.isNotEmpty(departmentIds)) {
                    or.or("c.department_id in (%s)", departmentIds);
                }
                if (StringUtils.isNotEmpty(includeIds)) {
                    or.or("c.id in (%s)", includeIds);
                }
            }).alwaysTrue();
        }
        return sqlBuilder;
    }

    private String joinListLong(List<Long> listLong) {
        return joinListLong(listLong, null);
    }

    private String joinListLong(List<Long> listLong1, List<Long> listLong2) {
        Set<Long> list = new HashSet<>();
        if (CollectionUtils.isNotEmpty(listLong1)) {
            list.addAll(listLong1);
        }
        if (CollectionUtils.isNotEmpty(listLong2)) {
            list.addAll(listLong2);
        }
        return list.stream().filter(Objects::nonNull).map(Objects::toString).collect(Collectors.joining(","));
    }


}
