package cn.hanyi.survey.service.template;


import cn.hanyi.survey.core.constant.Template.TemplateType;
import cn.hanyi.survey.core.entity.template.TemplateGroup;
import cn.hanyi.survey.core.entity.template.TemplateGroupDto;
import cn.hanyi.survey.core.repository.TemplateGroupRepository;
import cn.hanyi.survey.core.repository.TemplateSurveyQuestionRepository;
import cn.hanyi.survey.core.repository.TemplateSurveyRepository;
import cn.hanyi.survey.dto.template.GroupAddRequestDto;
import cn.hanyi.survey.dto.template.GroupUpdateRequestDto;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.dto.query.ResourceQueryCriteria;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.BaseNoPageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class TemplateGroupService extends BaseNoPageService<TemplateGroup, TemplateGroupDto, TemplateGroupRepository> {

    @Autowired
    private TemplateGroupRepository templateGroupRepository;

    @Autowired
    private TemplateSurveyRepository templateSurveyRepository;

    @Autowired
    private TemplateSurveyQuestionRepository templateSurveyQuestionRepository;

    public TemplateGroup getEmptyGroup(TemplateType type) {
        Long orgId = TenantContext.getCurrentTenant();
        TemplateGroup defaultGroup = new TemplateGroup(0, "未分组", false, type);
        defaultGroup.setOrgId(orgId);
        defaultGroup.setId(0L);
        return defaultGroup;
    }

    public TemplateGroup getDefaultGroup(TemplateType type, Long groupId) {
        return getDefaultGroup(type).stream().filter(g -> g.getId().equals(groupId)).findFirst().orElse(null);
    }

    public List<TemplateGroup> getDefaultGroup(TemplateType type) {
        Long orgId = TenantContext.getCurrentTenant();
        TemplateGroup all = new TemplateGroup(-1, "全部", false, type);
        all.setOrgId(orgId);
        all.setId(-1L);
        return List.of(all, getEmptyGroup(type));
    }

    public List<Long> getAllGroupIds(TemplateType type) {
        List<TemplateGroup> list = templateGroupRepository.findByType(type, Sort.by("sequence"));
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream().map(TemplateGroup::getId).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public List<TemplateGroupDto> findAll(ResourceEntityQueryDto<TemplateGroupDto> queryDto) {
        ResourceQueryCriteria criteria = queryDto.getQueryCriteriaList().stream().filter(i -> i.getKey().equals("type")).findFirst().orElse(null);
        if (criteria == null || criteria.getValue() == null || !(criteria.getValue() instanceof TemplateType)) {
            throw new BadRequestException("type 参数错误");
        }
        TemplateType type = (TemplateType) criteria.getValue();
        queryDto.setSorts(Sort.by("sequence"));
        List<TemplateGroupDto> list = super.findAll(queryDto);
        if (list == null) {
            list = new ArrayList<>();
        }
        list.addAll(0, mapToDto(getDefaultGroup(type)));
        return list;
    }

    @Override
    public <S extends BaseEntityDTO<TemplateGroup>> TemplateGroupDto create(S data) {
        GroupAddRequestDto dto = (GroupAddRequestDto) data;
        Long orgId = TenantContext.getCurrentTenant();
        int sequence = dto.getSequence() == null ? 0 : dto.getSequence();
        String title = dto.getTitle() == null ? null : dto.getTitle().trim();
        if (StringUtils.isEmpty(title)) {
            throw new BadRequestException("分组名称不能为空");
        }
        long count = templateGroupRepository.countByType(dto.getType());
        if (count >= 8) {
            throw new BadRequestException("分组数量到达上限");
        }
        TemplateGroup entity = new TemplateGroup(sequence, title, true, dto.getType());
        entity.setOrgId(orgId);
        templateGroupRepository.save(entity);
        return mapToDto(entity);
    }

    @Override
    public <S extends BaseEntityDTO<TemplateGroup>> TemplateGroupDto updateOne(long id, S change) {
        GroupUpdateRequestDto dto = (GroupUpdateRequestDto) change;
        TemplateGroup entity = require(id);
        checkIsCurrentOrg(entity);
        String title = dto.getTitle() == null ? null : dto.getTitle().trim();
        if (title != null && StringUtils.isEmpty(title)) {
            throw new BadRequestException("分组名称不能为空");
        }
        Optional.ofNullable(dto.getSequence()).ifPresent(entity::setSequence);
        Optional.ofNullable(title).ifPresent(entity::setTitle);
        templateGroupRepository.save(entity);
        return mapToDto(entity);
    }

    @Override
    public Boolean deleteOne(long id) {
        TemplateGroup group = require(id);
        TemplateGroup emptyGroup = getEmptyGroup(group.getType());
        switch (group.getType()) {
            // 分组删除后需要将该分组下的问卷移动到未分组
            case SURVEY -> templateSurveyRepository.findAllByGroup(group).forEach(templateSurvey -> {
                templateSurvey.setGroup(emptyGroup);
                templateSurveyRepository.save(templateSurvey);
            });
            case QUESTION -> templateSurveyQuestionRepository.findAllByGroup(group).forEach(templateSurveyQuestion -> {
                templateSurveyQuestion.setGroup(emptyGroup);
                templateSurveyQuestionRepository.save(templateSurveyQuestion);
            });
            default -> {
            }
        }
        return super.deleteOne(id);
    }


}
