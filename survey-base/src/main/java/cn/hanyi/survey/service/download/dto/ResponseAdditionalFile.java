package cn.hanyi.survey.service.download.dto;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ResponseAdditionalFile {

    private String fileName;    // 原始文件名
    private int sameIndex;      // 原始文件名同名数量，
    private String filePath;    // 保存路径
    private String fileUrl;     // 远程文件地址
    private byte[] bytes;
    private String fullFileName;// 完整文件路径

    public ResponseAdditionalFile(String fileName, int sameIndex, String filePath, String fileUrl) {
        this.fileName = fileName;
        this.sameIndex = sameIndex;
        this.filePath = filePath;
        this.fileUrl = fileUrl;
        buildFullFileName();
    }

    public void buildFullFileName() {
        String replaceName = fileName;
        if (sameIndex > 0) {
            int index = fileName.lastIndexOf(".");
            if (index < 0) {
                replaceName = fileName + "_" + sameIndex;
            } else {
                replaceName = fileName.substring(0, index) + "_" + sameIndex + fileName.substring(index);
            }
        }
        fullFileName = filePath + "/" + replaceName;
    }

//    public static void main(String[] args) {
//        int sameIndex = 1;
//        String replaceName;
//        String fileName = "aaaajpg";
//        int index = fileName.lastIndexOf(".");
//        if (index < 0) {
//            replaceName = fileName + "_" + sameIndex;
//        } else {
//            replaceName = fileName.substring(0, index) + "_" + sameIndex + fileName.substring(index);
//        }
//        System.out.printf(replaceName);
//    }
}
