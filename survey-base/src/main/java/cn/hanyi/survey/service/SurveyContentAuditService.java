package cn.hanyi.survey.service;

import cn.hanyi.survey.core.constant.error.SurveyErrorCode;
import cn.hanyi.survey.core.constant.survey.SurveyStatus;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.exception.SurveyErrorException;
import cn.hanyi.survey.core.repository.SurveyRepository;
import cn.hanyi.survey.workertrigger.ISurveyEventTrigger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.auth.constant.ContentAuditStatus;
import org.befun.auth.constant.OrganizationConfigType;
import org.befun.auth.dto.orgconfig.OrgConfigDto;
import org.befun.auth.projection.SimpleContentAuditRecord;
import org.befun.auth.service.ContentAuditRecordService;
import org.befun.auth.service.OrganizationConfigService;
import org.befun.core.rest.context.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class SurveyContentAuditService {

    @Value("${survey.enable-content-audit:false}")
    private boolean enableSurveyContentAudit;
    @Autowired
    private SurveyRepository surveyRepository;
    @Autowired
    @Lazy
    private SurveyService surveyService;
    @Autowired
    private ISurveyEventTrigger surveyEventTrigger;
    @Autowired
    private ContentAuditRecordService contentAuditRecordService;
    @Autowired
    private OrganizationConfigService organizationConfigService;

    @Transactional(noRollbackFor = SurveyErrorException.class)
    public boolean checkContentAudit(long surveyId, SurveyStatus toStatus, boolean manualAuditIfFail) {
        Survey survey = surveyService.requireWithFilter(surveyId);
        SurveyStatus.checkToStatus(survey.getStatus(), toStatus);
        checkContentAudit(survey, manualAuditIfFail);
        return true;
    }

    private boolean isEnableSurveyContentAudit() {
        OrgConfigDto configDto = organizationConfigService.getOrDefaultConfig(OrganizationConfigType.surveyContentAudit);
        if (configDto == null || configDto.getSurveyContentAudit() == null) {
            return true;
        }
        return configDto.getSurveyContentAudit();
    }

    @Transactional(noRollbackFor = SurveyErrorException.class)
    public void checkContentAudit(Survey survey, boolean manualAuditIfFail) {
        if (enableSurveyContentAudit && isEnableSurveyContentAudit() && SurveyStatus.needToContentAuditing(survey.getStatus())) {
            List<String> texts = buildAuditTexts(survey);
            SimpleContentAuditRecord record = contentAuditRecordService.auditSurvey(survey.getId(), texts);
            boolean manual = false;
            if (record.getStatus() == ContentAuditStatus.noPass) {
                if (manualAuditIfFail) {
                    manual = true;
                } else {
                    throw new SurveyErrorException(false, SurveyErrorCode.SURVEY_CONTENT_AUDIT_NO_PASS);
                }
            } else if (record.getStatus() == ContentAuditStatus.unknown || record.getStatus() == ContentAuditStatus.suspected) {
                manual = true;
            }
            if (manual) {
                surveyEventTrigger.surveyContentAudit(TenantContext.requireCurrentTenant(), TenantContext.requireCurrentUserId(), survey.getId(), record.getId());
                survey.setStatus(SurveyStatus.CONTENT_AUDITING);
                surveyRepository.save(survey);
                throw new SurveyErrorException(false, SurveyErrorCode.SURVEY_CONTENT_AUDITING);
            }
        }
    }

    private List<String> buildAuditTexts(Survey survey) {
        List<String> texts = new ArrayList<>();
        Optional.ofNullable(survey.getTitle()).ifPresent(texts::add);
        Optional.ofNullable(survey.getRealTitle()).ifPresent(texts::add);
        Optional.ofNullable(survey.getWelcomingRemark()).ifPresent(texts::add);
        Optional.ofNullable(survey.getConcludingRemark()).ifPresent(texts::add);
        Optional.ofNullable(survey.getAbnormalConcludingRemark()).ifPresent(texts::add);
        if (CollectionUtils.isNotEmpty(survey.getQuestions())) {
            survey.getQuestions().forEach(q -> {
                Optional.ofNullable(q.getCode()).ifPresent(texts::add);
                Optional.ofNullable(q.getTitle()).ifPresent(texts::add);
                Optional.ofNullable(q.getLabels()).ifPresent(texts::addAll);
                Optional.ofNullable(q.getInapplicableLabel()).ifPresent(texts::add);
                Optional.ofNullable(q.getOtherLabel()).ifPresent(texts::add);
                Optional.ofNullable(q.getRemark()).ifPresent(texts::add);
                Optional.ofNullable(q.getPlaceHolder()).ifPresent(texts::add);
                Optional.ofNullable(q.getEvaluationValue()).ifPresent(texts::add);
                Optional.ofNullable(q.getAlias()).ifPresent(texts::add);
                Optional.ofNullable(q.getConfigure()).ifPresent(texts::add);
                if (CollectionUtils.isNotEmpty(q.getColumns())) {
                    q.getColumns().forEach(i -> {
                        Optional.ofNullable(i.getText()).ifPresent(texts::add);
                    });
                }
                if (CollectionUtils.isNotEmpty(q.getItems())) {
                    q.getItems().forEach(i -> {
                        Optional.ofNullable(i.getText()).ifPresent(texts::add);
                        Optional.ofNullable(i.getConfigure()).ifPresent(texts::add);
                    });
                }
            });
        }
        return texts;
    }
}

