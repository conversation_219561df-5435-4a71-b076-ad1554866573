package cn.hanyi.survey.service.download.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.*;

@Getter
@Setter
public class ResponseExportRandomFile extends ResponseExportFile {

    private int randomSize = 1;
    private int randomItemSize = 1;
    private DownloadColumnRandomGroup randomGroup;

    private List<List<String>> itemHeader = new ArrayList<>();
    private List<List<Object>> itemRows = new ArrayList<>();

    //每份答卷存一行（group，question） row[0]是答卷id
    private final LinkedHashMap<Long, Object[]> randomRowMap = new LinkedHashMap<>();
    //item
    private final LinkedHashMap<Long, Object[]> randomItemRowMap = new LinkedHashMap<>();

    //记录<选项id，标签>
    private final Map<Long, String> questionItemMap = new HashMap<>();


    public ResponseExportRandomFile(String fileName) {
        super(fileName);
    }

    public void plusRandomSize() {
        randomSize++;
    }

    public void plusItemRandomSize() {
        randomItemSize++;
    }
}
