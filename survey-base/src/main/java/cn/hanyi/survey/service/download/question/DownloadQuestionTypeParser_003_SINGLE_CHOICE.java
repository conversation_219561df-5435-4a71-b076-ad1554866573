package cn.hanyi.survey.service.download.question;

import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.core.entity.SurveyResponseCell;
import cn.hanyi.survey.service.QuestionsDynamicItemService;
import cn.hanyi.survey.service.download.IDownloadQuestionTypeParser;
import cn.hanyi.survey.service.download.dto.DownloadColumnQuestion;
import cn.hanyi.survey.service.download.dto.DownloadColumnQuestionGroup;
import cn.hanyi.survey.service.download.dto.DownloadColumnQuestion_dynamic_item;
import cn.hanyi.survey.service.download.dto.ResponseDownloadContext;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class DownloadQuestionTypeParser_003_SINGLE_CHOICE implements IDownloadQuestionTypeParser {

    @Autowired
    private QuestionsDynamicItemService questionsDynamicItemService;

    @Override
    public QuestionType type() {
        return QuestionType.SINGLE_CHOICE;
    }

    @Override
    public DownloadColumnQuestionGroup buildGroup(ResponseDownloadContext context, SurveyQuestion question) {
        if (question.getIsDynamicItem() != null && question.getIsDynamicItem()) {
            DownloadColumnQuestionGroup group = new DownloadColumnQuestionGroup(question, questionsDynamicItemService.findAllByQuestion(question.getId()), null);
            group.getColumns().add(new DownloadColumnQuestion_dynamic_item(context, group));
            return group;
        }
        DownloadColumnQuestionGroup group = new DownloadColumnQuestionGroup(question, question.getItems(), question.getColumns());
        // 增加选项列
        group.getColumns().add(new DownloadColumnQuestion_SINGLE_CHOICE_item(context, group));
        if (group.getQuestionItems().stream().anyMatch(i -> i.getEnableTextInput() != null && i.getEnableTextInput())) {
            // 增加选项中包含文本输入列
            group.getColumns().add(new DownloadColumnQuestion_SINGLE_CHOICE_comment(context, group));
        }
        return group;
    }

    public static class DownloadColumnQuestion_SINGLE_CHOICE_item extends DownloadColumnQuestion {

        public DownloadColumnQuestion_SINGLE_CHOICE_item(ResponseDownloadContext context, DownloadColumnQuestionGroup group) {
            super(context, group);
        }

        @Override
        public Object getCode(SurveyResponseCell cell) {
            return group.getItemIndexMap().get(cell.getStrValue());
        }

        @Override
        public Object getLabel(SurveyResponseCell cell) {
            return group.getItemTextMap().get(cell.getStrValue());
        }
    }

    public static class DownloadColumnQuestion_SINGLE_CHOICE_comment extends DownloadColumnQuestion {

        public DownloadColumnQuestion_SINGLE_CHOICE_comment(ResponseDownloadContext context, DownloadColumnQuestionGroup group) {
            super(context, group,
                    group.getCode() + "_" + group.getTitle() + "_文本输入",
                    group.getCode() + "_text");
        }

        @Override
        public Object getCode(SurveyResponseCell cell) {
            Map<String, Object> map = JsonHelper.toMap(cell.getCommentValue());
            return map == null ? null : map.get(cell.getStrValue());
        }

        @Override
        public Object getLabel(SurveyResponseCell cell) {
            return getCode(cell);
        }

    }
}
