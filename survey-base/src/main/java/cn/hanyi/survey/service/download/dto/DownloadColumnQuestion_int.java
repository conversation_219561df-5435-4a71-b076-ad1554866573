package cn.hanyi.survey.service.download.dto;

import cn.hanyi.survey.core.entity.SurveyResponseCell;
import cn.hanyi.survey.service.download.IDownloadColumn;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DownloadColumnQuestion_int extends DownloadColumnQuestion {

    public DownloadColumnQuestion_int(ResponseDownloadContext context, DownloadColumnQuestionGroup group) {
        super(context, group);
    }

    @Override
    public Object getCode(SurveyResponseCell cell) {
        return cell.getIntValue();
    }

    @Override
    public Object getLabel(SurveyResponseCell cell) {
        return cell.getIntValue();
    }
}