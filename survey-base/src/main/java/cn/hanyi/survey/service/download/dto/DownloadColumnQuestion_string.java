package cn.hanyi.survey.service.download.dto;

import cn.hanyi.survey.core.entity.SurveyResponseCell;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DownloadColumnQuestion_string extends DownloadColumnQuestion {

    public DownloadColumnQuestion_string(ResponseDownloadContext context, DownloadColumnQuestionGroup group) {
        super(context, group);
    }

    @Override
    public Object getCode(SurveyResponseCell cell) {
        return cell.getStrValue();
    }

    @Override
    public Object getLabel(SurveyResponseCell cell) {
        return cell.getStrValue();
    }
}
