package cn.hanyi.survey.service.download.dto;

import cn.hanyi.survey.core.entity.SurveyResponse;
import cn.hanyi.survey.service.download.IDownloadColumnGroup;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@Setter
public class DownloadColumnResponseGroup implements IDownloadColumnGroup<SurveyResponse, DownloadColumnResponse> {

    private final List<DownloadColumnResponse> columns = new ArrayList<>();

    public List<Integer> deleteColumns() {
        return columns.stream().filter(DownloadColumnResponse::needDelete).map(DownloadColumnResponse::getIndex).collect(Collectors.toList());
    }
}
