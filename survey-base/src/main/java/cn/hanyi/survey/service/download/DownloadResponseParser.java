package cn.hanyi.survey.service.download;

import cn.hanyi.survey.core.constant.channel.SurveyCollectorMethod;
import cn.hanyi.survey.core.entity.SurveyChannel;
import cn.hanyi.survey.core.entity.SurveyResponse;
import cn.hanyi.survey.service.download.dto.DownloadColumnResponse;
import cn.hanyi.survey.service.download.dto.DownloadColumnResponseGroup;
import cn.hanyi.survey.service.download.dto.ResponseDownloadContext;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.core.utils.DateHelper;
import org.befun.core.utils.JsonHelper;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.function.Function;

@Component
public class DownloadResponseParser {

    public void buildGroup(ResponseDownloadContext context) {
        DownloadColumnResponseGroup group = new DownloadColumnResponseGroup();
        List<DownloadColumnResponse> columns = group.getColumns();
        context.setResponseGroup(group);
        buildColumn(context, columns, false, false, "id", "id", SurveyResponse::getId);
        buildColumn(context, columns, true, false, "答卷编号", "QID", SurveyResponse::getSequence);
        buildColumn(context, columns, false, false, "回收类型", "send_type", r -> r.getCollectorMethod().getText());
        buildColumn(context, columns, false, false, "发送名称", "send_name", r -> Optional.ofNullable(context.getChannelMap().get(r.getChannelId())).map(SurveyChannel::getName).orElse(r.getCollectorMethod().getText()));
        buildColumn(context, columns, false, true, "姓名", "name", SurveyResponse::getName);
        buildColumn(context, columns, false, true, "手机号码", "phone", SurveyResponse::getPhone);
        buildColumn(context, columns, false, true, "邮箱", "mailbox", SurveyResponse::getEmail);
        buildColumn(context, columns, false, false, "提交时间", "end_time", r -> DateHelper.formatDateTime(r.getFinishTime()));
        buildColumn(context, columns, false, false, "开始时间", "start_time", r -> DateHelper.formatDateTime(r.getCreateTime()));
        buildColumn(context, columns, false, false, "答题状态", "result_code", r -> r.getStatus().getExtText());
        buildColumn(context, columns, false, false, "ip", "ip", SurveyResponse::getIp);
        buildColumn(context, columns, false, false, "操作系统", "os", SurveyResponse::getOs);
        buildColumn(context, columns, false, false, "浏览器", "browser", SurveyResponse::getBrowser);
        buildColumn(context, columns, true, false, "答题时长(秒)", "duration_seconds", r -> getDurationSeconds.apply(r));
        buildColumn(context, columns, true, false, "答题总时长(秒)", "duration_total", r -> (r.getFinishTime() != null && r.getCreateTime() != null) ? (r.getFinishTime().getTime() - r.getCreateTime().getTime()) / 1000 : null);
        buildColumn(context, columns, false, false, "国家(ip)", "ip_country", SurveyResponse::getCountry);
        buildColumn(context, columns, false, false, "省份(ip)", "ip_province", SurveyResponse::getProvince);
        buildColumn(context, columns, false, false, "城市(ip)", "ip_city", SurveyResponse::getCity);
        buildColumn(context, columns, false, true, "答卷标签", "tag", r -> {
            if (CollectionUtils.isEmpty(r.getTags())) {
                return null;
            } else {
                return String.join(",", r.getTags());
            }
        });
        buildColumn(context, columns, true, true, "总得分", "score", SurveyResponse::getTotalScore);
        buildColumn(context, columns, false, true, "客户ID", "customerId", SurveyResponse::getCustomerId);
        buildColumn(context, columns, false, true, "外部客户ID", "externalUserId", SurveyResponse::getExternalUserId);
        buildColumn(context, columns, false, true, "层级ID", "departmentId", SurveyResponse::getDepartmentId);
        buildColumn(context, columns, false, true, "部门名称", "departmentName", SurveyResponse::getDepartmentName);
        buildColumn(context, columns, false, true, "客户名称", "customerName", SurveyResponse::getCustomerName);
        buildColumn(context, columns, false, true, "客户性别", "customerGender", SurveyResponse::getCustomerGender);
        buildColumn(context, columns, false, true, "外部组织编号", "departmentCode", SurveyResponse::getDepartmentCode);
        buildColumn(context, columns, false, true, "外部企业ID", "externalCompanyId", SurveyResponse::getExternalCompanyId);
        buildColumn(context, columns, false, true, "默认参数a", "默认参数a", SurveyResponse::getDefaultPa);
        buildColumn(context, columns, false, true, "默认参数b", "默认参数b", SurveyResponse::getDefaultPb);
        buildColumn(context, columns, false, true, "默认参数c", "默认参数c", SurveyResponse::getDefaultPc);
        buildColumn(context, columns, false, true, "外部参数", "external_parameters", r -> {
            if (r.getParameters() != null && r.getParameters().size() > 0) {
                return JsonHelper.toJson(r.getParameters());
            } else {
                return null;
            }
        });
        buildColumn(context, columns, false, true, "微信openId", "openID", r -> {
            if (r.getCollectorMethod() == SurveyCollectorMethod.SURVEY_PLUS) {
                return null;
            } else {
                return r.getOpenid();
            }
        });
    }
    public void buildTemplateGroup(ResponseDownloadContext context) {
        DownloadColumnResponseGroup group = new DownloadColumnResponseGroup();
        List<DownloadColumnResponse> columns = group.getColumns();
        context.setResponseGroup(group);
        buildColumn(context, columns, false, false, "提交时间", "end_time", r -> DateHelper.formatDateTime(r.getFinishTime()));
        buildColumn(context, columns, false, false, "开始时间", "start_time", r -> DateHelper.formatDateTime(r.getCreateTime()));
        buildColumn(context, columns, false, false, "ip", "ip", SurveyResponse::getIp);
        buildColumn(context, columns, true, false, "答题时长(秒)", "duration_seconds", r -> getDurationSeconds.apply(r));
        buildColumn(context, columns, false, true, "外部客户ID", "externalUserId", SurveyResponse::getExternalUserId);
        buildColumn(context, columns, false, true, "外部组织编号", "departmentCode", SurveyResponse::getDepartmentCode);
        buildColumn(context, columns, false, true, "外部参数", "external_parameters", SurveyResponse::getParameters);
    }

    private void buildColumn(ResponseDownloadContext context, List<DownloadColumnResponse> columns, boolean numberValue, boolean deleteIfEmpty, String label, String code, Function<SurveyResponse, Object> getValue) {
        columns.add(new DownloadColumnResponse(context.getColumnSize(), label, code, numberValue, deleteIfEmpty, getValue));
        context.plusColumnSize();
        context.addCodeHeader(code);
        context.addLabelHeader(label);
    }


    Function<SurveyResponse, Object> getDurationSeconds = r -> {
        if (r.getFinishTime() != null && r.getCreateTime() != null) {
            long totalTime = (r.getFinishTime().getTime() - r.getCreateTime().getTime()) / 1000;
            //时长大于总时长，使用总时长数据
            if (r.getDurationSeconds() == null || r.getDurationSeconds() > totalTime) {
                return totalTime;
            } else {
                return r.getDurationSeconds();
            }
        }
        return r.getDurationSeconds();
    };
}
