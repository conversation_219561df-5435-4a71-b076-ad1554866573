package cn.hanyi.survey.service.download.ext;

import cn.hanyi.survey.client.service.SurveyBehaviorService;
import cn.hanyi.survey.core.entity.SurveyBehaviorRecord;
import cn.hanyi.survey.core.entity.SurveyResponse;
import cn.hanyi.survey.core.repository.SurveyBehaviorRecordRepository;
import cn.hanyi.survey.service.download.IDownloadExtHelper;
import cn.hanyi.survey.service.download.ResponseDownloadHelper;
import cn.hanyi.survey.service.download.dto.ResponseDownloadContext;
import cn.hanyi.survey.service.download.dto.ResponseDownloadFile;
import cn.hanyi.survey.service.download.dto.ResponseExportBehaviorFile;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.handler.WriteHandler;
import org.apache.commons.collections.CollectionUtils;
import org.befun.core.entity.BaseEntity;
import org.befun.core.repo.ResourcePermissionRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Component
public class DownloadBehaviorHelper implements IDownloadExtHelper<ResponseExportBehaviorFile> {

    @Autowired
    private SurveyBehaviorService behaviorService;

    @Autowired
    private SurveyBehaviorRecordRepository surveyBehaviorRecordRepository;
    @Autowired
    private ResourcePermissionRepository resourcePermissionRepository;

    @Override
    public DownloadExtType extType() {
        return DownloadExtType.behavior;
    }

    @Override
    public ResponseExportBehaviorFile buildFile(ResponseDownloadContext context) {
        if (surveyBehaviorRecordRepository.findFirstBySurveyId(context.getSurveyId()) != null) {
            return new ResponseExportBehaviorFile(context.getSafeTitle() + "_行为数据" + context.getDownloadType().getSuffix());
        }
        return null;
    }

    @Override
    public void download(ResponseDownloadContext context, ResponseExportBehaviorFile file, List<SurveyResponse> responseList, boolean sequential) {
        List<SurveyBehaviorRecord> records;
        if (sequential) {
            long min = responseList.get(0).getId();
            long max = responseList.get(responseList.size() - 1).getId();
            records = getRecordByRIdRange(context.getSurveyId(), min, max);
        } else {
            List<Long> rIds = responseList.stream().map(BaseEntity::getId).collect(Collectors.toList());
            records = getRecordByRIds(context.getSurveyId(), rIds);
        }
        if (CollectionUtils.isNotEmpty(records)) {
            records.forEach(i -> fillRecordValue(file, i));
        }
    }

    @Override
    public void copyColumns(ResponseDownloadContext context, ResponseExportBehaviorFile fileBehavior) {
        fileBehavior.getHeaders().add(List.of("id"));
        IntStream.range(1, fileBehavior.getMaxPage() + 1).forEach(i -> {
            fileBehavior.getHeaders().add(List.of("page" + i + "_duration"));
            fileBehavior.getHeaders().add(List.of("page" + i + "_return_times"));
        });

        fileBehavior.getBehaviorRowMap().values().forEach(originRow -> {
            List<Object> row = new ArrayList<>();
            IntStream.range(0, originRow.length).forEach(i -> {
                row.add(originRow[i]);
            });
            fileBehavior.getRows().add(row);
        });

        fileBehavior.getBehaviorRowMap().clear();
    }

    private void fillRecordValue(ResponseExportBehaviorFile file, SurveyBehaviorRecord record) {
        if (record.getResponseId() == null || record.getPage() == null || record.getPage() < 1) {
            return;
        }
        int maxPage = Math.max(file.getMaxPage(), record.getPage());
        int length = maxPage * 2 + 1; // 最大列数 页数*2 + 1
        file.setMaxPage(maxPage);
        int indexDuration = record.getPage() * 2 - 1;
        int indexReturnTimes = record.getPage() * 2;
        Object[] row = file.getBehaviorRowMap().get(record.getResponseId());
        if (row == null) {
            // 初始化这一行
            row = new Object[length];
            row[0] = record.getResponseId().toString();
            file.getBehaviorRowMap().put(record.getResponseId(), row);
        } else if (row.length < length) {
            // 列数不够，扩容
            Object[] copyRow = new Object[length];
            System.arraycopy(row, 0, copyRow, 0, row.length);
            row = copyRow;
            file.getBehaviorRowMap().put(record.getResponseId(), copyRow);
        }
        row[indexDuration] = record.getDuration();
        row[indexReturnTimes] = record.getReturnTimes();
    }

    private List<SurveyBehaviorRecord> getRecordByRIdRange(Long surveyId, long minRId, long maxRId) {
        return surveyBehaviorRecordRepository.findBySurveyIdAndResponseIdBetween(surveyId, minRId, maxRId);
    }

    private List<SurveyBehaviorRecord> getRecordByRIds(Long surveyId, List<Long> rIds) {
        return surveyBehaviorRecordRepository.findBySurveyIdAndResponseIdIn(surveyId, rIds);
    }

    @Override
    public void writeFile(ResponseDownloadContext context, List<ResponseDownloadFile> files, WriteHandler handler, ExcelTypeEnum type) throws IOException {
        ResponseExportBehaviorFile file = (ResponseExportBehaviorFile) context.getExtFileMap().get(extType());
        ResponseDownloadHelper.writeFile(files, file, handler, type);
    }
}
