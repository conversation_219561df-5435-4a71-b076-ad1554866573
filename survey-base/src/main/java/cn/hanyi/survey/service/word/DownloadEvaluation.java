package cn.hanyi.survey.service.word;

import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/3/28 11:28:27
 */
@Component
public class DownloadEvaluation implements IDownloadSurvey{

    @Override
    public QuestionType type() {
        return QuestionType.EVALUATION;
    }

    /*
            很不满意 不满意 一般 满意 很满意
            1.很不满意:标签 1 标签 2 标签 3 标签 4
            2.不满意:标签 1 标签 2
            写评价
     */
    @Override
    public String buildQuestion(SurveyQuestion question) {
        StringBuffer label = new StringBuffer();
        StringBuffer label2 = new StringBuffer();

        question.getItems().forEach(x->{
            label.append(x.getText()).append(" ");
            label2.append(x.getText()).append(":").append(x.getConfigure().replace(",", " "));
        });

        StringBuffer res = new StringBuffer();
        res.append(question.getCode()).append(".").append(question.getTitle()).append("["+question.getType().getText()+"]").append("<br/>");
        res.append(label).append("<br>").append(label2);
        return res.toString();
    }
}
