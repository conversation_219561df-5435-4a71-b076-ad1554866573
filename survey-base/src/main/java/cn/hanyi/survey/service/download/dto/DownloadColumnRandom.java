package cn.hanyi.survey.service.download.dto;

import cn.hanyi.survey.core.entity.SurveyRandomResult;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.concurrent.atomic.AtomicInteger;

@Setter
@Getter
@NoArgsConstructor
public class DownloadColumnRandom {

    protected AtomicInteger countValue = new AtomicInteger();

    private int index;
    private String header;

    public DownloadColumnRandom(int index, String header) {
        this.index = index;
        this.header = header;
    }

    public Object getCode(SurveyRandomResult result) {
        countValue.getAndIncrement();
        return result.getIsShow() != null && result.getIsShow() ? 1 : 0;
    }

}
