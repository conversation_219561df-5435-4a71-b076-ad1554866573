package cn.hanyi.survey.service;

import cn.hanyi.survey.core.constant.channel.SurveyCollectorMethod;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.utilis.QRCodeUtil;
import cn.hanyi.survey.dto.qrcode.DepartmentQrCodeDto;
import org.apache.commons.collections.CollectionUtils;
import org.befun.auth.dto.DepartmentTreeDto;
import org.befun.auth.entity.Department;
import org.befun.auth.service.DepartmentService;
import org.befun.auth.service.TreeConvertService;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.context.TenantContext;
import org.befun.extension.service.LinkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.function.Function;

@Service
public class SurveyQrCodeService {

    @Autowired
    private SurveyService surveyService;

    @Autowired
    private ResponseService responseService;

    @Autowired
    private LinkService linkService;

    @Autowired
    private DepartmentService departmentService;

    @Autowired
    private TreeConvertService treeConvertService;

    /**
     * 一店一码列表
     *
     * @param id
     */
    public ResourceResponseDto<List<DepartmentQrCodeDto>> departmentQrcode(long id) {
        long orgId = TenantContext.requireCurrentTenant();
        long userId = TenantContext.requireCurrentUserId();
        List<DepartmentTreeDto> trees = departmentService.childrenTreeByUser(orgId, userId);
        List<DepartmentQrCodeDto> list = treeConvertService.treeToList(trees, DepartmentQrCodeDto::mapFrom);
        if (CollectionUtils.isNotEmpty(list)) {
            list.parallelStream().forEach(i -> {
                String shortLink = toShortLink(id, i.getId());
                i.setShortLink(shortLink);

                Survey survey = surveyService.requireSurvey(id);
                long answersNum = responseService.countByDepartmentId(survey, i.getId());
                i.setAnswersNum(answersNum);
            });

        }
        List<DepartmentQrCodeDto> newTree = treeConvertService.listToTree(list, Function.identity());
        if (newTree == null) {
            newTree = new ArrayList<>();
        }
        return new ResourceResponseDto<>(newTree);
    }

    /**
     * 下载问卷通用答题链接二维码
     *
     * @param id
     */
    public void downloadSurveyQrcode(HttpServletResponse response, long id, Integer width, Integer height) throws Exception {
        Survey survey = surveyService.requireSurvey(id);
        String fileName = survey.getTitle();
        String shortLink = surveyService.getOrCreateLink(survey).getShortUrl();
        (new QRCodeUtil(width, height)).download(response, fileName, shortLink, null, true);
    }

    /**
     * 批量下载部门绑定的答题链接二维码
     *
     * @param id
     */
    public void downloadDepartmentQrcode(HttpServletResponse response, long id, Integer width, Integer height) throws Exception {
        Survey survey = surveyService.requireSurvey(id);
        String fileName = survey.getTitle();
        List<Map<String, String>> departmentContentList = new ArrayList<>();
        long orgId = TenantContext.requireCurrentTenant();
        long userId = TenantContext.requireCurrentUserId();
        List<DepartmentTreeDto> trees = departmentService.childrenTreeByUser(orgId, userId);
        if (trees != null) {
            setContentList(id, departmentContentList, trees);
            (new QRCodeUtil(width, height)).downloadToZip(response, fileName, departmentContentList, null, true);
        }
    }

    private void setContentList(long id, List<Map<String, String>> departmentContentList, List<DepartmentTreeDto> departments) {
        if (CollectionUtils.isNotEmpty(departments)) {
            for (DepartmentTreeDto department : departments) {
                long departmentId = department.getId();
                String departmentName = departmentId + "(" + department.getTitle() + ")";
                List<DepartmentTreeDto> childSubDepartments = department.getSubDepartments();

                String shortLink = toShortLink(id, department.getId()); //文卷答题链接（短链接）

                Map<String, String> qrcodeNameAndContent = new HashMap<>();
                qrcodeNameAndContent.put("name", departmentName);
                qrcodeNameAndContent.put("content", shortLink);
                departmentContentList.add(qrcodeNameAndContent);

                setContentList(id, departmentContentList, childSubDepartments);
            }
        }
    }

    /**
     * 下载部门绑定的答题链接二维码
     *
     * @param id
     * @param departmentId
     */
    public void downloadDepartmentQrcode(HttpServletResponse response, long id, long departmentId, Integer width, Integer height) throws Exception {
        Department department = departmentService.require(departmentId);
        String fileName = departmentId + "(" + department.getTitle() + ")";//文件名
        String shortLink = toShortLink(id, departmentId); //文卷答题链接（短链接）
        (new QRCodeUtil(width, height)).download(response, fileName, shortLink, null, true);
    }

    private String toShortLink(long id, Long departmentId) {
        Map<String, Object> params = new HashMap<>();
        if (departmentId != null) {
            Optional.ofNullable(departmentService.get(departmentId)).ifPresent(i -> params.put("departmentName", i.getTitle()));
            params.put("departmentId", departmentId);
            params.put("collectorMethod", SurveyCollectorMethod.SHOP_QRCODE.name());
        }
        return linkService.toShortUrl(id, params, true);
    }
}
