package cn.hanyi.survey.service.download.question;

import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.core.entity.SurveyQuestionItem;
import cn.hanyi.survey.core.entity.SurveyResponseCell;
import cn.hanyi.survey.service.download.IDownloadQuestionTypeParser;
import cn.hanyi.survey.service.download.dto.DownloadColumnQuestionGroup;
import cn.hanyi.survey.service.download.dto.DownloadColumnQuestion_item;
import cn.hanyi.survey.service.download.dto.ResponseDownloadContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;

@Component
public class DownloadQuestionTypeParser_014_MATRIX_CHOICE implements IDownloadQuestionTypeParser {

    @Override
    public QuestionType type() {
        return QuestionType.MATRIX_CHOICE;
    }

    @Override
    public DownloadColumnQuestionGroup buildGroup(ResponseDownloadContext context, SurveyQuestion question) {
        DownloadColumnQuestionGroup group = new DownloadColumnQuestionGroup(question, question.getItems(), question.getColumns());
        AtomicInteger index = new AtomicInteger(1);
        group.getQuestionItems().forEach(i -> {
            int itemIndex = index.getAndIncrement();
            // 增加选项列，每个选项作为一列
            group.getColumns().add(new DownloadColumnQuestion_MATRIX_CHOICE_item(context, group, itemIndex, i));
        });
        return group;
    }

    public static class DownloadColumnQuestion_MATRIX_CHOICE_item extends DownloadColumnQuestion_item {

        public DownloadColumnQuestion_MATRIX_CHOICE_item(ResponseDownloadContext context, DownloadColumnQuestionGroup group, int itemIndex, SurveyQuestionItem item) {
            super(context, group, itemIndex, item);
        }

        @Override
        public Object getCode(SurveyResponseCell cell) {
            String value = Optional.ofNullable(cell.getJsonValue()).map(m -> m.get(item.getValue())).map(Object::toString).orElse(null);
            if (StringUtils.isNotEmpty(value)) {
                return group.getColumnIndexMap().get(value);
            }
            return null;
        }

        @Override
        public Object getLabel(SurveyResponseCell cell) {
            String value = Optional.ofNullable(cell.getJsonValue()).map(m -> m.get(item.getValue())).map(Object::toString).orElse(null);
            if (StringUtils.isNotEmpty(value)) {
                return group.getColumnTextMap().get(value);
            }
            return null;
        }
    }
}
