package cn.hanyi.survey.service.template;

import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.entity.*;
import cn.hanyi.survey.core.entity.template.*;
import cn.hanyi.survey.core.repository.*;
import cn.hanyi.survey.core.utilis.RegularExpressionUtils;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Supplier;

@Service
@Slf4j
public class TemplateMapService {
    @Autowired
    private TemplateSurveyRepository templateSurveyRepository;
    @Autowired
    private TemplateSurveyLogicRepository templateSurveyLogicRepository;
    @Autowired
    private TemplateSurveyQuestionRepository templateSurveyQuestionRepository;
    @Autowired
    private TemplateSurveyQuestionItemRepository templateSurveyQuestionItemRepository;
    @Autowired
    private TemplateSurveyQuestionColumnRepository templateSurveyQuestionColumnRepository;
    @Autowired
    private TemplateSurveyQuestionRandomRepository templateSurveyQuestionRandomRepository;
    @Autowired
    private TemplateSurveyLanguageRepository templateSurveyLanguageRepository;
    @Autowired
    private SurveyRepository surveyRepository;
    @Autowired
    private SurveyLogicRepository surveyLogicRepository;
    @Autowired
    private SurveyQuestionRepository surveyQuestionRepository;
    @Autowired
    private SurveyQuestionItemRepository surveyQuestionItemRepository;
    @Autowired
    private SurveyQuestionColumnRepository surveyQuestionColumnRepository;
    @Autowired
    private SurveyQuestionRandomRepository surveyQuestionRandomRepository;
    @Autowired
    private SurveyLanguageRepository surveyLanguageRepository;

    // 填空题不需要去除html标签
    public static List<QuestionType> NotClearHtmlQuestionType = new ArrayList<QuestionType>() {{
        add(QuestionType.BLANK);
        add(QuestionType.MULTIPLE_BLANK);
    }};

    public TemplateSurvey mapToTemplate(Survey source, TemplateGroup group, String templateName, Boolean visibleIf, String remark) {
        Long userId = TenantContext.getCurrentUserId();
        Long orgId = TenantContext.getCurrentTenant();
        TemplateSurvey target = new TemplateSurvey();
        target.setUserId(userId);
        target.setOrgId(orgId);
        target.setGroup(group);
        target.setTitle(templateName);
        target.setRemark(remark);
        copySurvey(source, target);

        List<TemplateSurveyQuestionItem> tsqi = new ArrayList<>();
        List<TemplateSurveyQuestionColumn> tsqc = new ArrayList<>();
        List<TemplateSurveyQuestionRandom> tsqr = new ArrayList<>();
        List<TemplateSurveyLogic> tsl = new ArrayList<>();
        List<TemplateSurveyLanguage> languages = new ArrayList<>();


        Optional.ofNullable(source.getLogics()).ifPresent(l -> {
            List<TemplateSurveyLogic> ls = new ArrayList<>();
            target.setLogics(ls);
            l.forEach(s -> {
                TemplateSurveyLogic t = new TemplateSurveyLogic();
                t.setSurvey(target);
                copySurveyLogic(s, t);
                ls.add(t);
            });
            tsl.addAll(ls);
        });
        Optional.ofNullable(source.getQuestions()).ifPresent(l -> {
            List<TemplateSurveyQuestion> ls = new ArrayList<>();
            target.setQuestions(ls);
            l.forEach(s -> {
                TemplateSurveyQuestion t = new TemplateSurveyQuestion();
                t.setUserId(userId);
                t.setOrgId(orgId);
                t.setSurvey(target);
                copySurveyQuestion(s, t, visibleIf);
                ls.add(t);
                Optional.ofNullable(s.getItems()).ifPresent(ll -> {
                    List<TemplateSurveyQuestionItem> lls = new ArrayList<>();
                    t.setItems(lls);
                    ll.forEach(ss -> {
                        TemplateSurveyQuestionItem tt = new TemplateSurveyQuestionItem();
                        tt.setQuestion(t);
                        copySurveyQuestionItem(ss, tt, visibleIf);
                        lls.add(tt);
                    });
                    tsqi.addAll(lls);
                });

                Optional.ofNullable(s.getColumns()).ifPresent(ll -> {
                    List<TemplateSurveyQuestionColumn> lls = new ArrayList<>();
                    t.setColumns(lls);
                    ll.forEach(ss -> {
                        TemplateSurveyQuestionColumn tt = new TemplateSurveyQuestionColumn();
                        tt.setQuestion(t);
                        copySurveyQuestionColumn(ss, tt, visibleIf);
                        lls.add(tt);
                    });
                    tsqc.addAll(lls);
                });
            });
        });
        Optional.ofNullable(source.getRandoms()).ifPresent(l -> {
            List<TemplateSurveyQuestionRandom> ll = new ArrayList<>();
            target.setRandoms(ll);
            l.forEach(s -> {
                TemplateSurveyQuestionRandom t = new TemplateSurveyQuestionRandom();
                t.setSurvey(target);
                copySurveyQuestionRandom(s, t);
                ll.add(t);
            });
            tsqr.addAll(ll);
        });
        Optional.ofNullable(source.getLanguages()).ifPresent(l -> {
            List<TemplateSurveyLanguage> ls = new ArrayList<>();
            target.setLanguages(ls);
            l.forEach(s -> {
                TemplateSurveyLanguage t = new TemplateSurveyLanguage();
                t.setSurvey(target);
                copySurveyLanguage(s, t);
                ls.add(t);
            });
            languages.addAll(ls);
        });
        templateSurveyRepository.save(target);
        if (!tsqi.isEmpty()) {
            templateSurveyQuestionItemRepository.saveAll(tsqi);
        }
        if (!tsqc.isEmpty()) {
            templateSurveyQuestionColumnRepository.saveAll(tsqc);
        }
        if (!tsl.isEmpty()) {
            templateSurveyLogicRepository.saveAll(tsl);
        }
        if (!tsqr.isEmpty()) {
            templateSurveyQuestionRandomRepository.saveAll(tsqr);
        }
        if (!languages.isEmpty()) {
            templateSurveyLanguageRepository.saveAll(languages);
        }
        return target;
    }

    public Survey mapToSurvey(Long groupId, TemplateSurvey source, Boolean visibleIf) {
        Long userId = TenantContext.getCurrentUserId();
        Long orgId = TenantContext.getCurrentTenant();
        Survey target = new Survey();
        target.setGroupId(groupId);
        target.setUserId(userId);
        target.setOrgId(orgId);
        target.setTitle(source.getTitle());
        copySurvey(source, target);

        List<SurveyQuestionItem> tsqi = new ArrayList<>();
        List<SurveyQuestionColumn> tsqc = new ArrayList<>();
        List<SurveyQuestionRandom> tsqr = new ArrayList<>();
        List<SurveyLogic> tsl = new ArrayList<>();
        List<SurveyLanguage> languages = new ArrayList<>();

        Optional.ofNullable(source.getLogics()).ifPresent(l -> {
            List<SurveyLogic> ls = new ArrayList<>();
            target.setLogics(ls);
            l.forEach(s -> {
                SurveyLogic t = new SurveyLogic();
                t.setSurvey(target);
                copySurveyLogic(s, t);
                ls.add(t);
            });
            tsl.addAll(ls);
        });
        Optional.ofNullable(source.getQuestions()).ifPresent(l -> {
            List<SurveyQuestion> ls = new ArrayList<>();
            target.setQuestions(ls);
            l.forEach(s -> {
                SurveyQuestion t = new SurveyQuestion();
                t.setSurvey(target);
                copySurveyQuestion(s, t, visibleIf);
                ls.add(t);
                Optional.ofNullable(s.getItems()).ifPresent(ll -> {
                    List<SurveyQuestionItem> lls = new ArrayList<>();
                    t.setItems(lls);
                    ll.forEach(ss -> {
                        SurveyQuestionItem tt = new SurveyQuestionItem();
                        tt.setQuestion(t);
                        copySurveyQuestionItem(ss, tt, visibleIf);
                        lls.add(tt);
                    });
                    tsqi.addAll(lls);
                });
                Optional.ofNullable(s.getColumns()).ifPresent(ll -> {
                    List<SurveyQuestionColumn> lls = new ArrayList<>();
                    t.setColumns(lls);
                    ll.forEach(ss -> {
                        SurveyQuestionColumn tt = new SurveyQuestionColumn();
                        tt.setQuestion(t);
                        copySurveyQuestionColumn(ss, tt, visibleIf);
                        lls.add(tt);
                    });
                    tsqc.addAll(lls);
                });
            });
        });
        Optional.ofNullable(source.getRandoms()).ifPresent(l -> {
            List<SurveyQuestionRandom> ls = new ArrayList<>();
            target.setRandoms(ls);
            l.forEach(s -> {
                SurveyQuestionRandom t = new SurveyQuestionRandom();
                t.setSurvey(target);
                copySurveyQuestionRandom(s, t);
                ls.add(t);
            });
            tsqr.addAll(ls);
        });
        Optional.ofNullable(source.getLanguages()).ifPresent(l -> {
            List<SurveyLanguage> ls = new ArrayList<>();
            target.setLanguages(ls);
            l.forEach(s -> {
                SurveyLanguage t = new SurveyLanguage();
                t.setSurvey(target);
                copySurveyLanguage(s, t);
                ls.add(t);
            });
            languages.addAll(ls);
        });
        surveyRepository.save(target);
        if (!tsqi.isEmpty()) {
            surveyQuestionItemRepository.saveAll(tsqi);
        }
        if (!tsqc.isEmpty()) {
            surveyQuestionColumnRepository.saveAll(tsqc);
        }
        if (!tsl.isEmpty()) {
            surveyLogicRepository.saveAll(tsl);
        }
        if (!tsqr.isEmpty()) {
            surveyQuestionRandomRepository.saveAll(tsqr);
        }
        if (!languages.isEmpty()) {
            surveyLanguageRepository.saveAll(languages);
        }
        return target;
    }

    public TemplateSurveyQuestion mapToTemplate(SurveyQuestion source, TemplateSurveyQuestion target, Boolean visibleIf) {
        Long userId = TenantContext.getCurrentUserId();
        Long orgId = TenantContext.getCurrentTenant();
        copySurveyQuestion(source, target, visibleIf);

        List<TemplateSurveyQuestionItem> items = new ArrayList<>();
        source.getItems().forEach(surveyQuestionItem -> {
            TemplateSurveyQuestionItem i = new TemplateSurveyQuestionItem();
            copySurveyQuestionItem(surveyQuestionItem, i, visibleIf);
            i.setText(RegularExpressionUtils.replaceHtml(i.getText()));
            i.setQuestion(target);
            items.add(i);
        });
        List<TemplateSurveyQuestionColumn> columns = new ArrayList<>();
        source.getColumns().forEach(surveyQuestionColumn -> {
            TemplateSurveyQuestionColumn c = new TemplateSurveyQuestionColumn();
            copySurveyQuestionColumn(surveyQuestionColumn, c, visibleIf);
            c.setText(RegularExpressionUtils.replaceHtml(c.getText()));
            c.setQuestion(target);
            columns.add(c);
        });
        // 填空题需要保留html格式
        target.setTitle(NotClearHtmlQuestionType.contains(source.getType()) ? target.getTitle() : RegularExpressionUtils.replaceHtml(target.getTitle()));
        target.setItems(items);
        target.setColumns(columns);

        target.setUserId(userId);
        target.setOrgId(orgId);
        target.setSurvey(null);
        return target;
    }

    public SurveyQuestion mapToQuestion(TemplateSurveyQuestion source, Boolean visibleIf) {
        SurveyQuestion target = new SurveyQuestion();
        copySurveyQuestion(source, target, visibleIf);
        Optional.ofNullable(source.getItems()).ifPresent(l -> {
            List<SurveyQuestionItem> ls = new ArrayList<>();
            l.forEach(s -> {
                SurveyQuestionItem t = new SurveyQuestionItem();
                copySurveyQuestionItem(s, t, visibleIf);
                t.setQuestion(target);
                ls.add(t);
            });
            target.setItems(ls);
        });
        Optional.ofNullable(source.getColumns()).ifPresent(l -> {
            List<SurveyQuestionColumn> ls = new ArrayList<>();
            l.forEach(s -> {
                SurveyQuestionColumn t = new SurveyQuestionColumn();
                copySurveyQuestionColumn(s, t, visibleIf);
                t.setQuestion(target);
                ls.add(t);
            });
            target.setColumns(ls);
        });
        return target;
    }

    private String parseRemark(String remark) {
        Map<String, Object> map = null;
        try {
            map = JsonHelper.toMap(remark);
            map.put("type", "text");
            //link不保存模板
            map.put("link", null);
            return JsonHelper.toJson(map);
        } catch (Exception e) {
            throw new BadRequestException("结束语解析错误：" + remark);
        }
    }

    private void copySurvey(BaseSurvey source, BaseSurvey target) {
//        copyProperty(source::getOrgId, target::setOrgId);
        copyProperty(source::getWelcomingRemark, target::setWelcomingRemark);
        copyProperty(() -> parseRemark(source.getConcludingRemark()), target::setConcludingRemark);
        copyProperty(() -> parseRemark(source.getAbnormalConcludingRemark()), target::setAbnormalConcludingRemark);
        copyProperty(source::getLanguageSetting, target::setLanguageSetting);
        copyProperty(source::getLanguage, target::setLanguage);
        copyProperty(source::getStyle, target::setStyle);
        copyProperty(source::getEmbed, target::setEmbed);
//        copyProperty(source::getPageMode, target::setPageMode);
        copyProperty(source::getQuestionNumberMode, target::setQuestionNumberMode);
//        copyProperty(source::getEnableQuota, target::setEnableQuota);
//        copyProperty(source::getQuotaStatus, target::setQuotaStatus);
//        copyProperty(source::getEnableRedirect, target::setEnableRedirect);
//        copyProperty(source::getRedirectUrl, target::setRedirectUrl);
        copyProperty(source::getShowHeader, target::setShowHeader);
        copyProperty(source::getShowTitle, target::setShowTitle);
        copyProperty(source::getRealTitle, target::setRealTitle);
        copyProperty(source::getShowBackground, target::setShowBackground);
        copyProperty(source::getShowLogo, target::setShowLogo);
        copyProperty(source::getShowWelcoming, target::setShowWelcoming);
        copyProperty(source::getShowConcluding, target::setShowConcluding);
        copyProperty(source::getShowProgressBar, target::setShowProgressBar);
        copyProperty(source::getShowPageNumbers, target::setShowPageNumbers);
        copyProperty(source::getShowPreviousButton, target::setShowPreviousButton);
        copyProperty(source::getShowQuestionNumbers, target::setShowQuestionNumbers);
        copyProperty(source::getShowBrand, target::setShowBrand);
        copyProperty(source::getBrandLogo, target::setBrandLogo);
        copyProperty(source::getLogo, target::setLogo);
        copyProperty(source::getHeaderImagePc, target::setHeaderImagePc);
        copyProperty(source::getHeaderImageMobile, target::setHeaderImageMobile);
        copyProperty(source::getBackgroundImage, target::setBackgroundImage);
        copyProperty(source::getLogoFit, target::setLogoFit);
        copyProperty(source::getLogoPosition, target::setLogoPosition);
//        copyProperty(source::getEnableIpLimit, target::setEnableIpLimit);
//        copyProperty(source::getEnableDeviceLimit, target::setEnableDeviceLimit);
//        copyProperty(source::getEnableSmartVerify, target::setEnableSmartVerify);
//        copyProperty(source::getEnableResponseLimit, target::setEnableResponseLimit);
//        copyProperty(source::getResponseAmount, target::setResponseAmount);
    }

//    private void copySurvey(TemplateSurvey source, Survey target) {
//        copyProperty(source::getOrgId, target::setOrgId);
//        copyProperty(source::getWelcomingRemark, target::setWelcomingRemark);
//        copyProperty(source::getConcludingRemark, target::setConcludingRemark);
//        copyProperty(source::getAbnormalConcludingRemark, target::setAbnormalConcludingRemark);
////        copyProperty(source::getStatus, target::setStatus);
//        copyProperty(source::getLanguage, target::setLanguage);
//        copyProperty(source::getStyle, target::setStyle);
//        copyProperty(source::getEmbed, target::setEmbed);
//        copyProperty(source::getPageMode, target::setPageMode);
//        copyProperty(source::getQuestionNumberMode, target::setQuestionNumberMode);
//        copyProperty(source::getEnableQuota, target::setEnableQuota);
//        copyProperty(source::getQuotaStatus, target::setQuotaStatus);
//        copyProperty(source::getEnableRedirect, target::setEnableRedirect);
//        copyProperty(source::getRedirectUrl, target::setRedirectUrl);
//        copyProperty(source::getShowHeader, target::setShowHeader);
//        copyProperty(source::getShowTitle, target::setShowTitle);
//        copyProperty(source::getShowBackground, target::setShowBackground);
//        copyProperty(source::getShowLogo, target::setShowLogo);
//        copyProperty(source::getShowWelcoming, target::setShowWelcoming);
//        copyProperty(source::getShowConcluding, target::setShowConcluding);
//        copyProperty(source::getShowProgressBar, target::setShowProgressBar);
//        copyProperty(source::getShowPageNumbers, target::setShowPageNumbers);
//        copyProperty(source::getShowBrand, target::setShowBrand);
//        copyProperty(source::getLogo, target::setLogo);
//        copyProperty(source::getHeaderImagePc, target::setHeaderImagePc);
//        copyProperty(source::getHeaderImageMobile, target::setHeaderImageMobile);
//        copyProperty(source::getBackgroundImage, target::setBackgroundImage);
//        copyProperty(source::getLogoFit, target::setLogoFit);
//        copyProperty(source::getLogoPosition, target::setLogoPosition);
//        copyProperty(source::getEnableIpLimit, target::setEnableIpLimit);
//        copyProperty(source::getEnableDeviceLimit, target::setEnableDeviceLimit);
//        copyProperty(source::getEnableSmartVerify, target::setEnableSmartVerify);
//        copyProperty(source::getEnableResponseLimit, target::setEnableResponseLimit);
////        copyProperty(source::getResponseAmount, target::setResponseAmount);
//    }

    private void copySurveyQuestion(BaseQuestion source, BaseQuestion target, Boolean visibleIf) {
        copyProperty(source::getGroupCode, target::setGroupCode);
        copyProperty(source::getType, target::setType);
        copyProperty(source::getSequence, target::setSequence);
        copyProperty(source::getTitle, target::setTitle);
        copyProperty(source::getName, target::setName);
        copyProperty(source::getCode, target::setCode);
        copyProperty(source::getInputType, target::setInputType);
        copyProperty(source::getAreaType, target::setAreaType);
        copyProperty(source::getMin, target::setMin);
        copyProperty(source::getMax, target::setMax);
        copyProperty(source::getLabels, target::setLabels);
        copyProperty(source::getMinLength, target::setMinLength);
        copyProperty(source::getMaxLength, target::setMaxLength);
        copyProperty(source::getStepLength, target::setStepLength);
        copyProperty(source::getDecimalPlaces, target::setDecimalPlaces);
        copyProperty(source::getIsRequired, target::setIsRequired);
        copyProperty(source::getIsNps, target::setIsNps);
        copyProperty(source::getShowLabel, target::setShowLabel);
        copyProperty(source::getInapplicable, target::setInapplicable);
        copyProperty(source::getInapplicableLabel, target::setInapplicableLabel);
        copyProperty(source::getEnableValidator, target::setEnableValidator);
        copyProperty(source::getEnableTotal, target::setEnableTotal);
        copyProperty(source::getTotalType, target::setTotalType);
        copyProperty(source::getTotalValue, target::setTotalValue);
        copyProperty(source::getItemsOrder, target::setItemsOrder);
        copyProperty(source::getRandomLimit, target::setRandomLimit);
        copyProperty(source::getHasOther, target::setHasOther);
        copyProperty(source::getExcludeOther, target::setExcludeOther);
        copyProperty(source::getExcludeOtherLabel, target::setExcludeOtherLabel);
        copyProperty(source::getOtherLabel, target::setOtherLabel);
        copyProperty(source::getShowType, target::setShowType);
        copyProperty(source::getRemark, target::setRemark);
        copyProperty(source::getStartTime, target::setStartTime);
        copyProperty(source::getEndTime, target::setEndTime);
        copyProperty(source::getAdaptedMobile, target::setAdaptedMobile);
        copyProperty(source::getIsItemsGroup, target::setIsItemsGroup);
        if (visibleIf) {
            copyProperty(source::getVisibleIf, target::setVisibleIf);
        }
        copyProperty(source::getConfigure, target::setConfigure);
        copyProperty(source::getItemLayout, target::setItemLayout);
        copyProperty(source::getLabelLayout, target::setLabelLayout);
        copyProperty(source::getPlaceHolder, target::setPlaceHolder);
        copyProperty(source::getIsModify, target::setIsModify);
        copyProperty(source::getAlias, target::setAlias);
        copyProperty(source::getIsScore, target::setIsScore);
        copyProperty(source::getIsVerifyMobile, target::setIsVerifyMobile);
        copyProperty(source::getIsLinkCustomer, target::setIsLinkCustomer);
        copyProperty(source::getIsDeduplication, target::setIsDeduplication);
        copyProperty(source::getIsDynamicItem, target::setIsDynamicItem);
        copyProperty(source::getOtherCountryNumber, target::setOtherCountryNumber);
        copyProperty(source::getAreaCode, target::setAreaCode);
        copyProperty(source::getShowTags, target::setShowTags);
        copyProperty(source::getTitleShow, target::setTitleShow);
        copyProperty(source::getItemColumnExchange, target::setItemColumnExchange);
    }

//    private void copySurveyQuestion(TemplateSurveyQuestion source, SurveyQuestion target) {
//        copyProperty(source::getType, target::setType);
//        copyProperty(source::getSequence, target::setSequence);
//        copyProperty(source::getTitle, target::setTitle);
//        copyProperty(source::getName, target::setName);
//        copyProperty(source::getCode, target::setCode);
//        copyProperty(source::getInputType, target::setInputType);
//        copyProperty(source::getAreaType, target::setAreaType);
//        copyProperty(source::getMin, target::setMin);
//        copyProperty(source::getMax, target::setMax);
//        copyProperty(source::getLabels, target::setLabels);
//        copyProperty(source::getMinLength, target::setMinLength);
//        copyProperty(source::getMaxLength, target::setMaxLength);
//        copyProperty(source::getStepLength, target::setStepLength);
//        copyProperty(source::getDecimalPlaces, target::setDecimalPlaces);
//        copyProperty(source::getIsRequired, target::setIsRequired);
//        copyProperty(source::getIsNps, target::setIsNps);
//        copyProperty(source::getShowLabel, target::setShowLabel);
//        copyProperty(source::getInapplicable, target::setInapplicable);
//        copyProperty(source::getInapplicableLabel, target::setInapplicableLabel);
//        copyProperty(source::getVisibleIf, target::setVisibleIf);
//        copyProperty(source::getEnableValidator, target::setEnableValidator);
//        copyProperty(source::getItemsOrder, target::setItemsOrder);
//        copyProperty(source::getRandomLimit, target::setRandomLimit);
//        copyProperty(source::getHasOther, target::setHasOther);
//        copyProperty(source::getExcludeOther, target::setExcludeOther);
//        copyProperty(source::getExcludeOtherLabel, target::setExcludeOtherLabel);
//        copyProperty(source::getOtherLabel, target::setOtherLabel);
//        copyProperty(source::getShowType, target::setShowType);
//        copyProperty(source::getRemark, target::setRemark);
//        copyProperty(source::getStartTime, target::setStartTime);
//        copyProperty(source::getEndTime, target::setEndTime);
//        copyProperty(source::getAdaptedMobile, target::setAdaptedMobile);
//    }

    private void copySurveyQuestionItem(BaseQuestionItem source, BaseQuestionItem target, Boolean visibleIf) {
        copyProperty(source::getSequence, target::setSequence);
        copyProperty(source::getValue, target::setValue);
        copyProperty(source::getText, target::setText);
        if (visibleIf) {
            copyProperty(source::getVisibleIf, target::setVisibleIf);
        }
        copyProperty(source::getExcludeOther, target::setExcludeOther);
        copyProperty(source::getConfigure, target::setConfigure);
        copyProperty(source::getIsFix, target::setIsFix);
        copyProperty(source::getScore, target::setScore);
        copyProperty(source::getGroupCode, target::setGroupCode);
    }

//    private void copySurveyQuestionItem(TemplateSurveyQuestionItem source, SurveyQuestionItem target) {
//        copyProperty(source::getSequence, target::setSequence);
//        copyProperty(source::getValue, target::setValue);
//        copyProperty(source::getText, target::setText);
//        copyProperty(source::getVisibleIf, target::setVisibleIf);
//        copyProperty(source::getExcludeOther, target::setExcludeOther);
//    }

    private void copySurveyQuestionColumn(BaseQuestionColumn source, BaseQuestionColumn target, Boolean visibleIf) {
        copyProperty(source::getSequence, target::setSequence);
        copyProperty(source::getValue, target::setValue);
        if (visibleIf) {
            copyProperty(source::getVisibleIf, target::setVisibleIf);
        }
        copyProperty(source::getText, target::setText);
    }

//    private void copySurveyQuestionColumn(TemplateSurveyQuestionColumn source, SurveyQuestionColumn target) {
//        copyProperty(source::getSequence, target::setSequence);
//        copyProperty(source::getValue, target::setValue);
//        copyProperty(source::getText, target::setText);
//        copyProperty(source::getVisibleIf, target::setVisibleIf);
//    }

    private void copySurveyLogic(BaseLogic source, BaseLogic target) {
        copyProperty(source::getQuestionNames, target::setQuestionNames);
        copyProperty(source::getExpression, target::setExpression);
        copyProperty(source::getTarget, target::setTarget);
        copyProperty(source::getType, target::setType);
    }

    private void copySurveyLanguage(BaseSurveyLanguage source, BaseSurveyLanguage target) {
        copyProperty(source::getLanguage, target::setLanguage);
        copyProperty(source::getTitle, target::setTitle);
        copyProperty(source::getWelcomingRemark, target::setWelcomingRemark);
        copyProperty(source::getConcludingRemark, target::setConcludingRemark);
        copyProperty(source::getAbnormalConcludingRemark, target::setAbnormalConcludingRemark);
        copyProperty(source::getPersonalizedRemarks, target::setPersonalizedRemarks);
        copyProperty(source::getRealTitle, target::setRealTitle);
        copyProperty(source::getQuestions, target::setQuestions);
        copyProperty(source::getStatus, target::setStatus);
        copyProperty(source::getSourceQuestions, target::setSourceQuestions);
    }

    private void copySurveyQuestionRandom(BaseQuestionRandom source, BaseQuestionRandom target) {
        copyProperty(source::getNum, target::setNum);
        copyProperty(source::getType, target::setType);
        copyProperty(source::getQuestionNames, target::setQuestionNames);
    }

//    private void copySurveyLogic(TemplateSurveyLogic source, SurveyLogic target) {
//        copyProperty(source::getQuestionNames, target::setQuestionNames);
//        copyProperty(source::getExpression, target::setExpression);
//        copyProperty(source::getTarget, target::setTarget);
//        copyProperty(source::getType, target::setType);
//    }

    private <T> void copyProperty(Supplier<T> get, Consumer<T> set) {
        set.accept(get.get());
    }
}
