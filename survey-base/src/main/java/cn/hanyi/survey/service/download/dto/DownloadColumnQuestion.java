package cn.hanyi.survey.service.download.dto;

import cn.hanyi.survey.core.entity.SurveyResponseCell;
import cn.hanyi.survey.service.download.IDownloadColumn;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public abstract class DownloadColumnQuestion implements IDownloadColumn<SurveyResponseCell> {

    protected ResponseDownloadContext context;
    protected DownloadColumnQuestionGroup group;

    private int index;
    private String label;
    private String code;

    public DownloadColumnQuestion(ResponseDownloadContext context, DownloadColumnQuestionGroup group) {
        this(context, group, context.getColumnSize(), group.getCode() + "_" + group.getTitle(), group.getCode());
    }

    public DownloadColumnQuestion(ResponseDownloadContext context, DownloadColumnQuestionGroup group, String label, String code) {
        this(context, group, context.getColumnSize(), label, code);
    }

    public DownloadColumnQuestion(ResponseDownloadContext context, DownloadColumnQuestionGroup group, int index, String label, String code) {
        this.context = context;
        this.group = group;
        this.index = index;
        this.label = label;
        this.code = code;
        this.context.plusColumnSize();
        this.context.addCodeHeader(code);
        this.context.addLabelHeader(label);
    }
}
