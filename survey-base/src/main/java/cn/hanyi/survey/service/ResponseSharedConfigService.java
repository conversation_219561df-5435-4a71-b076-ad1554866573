package cn.hanyi.survey.service;

import cn.hanyi.survey.core.entity.ResponseSharedConfig;
import cn.hanyi.survey.core.entity.ResponseSharedConfigDto;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.repository.ResponseSharedConfigRepository;
import cn.hanyi.survey.core.repository.ResponseSharedRepository;
import cn.hanyi.survey.core.repository.SurveyResponseRepository;
import cn.hanyi.survey.dto.open.ResponseSharedParamsDto;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class ResponseSharedConfigService extends BaseService<ResponseSharedConfig, ResponseSharedConfigDto, ResponseSharedConfigRepository> {

    @Autowired
    private SurveyService surveyService;

    @Autowired
    private ResponseService responseService;

    @Autowired
    private SurveyResponseRepository responseRepository;

    @Autowired
    private ResponseSharedRepository responseSharedRepository;


    @Value("${xmplus.response-shared-expire-time:10}")
    private Integer expireTime;


    public ResponseSharedParamsDto updateOrCreateResponseSharedConfig(Long surveyId, ResponseSharedParamsDto paramsDto) {
        Survey survey = surveyService.requireSurvey(surveyId);

        ResponseSharedConfig responseSharedConfig = repository
                .findBySurvey(survey)
                .orElse(new ResponseSharedConfig(survey, false, false, false));

        Optional.ofNullable(paramsDto.getEnable()).ifPresent(enable -> {
            if (!enable) {
                responseSharedConfig.setWithToken(false);
            }
            responseSharedConfig.setEnable(enable);
        });

        Optional.ofNullable(paramsDto.getWithToken()).ifPresent(token -> {
            if (token) {
                responseSharedConfig.setEnable(true);
            }
            responseSharedConfig.setWithToken(token);
        });
        
        Optional.ofNullable(paramsDto.getHideQid()).ifPresent(responseSharedConfig::setHideQid);

        if (paramsDto.getIds() != null) {
            responseSharedConfig.setQids(paramsDto.getIds());
        }
        repository.save(responseSharedConfig);

        return new ResponseSharedParamsDto(
                responseSharedConfig.getEnable(),
                responseSharedConfig.getWithToken(),
                responseSharedConfig.getHideQid(),
                responseSharedConfig.getQids()
        );
    }


    public ResponseSharedParamsDto getResponseSharedConfig(Long surveyId) {
        Survey survey = surveyService.requireSurvey(surveyId);
        ResponseSharedParamsDto responseSharedParamsDto = new ResponseSharedParamsDto(false, false, false, List.of());
        repository.findBySurvey(survey).ifPresent(responseSharedConfig -> {
            responseSharedParamsDto.setEnable(responseSharedConfig.getEnable());
            responseSharedParamsDto.setHideQid(responseSharedConfig.getHideQid());
            responseSharedParamsDto.setWithToken(responseSharedConfig.getWithToken());
            responseSharedParamsDto.setIds(responseSharedConfig.getQids());
        });
        return responseSharedParamsDto;
    }

}
