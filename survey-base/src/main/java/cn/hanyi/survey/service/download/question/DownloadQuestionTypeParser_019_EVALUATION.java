package cn.hanyi.survey.service.download.question;

import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.core.entity.SurveyResponseCell;
import cn.hanyi.survey.core.utilis.RegularExpressionUtils;
import cn.hanyi.survey.service.download.IDownloadQuestionTypeParser;
import cn.hanyi.survey.service.download.dto.DownloadColumnQuestion;
import cn.hanyi.survey.service.download.dto.DownloadColumnQuestionGroup;
import cn.hanyi.survey.service.download.dto.ResponseDownloadContext;
import org.springframework.stereotype.Component;

@Component
public class DownloadQuestionTypeParser_019_EVALUATION implements IDownloadQuestionTypeParser {

    @Override
    public QuestionType type() {
        return QuestionType.EVALUATION;
    }

    @Override
    public DownloadColumnQuestionGroup buildGroup(ResponseDownloadContext context, SurveyQuestion question) {
        DownloadColumnQuestionGroup group = new DownloadColumnQuestionGroup(question, question.getItems(), question.getColumns());
        group.getColumns().add(new DownloadColumnQuestion_EVALUATION_item(context, group));
        group.getColumns().add(new DownloadColumnQuestion_EVALUATION_tag(context, group));
        group.getColumns().add(new DownloadColumnQuestion_EVALUATION_comment(context, group));
        return group;
    }

    public static class DownloadColumnQuestion_EVALUATION_item extends DownloadColumnQuestion {

        public DownloadColumnQuestion_EVALUATION_item(ResponseDownloadContext context, DownloadColumnQuestionGroup group) {
            super(context, group,
                    group.getCode() + "_" + group.getTitle() + "_评价",
                    group.getCode() + "_1");
        }

        @Override
        public Object getCode(SurveyResponseCell cell) {
            return group.getItemIndexMap().get(cell.getStrValue());
        }

        @Override
        public Object getLabel(SurveyResponseCell cell) {
            return group.getItemTextMap().get(cell.getStrValue());
        }
    }

    public static class DownloadColumnQuestion_EVALUATION_tag extends DownloadColumnQuestion {

        public DownloadColumnQuestion_EVALUATION_tag(ResponseDownloadContext context, DownloadColumnQuestionGroup group) {
            super(context, group,
                    group.getCode() + "_" + group.getTitle() + "_标签",
                    group.getCode() + "_2");
        }

        @Override
        public Object getCode(SurveyResponseCell cell) {
            return RegularExpressionUtils.replaceHtml(cell.getTags());
        }

        @Override
        public Object getLabel(SurveyResponseCell cell) {
            return RegularExpressionUtils.replaceHtml(cell.getTags());
        }
    }

    public static class DownloadColumnQuestion_EVALUATION_comment extends DownloadColumnQuestion {

        public DownloadColumnQuestion_EVALUATION_comment(ResponseDownloadContext context, DownloadColumnQuestionGroup group) {
            super(context, group,
                    group.getCode() + "_" + group.getTitle() + "_评价文本",
                    group.getCode() + "_3");
        }

        @Override
        public Object getCode(SurveyResponseCell cell) {
            return cell.getCommentValue();
        }

        @Override
        public Object getLabel(SurveyResponseCell cell) {
            return cell.getCommentValue();
        }

    }


}
