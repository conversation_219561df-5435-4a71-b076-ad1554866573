package cn.hanyi.survey.service.download.question;

import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.core.entity.SurveyResponseCell;
import cn.hanyi.survey.dto.QuestionDropDownDto;
import cn.hanyi.survey.service.download.IDownloadQuestionTypeParser;
import cn.hanyi.survey.service.download.dto.DownloadColumnQuestion;
import cn.hanyi.survey.service.download.dto.DownloadColumnQuestionGroup;
import cn.hanyi.survey.service.download.dto.ResponseDownloadContext;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.utils.JsonHelper;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

@Component
public class DownloadQuestionTypeParser_015_DROP_DOWN implements IDownloadQuestionTypeParser {

    @Override
    public QuestionType type() {
        return QuestionType.DROP_DOWN;
    }

    @Override
    public DownloadColumnQuestionGroup buildGroup(ResponseDownloadContext context, SurveyQuestion question) {
        DownloadColumnQuestionGroup group = new DownloadColumnQuestionGroup(question, question.getItems(), question.getColumns());
        AtomicInteger index = new AtomicInteger(1);
        getItems(question.getConfigure()).forEach(i -> {
            int itemIndex = index.getAndIncrement();
            group.getColumns().add(new DownloadColumnQuestion_DROP_DOWN_item(context, group,
                    group.getCode() + "_" + group.getTitle() + "_" + i,
                    group.getCode() + "_" + itemIndex,
                    itemIndex));
        });
        return group;
    }

    private List<String> getItems(String configure) {
        QuestionDropDownDto config = JsonHelper.toObject(configure, QuestionDropDownDto.class);
        if (config == null || config.getProps() == null) {
            return List.of("一级选项", "二级选项");
        } else {
            return config.getProps();
        }
    }

    public static class DownloadColumnQuestion_DROP_DOWN_item extends DownloadColumnQuestion {

        private final int itemIndex;

        public DownloadColumnQuestion_DROP_DOWN_item(ResponseDownloadContext context, DownloadColumnQuestionGroup group, String label, String code, int itemIndex) {
            super(context, group, label, code);
            this.itemIndex = itemIndex;
        }

        @Override
        public Object getCode(SurveyResponseCell cell) {
            String s = cell.getStrValue();
            if (StringUtils.isNotEmpty(s)) {
                int index = itemIndex - 1;
                String[] ss = s.split(";");
                if (ss.length > index) {
                    return ss[index];
                }
            }
            return null;
        }

        @Override
        public Object getLabel(SurveyResponseCell cell) {
            return getCode(cell);
        }
    }

}
