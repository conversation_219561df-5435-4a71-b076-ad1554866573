package cn.hanyi.survey.service;

import cn.hanyi.survey.core.constant.survey.SurveyVerifyStatus;
import cn.hanyi.survey.core.entity.SurveyVerifyRecord;
import cn.hanyi.survey.core.entity.SurveyVerifyRecordDto;
import cn.hanyi.survey.core.repository.SurveyVerifyRecordRepository;
import cn.hanyi.survey.dto.verifyRecord.SimpleUserInfo;
import cn.hanyi.survey.dto.verifyRecord.VerifyRecordResultDto;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.entity.User;
import org.befun.auth.service.UserService;
import org.befun.core.service.CustomEmbeddedService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SurveyVerifyRecordService extends CustomEmbeddedService<SurveyVerifyRecord, SurveyVerifyRecordDto, SurveyVerifyRecordRepository> {

    @Autowired
    private SurveyService surveyService;

    @Autowired
    SurveyVerifyRecordRepository verifyRecordRepository;

    @Autowired
    private UserService userService;

    @Override
    protected Object requireParent(long l) {
        return surveyService.require(l);
    }


    public List<VerifyRecordResultDto> findBySurveyId(Long sid) {
        List<SurveyVerifyRecord> records = verifyRecordRepository.findBySurveyId(sid, Sort.by("createTime").descending());
        List<VerifyRecordResultDto> list = new ArrayList<>();
        records.stream().forEach(x -> {
            VerifyRecordResultDto dto = new VerifyRecordResultDto();
            dto.setComment(x.getComment());
            dto.setCreateTime(x.getCreateTime());
            dto.setStatus(x.getOperation());
            User user = userService.get(x.getUserId());
            if (Objects.nonNull(user)) {
                SimpleUserInfo userInfo = new SimpleUserInfo();
                userInfo.setAvatar(user.getAvatar());
                userInfo.setName(user.getTruename());
                dto.setUser(userInfo);
            }
            list.add(dto);
        });
        return list;
    }

    public SurveyVerifyRecord findBySurveyIdAndVerifyVersionAndOperation(Long sid, Integer version, SurveyVerifyStatus operation) {
        return verifyRecordRepository.findBySurveyIdAndVerifyVersionAndOperation(sid, version, operation).orElse(null);
    }

}
