package cn.hanyi.survey.service.word;

import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import org.befun.core.utils.JsonHelper;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2023/3/28 11:28:27
 */
@Component
public class DownloadExperiment implements IDownloadSurvey{

    @Override
    public QuestionType type() {
        return QuestionType.EXPERIMENT;
    }

    /*
            1.颜色:白色 黑色 银色 金色
            2.屏幕大小:5.5 英寸 6 英寸 6.5 英寸
            3.摄像头像素:800 万 1200 万 1600 万
            组合 1
            组合 2
            组合 3
     */
    @Override
    public String buildQuestion(SurveyQuestion question) {
        StringBuffer res = new StringBuffer();
        res.append(question.getCode()).append(".").append(question.getTitle()).append("["+question.getType().getText()+"]").append("<br/>");

        List<Map> list = JsonHelper.toList(question.getConfigure(),Map.class);
        //属性
        AtomicInteger index = new AtomicInteger(1);
        list.stream().forEach(x->{
            res.append(index.get()).append(".").append(x.get("attribute")).append(":");
            List<Map> columns = (List<Map>)x.get("column");
            columns.stream().forEach(y->{
                res.append(y.get("value")).append(" ");
            });
            res.append("<br/>");
            index.getAndIncrement();
        });

        //组合
        question.getItems().forEach(x->{
            res.append(x.getText()).append("<br/>");
        });

        return res.toString();
    }

}















