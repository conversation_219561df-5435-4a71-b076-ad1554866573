package cn.hanyi.survey.service.channel;

import cn.hanyi.cem.core.dto.task.progress.TaskPageableDto;
import cn.hanyi.ctm.entity.Customer;
import cn.hanyi.survey.core.constant.channel.ChannelType;
import cn.hanyi.survey.core.dto.channel.ChannelSelectCustomerDto;
import cn.hanyi.survey.core.dto.channel.UserContactDto;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;

@Service
public class ChannelSendListHelper extends ChannelSendDataHelper {

    @Override
    public long countCustomers(Long orgId, Long userId, ChannelType channelType, ChannelSelectCustomerDto select) {
        if (CollectionUtils.isEmpty(select.getContacts())) {
            return 0;
        }
        return select.getContacts().size();
    }

    @Override
    public void foreachCustomers(Long orgId, Long userId, ChannelType channelType, int pageSize, ChannelSelectCustomerDto select, Consumer<List<Customer>> consumerCustomers) {
        if (CollectionUtils.isNotEmpty(select.getContacts())) {
            List<Customer> allCustomers = transformCustomers(channelType, select.getContacts());
            pageable(allCustomers.size(), pageSize, (page, size) -> TaskPageableDto.splitPageList(allCustomers, page, size), consumerCustomers);
        }
    }

    private List<Customer> transformCustomers(ChannelType channelType, List<UserContactDto> contacts) {
        List<Customer> allCustomers = new ArrayList<>();
        contacts.stream().filter(c -> {
            if (channelType == ChannelType.PHONE_MSG) {
                return StringUtils.isNotEmpty(c.getPhone());
            } else if (channelType == ChannelType.EMAIL) {
                return StringUtils.isNotEmpty(c.getEmail());
            } else if (channelType == ChannelType.WECHAT_SERVICE) {
                return StringUtils.isNotEmpty(c.getWechatAppId()) && StringUtils.isNotEmpty(c.getWechatOpenId());
            }
            return false;
        }).forEach(c -> {
            Customer customer = new Customer();
            customer.setMobile(c.getPhone());
            customer.setUsername(c.getUsername());
            customer.setEmail(c.getEmail());
            customer.getWechatParams().setAppId(c.getWechatAppId());
            customer.getWechatParams().setOpenId(c.getWechatOpenId());
            if (StringUtils.isNotEmpty(c.getExternalUserId())) {
                customer.setExternalUserId(c.getExternalUserId());
            }
            if (c.getDepartmentId() != null && c.getDepartmentId() > 0) {
                customer.getUrlInternalParams().setDepartmentId(c.getDepartmentId());
            }
            if (StringUtils.isNotEmpty(c.getDepartmentCode())) {
                customer.getUrlInternalParams().setDepartmentCode(c.getDepartmentCode());
            }
            if (StringUtils.isNotEmpty(c.getExpireTime())) {
                customer.getUrlInternalParams().setExpireTime(c.getExpireTime());
            }
            if (MapUtils.isNotEmpty(c.getParams())) {
                customer.getUrlCustomParams().putAll(c.getParams());
            }
            if (MapUtils.isNotEmpty(c.getContentParams())) {
                customer.getContentParams().putAll(c.getContentParams());
            }
            allCustomers.add(customer);
        });
        return allCustomers;
    }

}
