package cn.hanyi.survey.service.word;

import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/3/28 11:28:27
 */
@Component
public class DownloadArea implements IDownloadSurvey{

    @Override
    public QuestionType type() {
        return QuestionType.AREA;
    }

    /*
            1.省
            2.市
            3.区/县
            4.详细地址
     */
    @Override
    public String buildQuestion(SurveyQuestion question) {
        StringBuffer res = new StringBuffer();
        res.append(question.getCode()).append(".").append(question.getTitle()).append("["+question.getType().getText()+"]").append("<br/>");
        return res.append("省<br/>").append("市<br/>").append("区/县<br/>").append("详细地址").toString();
    }
}
