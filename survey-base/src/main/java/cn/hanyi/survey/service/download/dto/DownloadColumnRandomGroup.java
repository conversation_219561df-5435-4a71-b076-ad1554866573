package cn.hanyi.survey.service.download.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@Setter
public class DownloadColumnRandomGroup {


    //临时问题id，对应的列的索引和标题
    private final LinkedHashMap<Long, DownloadColumnRandom> columnMap = new LinkedHashMap<>();

    //临时选项
    private final LinkedHashMap<Long, List<DownloadColumnRandomItem>> columnItemMap = new LinkedHashMap<>();


    public List<Integer> deleteColumns() {
        return columnMap.values().stream().filter(i -> i.getCountValue().get() == 0).map(DownloadColumnRandom::getIndex).collect(Collectors.toList());
    }

    public List<String> originHeaders() {
        List<String> headers = columnMap.values().stream().map(DownloadColumnRandom::getHeader).collect(Collectors.toList());
        headers.add(0, "id");
        return headers;
    }

    public List<String> originItemHeaders() {
        List<String> headers = columnItemMap.values().stream().flatMap(Collection::stream).map(DownloadColumnRandom::getHeader).collect(Collectors.toList());
        headers.add(0, "id");
        return headers;
    }
}
