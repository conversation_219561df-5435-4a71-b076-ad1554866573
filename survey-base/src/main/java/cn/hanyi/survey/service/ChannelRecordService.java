package cn.hanyi.survey.service;

import cn.hanyi.cem.core.constant.CustomerSendFromType;
import cn.hanyi.survey.core.constant.channel.ChannelSendStatus;
import cn.hanyi.survey.core.constant.channel.ChannelType;
import cn.hanyi.survey.core.constant.error.SurveyErrorCode;
import cn.hanyi.survey.core.constant.survey.ReceiveStatus;
import cn.hanyi.survey.core.dto.channel.ChannelSendSearchDto;
import cn.hanyi.survey.core.dto.channel.ResendDto;
import cn.hanyi.survey.core.dto.channel.SendStatisticDto;
import cn.hanyi.survey.core.dto.channel.SurveySendRecordQueryDto;
import cn.hanyi.survey.core.entity.SurveyChannel;
import cn.hanyi.survey.core.entity.SurveySendRecord;
import cn.hanyi.survey.core.exception.SurveyErrorException;
import cn.hanyi.survey.core.repository.SurveySendRecordRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.service.UserTaskService;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.exception.BadRequestException;
import org.befun.core.exception.EntityNotFoundException;
import org.befun.core.utils.DateHelper;
import org.befun.extension.dto.SmsReceiveInfo;
import org.befun.task.constant.TaskStatus;
import org.befun.task.dto.TaskProgressDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import javax.transaction.Transactional;
import javax.validation.constraints.NotNull;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ChannelRecordService {

    @Autowired
    private ChannelService channelService;
    @Autowired
    private SurveySendRecordRepository surveySendRecordRepository;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private UserTaskService userTaskService;

    /**
     * 获取问卷渠道推送统计数据
     */
    public SendStatisticDto getSendStatistic(@NotNull long surveyId, @NotNull long channelId) {

        /**
         发送状态(0:待发送,1:发送成功,2:发送失败)
         答题状态(0:未访问,1:未提交,2:已提交)
         sendSuccessCount     //发送成功
         unSendCount          //待发送
         sendFailCount        //发送失败

         submitSuccessCount   //已提交
         unSubmitCount        //未提交
         unVisitCount         //未访问
         **/

        String sql = String.format("select " +
                "IFNULL(SUM(if(`send_status`= 1,1,0)),0) 'sendSuccessCount'," +
                "IFNULL(SUM(if(`send_status`= 0,1,0)),0) 'unSendCount'," +
                "IFNULL(SUM(if(`send_status`= 2,1,0)),0) 'sendFailCount'," +
                "IFNULL(SUM(if(`reply_status`= 0,1,0)),0) 'unVisitCount'," +
                "IFNULL(SUM(if(`reply_status`= 1,1,0)),0) 'unSubmitCount'," +
                "IFNULL(SUM(if(`reply_status`= 2,1,0)),0) 'submitSuccessCount' " +
                "from survey_send_record where channel_id=%d;", channelId);
        SendStatisticDto dto = jdbcTemplate.queryForObject(sql, new BeanPropertyRowMapper<>(SendStatisticDto.class));
        if (dto == null) {
            dto = new SendStatisticDto();
        }
        SurveyChannel channel = channelService.requireChannel(channelId);
        TaskProgressDto progress = userTaskService.progress(channel.getTaskProgressId());
        if (progress.getStatus() == TaskStatus.INIT) {
            dto.setTaskStatus(0);
        } else if (progress.getStatus() == TaskStatus.RUNNING) {
            dto.setTaskStatus(1);
        } else if (progress.getStatus() == TaskStatus.FAILED) {
            dto.setTaskStatus(3);
        } else {
            dto.setTaskStatus(2);
        }
        return dto;
    }

//    /**
//     * 渠道答题检测
//     *
//     * @param survey
//     * @param clientId
//     */
//    public void submitWithChannel(@NotNull Survey survey, @NotNull String clientId, @NotNull Long channelId, String openid) {
//        SurveyChannel channel = channelService.requireChannel(channelId);
//        surveyChannelVerify(survey, clientId, channel);
//        //渠道ip限制
//        responseService.submitIpLimitWithChannel(survey, channel);
//
//        if (channel.getType() == ChannelType.SHORT_LINK && channel.getEnableWechatReplyOnly()) {
//            if (openid.isEmpty()) throw new CommonException("微信openid不存在");
//            //开启了每个微信只能填答一次 需要验证该微信openid 是否已经填答了
//            responseService.submitWithOpenId(survey.getId(), openid);
//        }
//    }


//    /**
//     * 获取渠道问卷检测
//     *
//     * @param survey
//     * @param client_id
//     * @param channel
//     * @return
//     */
//    public SurveyChannel surveyChannelVerify(@NotNull Survey survey, @NotNull String client_id, @NotNull SurveyChannel channel) {
//        SurveyChannel returnSurveyChannel = new SurveyChannel();
//
//        //校验问卷渠道
//        if (!channel.getSid().equals(survey.getId())) {
//            throw new SurveyErrorException(SurveyErrorCode.CHANNEL_TYPE_ERROR);
//        }
//
//        // 判断渠道答题时间限制
//        if (channel.getEnableDateLimit()) {
//            String msg = channel.getStartTime() != null && new Date().before(channel.getStartTime()) ? "当前问卷的答题时间还未开始" :
//                    channel.getEndTime() != null && new Date().after(channel.getEndTime()) ? "当前问卷的答题时间已过期" : null;
//
//            if (msg != null) {
//                throw new DateLimitException(msg);
//            }
//        }
//
//        //校验渠道状态
//        switch (channel.getStatus()) {
//            case UNSET:
//            case COMPLETE:
//                throw new SurveyErrorException(SurveyErrorCode.CHANNEL_TYPE_COMPLETE);
//            case PAUSE:
//                throw new SurveyErrorException(SurveyErrorCode.CHANNEL_TYPE_PAUSE);
//            case RECOVERY:
//                break;
//            default:
//                throw new SurveyErrorException(SurveyErrorCode.CHANNEL_TYPE_ERROR);
//        }
//
//        //根据client_id 获取问卷推送记录 问卷推送只有短信和微信服务号渠道 才有记录
//        // 未访问,未提交,未完成
//        Optional<SurveySendRecord> optionalSurveySendRecord = surveySendRecordRepository.findByClientIdAndChannelId(client_id, channel.getId());
//        if (!optionalSurveySendRecord.isEmpty()) {
//            SurveySendRecord surveySendRecord = optionalSurveySendRecord.get();
//            //问卷已完成
//            if (surveySendRecord.getStatus() == ChannelSendStatus.COMPLETE)
//                throw new SurveyErrorException(SurveyErrorCode.ALREADY_SUBMIT);
//            //问卷已提交
//            if (surveySendRecord.getReplyStatus() == ReplyStatus.SUBMIT)
//                throw new SurveyErrorException(SurveyErrorCode.ALREADY_SUBMIT);
//
//            //如果问卷ReplyStatus是未访问状态 更新ReplyStatus为未提交
//            if (surveySendRecord.getReplyStatus() == ReplyStatus.UN_VISIT) {
//                surveySendRecord.setReplyStatus(ReplyStatus.UN_SUBMIT);
//                surveySendRecordRepository.save(surveySendRecord);
//            }
//            returnSurveyChannel = channel;
//        }
//        return returnSurveyChannel;
//    }
//

    /**
     * 删除推送记录
     *
     * @param surveyId
     * @param channelId
     * @param params
     * @return
     */
    @Transactional
    public ResourceResponseDto<Boolean> deleteSendRecord(long surveyId, long channelId, ResendDto params) {
        SurveyChannel surveyChannel = channelService.requireChannel(channelId);

        if (!surveyChannel.getSid().equals(surveyId))
            throw new SurveyErrorException(SurveyErrorCode.SURVEY_ERROR);
        params.appendLogIds(getSendRecordIds(channelId, params.getQueryDto()));
        if (params.getLogIds() != null && params.getLogIds().size() > 0)
            surveySendRecordRepository.deleteByIdIn(params.getLogIds());
        return new ResourceResponseDto<>(true);
    }

    /**
     * 下载渠道推送已完成名单
     */
    public void download(HttpServletResponse response, Long channelId) {

        SurveyChannel channel = channelService.requireChannel(channelId);
        ArrayList<String> headers;
        switch (channel.getType()) {
            case PHONE_MSG:
                headers = new ArrayList<>(Arrays.asList("姓名", "手机号码", "发送时间", "发送次数", "发送状态", "填答状态"));
                break;
            case WECHAT_SERVICE:
                headers = new ArrayList<>(Arrays.asList("姓名", "发送时间", "发送次数", "发送状态", "填答状态"));
                break;
            case EMAIL:
                headers = new ArrayList<>(Arrays.asList("姓名", "邮箱", "发送时间", "发送次数", "发送状态", "填答状态"));
                break;
            default:
                throw new BadRequestException();
        }

        Optional<List<SurveySendRecord>> surveySendRecordsOptional = surveySendRecordRepository.findByChannelIdAndStatus(channelId, ChannelSendStatus.COMPLETE);
        if (!surveySendRecordsOptional.isPresent()) {
            throw new EntityNotFoundException();
        }

        List<SurveySendRecord> surveySendRecords = surveySendRecordsOptional.get();

        int batchSize = 500;
        final ByteArrayOutputStream outB = new ByteArrayOutputStream();

        // 写入bom, 防止中文乱码
        byte[] bytes = {(byte) 0xEF, (byte) 0xBB, (byte) 0xBF};
        try {
            outB.write(bytes);

            try (Writer out = new BufferedWriter(new OutputStreamWriter(outB));) {
                CSVPrinter csvPrinter = null;
                CSVFormat csvFormat = CSVFormat.DEFAULT;
                csvPrinter = new CSVPrinter(out, csvFormat);
                csvPrinter.printRecord(headers);

                CSVPrinter finalCsvPrinter = csvPrinter;
                surveySendRecords.forEach(surveySendRecord -> {
                    try {
                        if (channel.getType() == ChannelType.PHONE_MSG || channel.getType() == ChannelType.EMAIL) {
                            finalCsvPrinter.printRecord(List.of(
                                    surveySendRecord.getName() == null ? "" : surveySendRecord.getName(),
                                    surveySendRecord.getAccount() == null ? "" : surveySendRecord.getAccount(),
                                    surveySendRecord.getSendTime() == null ? "" : DateHelper.formatDateTime(surveySendRecord.getSendTime()),
                                    surveySendRecord.getSendCount() == null ? "" : surveySendRecord.getSendCount().toString(),
                                    surveySendRecord.getSendStatus().getText(),
                                    surveySendRecord.getReplyStatus().getText())
                            );
                        } else if (channel.getType() == ChannelType.WECHAT_SERVICE) {
                            finalCsvPrinter.printRecord(List.of(
                                    surveySendRecord.getName() == null ? "" : surveySendRecord.getName(),
                                    surveySendRecord.getSendTime() == null ? "" : DateHelper.formatDateTime(surveySendRecord.getSendTime()),
                                    surveySendRecord.getSendCount() == null ? "" : surveySendRecord.getSendCount().toString(),
                                    surveySendRecord.getSendStatus().getText(),
                                    surveySendRecord.getReplyStatus().getText())
                            );
                        }

                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                });


                csvPrinter.flush();
                csvPrinter.close();
            } catch (Exception e) {
                e.printStackTrace();
                outB.close();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }


        ServletOutputStream outputStream = null;

        try {
            String tmpFile = String.format("%s.csv", channel.getName());
            String fileName = URLEncoder.encode(tmpFile, StandardCharsets.UTF_8.toString());

            response.reset();

            // 流跨域
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Allow-Methods", "POST, GET, OPTIONS, DELETE");
            response.setHeader("Access-Control-Max-Age", "3600");
            response.setHeader("Access-Control-Allow-Headers", "content-type, x-requested-with");
            response.setHeader("Access-Control-Allow-Credentials", "true");
            response.setHeader("Content-type", "text/html;charset=UTF-8");

            response.addHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setContentType("application/octet-stream");
            response.addHeader("Content-Disposition", "attachment;fileName=" + fileName);
            outputStream = response.getOutputStream();

            outputStream.write(outB.toByteArray());
            outputStream.flush();
            outputStream.close();

        } catch (UnsupportedEncodingException e) {
            log.error("{} build urlCode file name error:{}", channelId, e.getMessage());
            throw new BadRequestException("build urlCode file name error", e.getMessage());
        } catch (IOException e) {
            log.error("{} response outputStream error:{}", channelId, e.getMessage());
            throw new BadRequestException(" response outputStream error", e.getMessage());
        } finally {
            try {
                outputStream.close();
            } catch (IOException e) {
                log.error("{} close response outputStream error:{}", channelId, e.getMessage());
                throw new BadRequestException("close response outputStream error", e.getMessage());
            }
        }
    }

    /**
     * 根据搜索条件 查询批量处理的send record ids
     *
     * @param channelId
     * @param queryDto
     * @return
     */
    public List<Long> getSendRecordIds(Long channelId, SurveySendRecordQueryDto queryDto) {
        if (channelId == null || queryDto == null) return null;
        String where = "channel_id = " + channelId;
        if (queryDto.getStatus() != null) where += " and status = " + queryDto.getStatus().ordinal();
        if (queryDto.getSendStatus() != null) where += " and send_status = " + queryDto.getSendStatus().ordinal();
        if (queryDto.getReplyStatus() != null) where += " and reply_status = " + queryDto.getReplyStatus().ordinal();
        if (!StringUtils.isEmpty(queryDto.getQ()))
            where += " and (`name` like '%" + queryDto.getQ() + "%' or account like '%" + queryDto.getQ() + "%')";
        String sql = String.format("select id " +
                "from survey_send_record where %s;", where);
        List<ChannelSendSearchDto> dto = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(ChannelSendSearchDto.class));
        return dto.stream().map(x -> x.getId()).collect(Collectors.toList());
    }

    /**
     * 根据搜索条件 查询批量处理的send record ids
     */
    public int countSendRecordIds(Long channelId, SurveySendRecordQueryDto queryDto) {
        if (channelId == null || queryDto == null) return 0;
        String where = "channel_id = " + channelId;
        if (queryDto.getStatus() != null) where += " and status = " + queryDto.getStatus().ordinal();
        if (queryDto.getSendStatus() != null) where += " and send_status = " + queryDto.getSendStatus().ordinal();
        if (queryDto.getReplyStatus() != null) where += " and reply_status = " + queryDto.getReplyStatus().ordinal();
        if (!StringUtils.isEmpty(queryDto.getQ()))
            where += " and (`name` like '%" + queryDto.getQ() + "%' or account like '%" + queryDto.getQ() + "%')";
        String sql = String.format("select count(id) " +
                "from survey_send_record where %s;", where);
        Integer count = jdbcTemplate.queryForObject(sql, Integer.class);
        return count == null ? 0 : count;
    }

    /**
     * 短信发送完成后，第三发短信服务的回调结果
     */
    public void smsSendCallback(SmsReceiveInfo receiveInfo) {
        if (receiveInfo != null && CustomerSendFromType.SURVEY_CHANNEL.name().equals(receiveInfo.getFromType())) {
            SurveySendRecord record = surveySendRecordRepository.findById(receiveInfo.getFromId()).orElse(null);
            if (record != null && (record.getReceiveStatus() == null || record.getReceiveStatus() == ReceiveStatus.UNKNOWN)) {
                ReceiveStatus status = receiveInfo.isSuccess() ? ReceiveStatus.SUCCESS : ReceiveStatus.FAIL;
                record.setReceiveStatus(status);
                if (!receiveInfo.isSuccess()) {
                    record.setResult(receiveInfo.getMessage());
                }
                surveySendRecordRepository.save(record);
            }
        }

    }
}
