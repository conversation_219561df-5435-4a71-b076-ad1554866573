package cn.hanyi.survey.service.download.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration()
@ConfigurationProperties(prefix = "survey.download.attachment")
public class AttachmentProperties {
    /**
     * 免费版提示
     */
    private String freeMsg;
    /**
     * 付费版提示
     */
    private String paidMsg;
    /**
     * 免费版本最大支持附件下载大小
     */
    private Integer free;
    private Integer base;
    private Integer update;
    private Integer profession;
    /**
     * 系统容量 单位 G
     */
    private Integer systemCapacity;
    /**
     * 附件保存的临时目录
     */
    private String saveDir;
}
