package cn.hanyi.survey.service;

import cn.hanyi.survey.client.cache.SurveyCacheHelper;
import cn.hanyi.survey.client.dto.ClientSurvey;
import cn.hanyi.survey.core.dto.SubmitAdditionDataDto;
import cn.hanyi.survey.core.entity.*;
import cn.hanyi.survey.core.exception.SurveyErrorException;
import cn.hanyi.survey.core.repository.ResponseSharedConfigRepository;
import cn.hanyi.survey.core.repository.ResponseSharedRepository;
import cn.hanyi.survey.core.repository.SurveyResponseRepository;
import cn.hanyi.survey.dto.SurveyResponseDetailDto;
import cn.hanyi.survey.dto.open.ResponseSharedDetailDto;
import cn.hanyi.survey.dto.open.ResponseSharedParamsDto;
import cn.hanyi.survey.dto.open.ResponseSharedTokenDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.service.BaseService;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import static cn.hanyi.survey.core.constant.error.SurveyErrorCode.RESPONSE_SHARED_TOKEN_DISABLE;
import static cn.hanyi.survey.core.constant.error.SurveyErrorCode.RESPONSE_SHARED_VALID_TOKEN;

@Service
@Slf4j
public class ResponseSharedService extends BaseService<ResponseShared, ResponseSharedDto, ResponseSharedRepository> {

    @Autowired
    @Lazy
    private SurveyService surveyService;

    @Autowired
    private ResponseService responseService;

    @Autowired
    private SurveyResponseRepository responseRepository;

    @Autowired
    private SurveyCacheHelper surveyCacheHelper;

    @Autowired
    private ResponseSharedConfigRepository responseSharedConfigRepository;

    @Autowired
    private HttpServletResponse response;

    @Value("${xmplus.response-shared-expire-time:10}")
    private Integer expireTime;


    public Map<String, Object> getSurveyResponseSharedByToken(Long surveyId, Long responseId, String token) {
        var language = "zh-cn";
        Survey survey = surveyService.requireSurvey(surveyId);

        try {
            language = survey.getLanguage();
            SurveyResponse response = responseService.requireResponse(responseId);
            SurveyResponseDetailDto surveyResponseDetail = responseService.getSurveyResponseDetail(surveyId, responseId);

            ResponseSharedConfig responseSharedConfig = responseSharedConfigRepository.findBySurvey(survey).orElse(null);

            if (responseSharedConfig == null || !responseSharedConfig.getEnable()) {
                throw new SurveyErrorException(RESPONSE_SHARED_TOKEN_DISABLE);
            }

            if (responseSharedConfig.getWithToken()) {
                ResponseShared responseSharedToken = repository.findBySurveyAndResponse(survey, response).orElse(null);
                if (responseSharedToken != null && !validToken(new ResponseSharedTokenDto(responseSharedToken.getToken(), responseSharedToken.getExpireTime()), token)) {
                    throw new SurveyErrorException(RESPONSE_SHARED_VALID_TOKEN);
                }
            }

            Map<String, Object> cellData = responseService.buildSubmitCellData(surveyId, responseId);
            SubmitAdditionDataDto submitAdditionDataDto = JsonHelper.toObject(response.getAdditionData(), SubmitAdditionDataDto.class);

            ResponseSharedParamsDto responseSharedParamsDto = new ResponseSharedParamsDto(
                    responseSharedConfig.getEnable(),
                    responseSharedConfig.getWithToken(),
                    responseSharedConfig.getHideQid(),
                    responseSharedConfig.getQids()
            );

            ClientSurvey clientSurvey = surveyCacheHelper.getSurvey(surveyId);
            clientSurvey.setCellData(cellData);

            Map<String, Object> sharedResponse = JsonHelper.toMap(clientSurvey);
            sharedResponse.put("shareParams", responseSharedParamsDto);
            sharedResponse.put("additionData", submitAdditionDataDto == null ? new SubmitAdditionDataDto() : submitAdditionDataDto);
            sharedResponse.put("responseDetail", surveyResponseDetail);

            return sharedResponse;
        } finally {

            // 设置跨域响应头
            this.response.setHeader("Access-Control-Expose-Headers", "survey-language");
            this.response.setHeader("survey-language", language);
            this.response.setHeader("survey-language-setting", JsonHelper.toJson(survey.getLanguageSetting()));
        }
    }


    private Boolean validToken(ResponseSharedTokenDto rs, String token) {
        if (rs != null && StringUtils.isNotEmpty(rs.getToken()) && rs.getToken().equals(token)) {

            if (rs.getExpireTime() != null) {
                java.util.Date now = new java.util.Date();
                if (now.after(rs.getExpireTime())) {
                    return false;
                }
            }
            return true;
        }
        return false;
    }

    public ResponseSharedTokenDto getResponseSharedToken(Long surveyId, Long responseId, Boolean create, Optional<String> externalUserId) {
        ResponseShared responseShared = getResponseSharedEnable(surveyId, responseId, create);

        if (responseShared != null) {
            externalUserId.ifPresent(x -> {
                if (!x.equals(responseShared.getResponse().getExternalUserId())) {
                    throw new SurveyErrorException(RESPONSE_SHARED_VALID_TOKEN);
                }

            });

            if (responseShared.getToken() != null) {
                ResponseSharedTokenDto token = new ResponseSharedTokenDto(surveyId, responseId, responseShared.getToken(), responseShared.getExpireTime());
                if (!validToken(token, responseShared.getToken())) {
                    freshToken(responseShared);
                    token.setToken(responseShared.getToken());
                    token.setExpireTime(responseShared.getExpireTime());
                }
                return token;
            }
        }
        throw new SurveyErrorException(RESPONSE_SHARED_TOKEN_DISABLE);
    }

    private ResponseShared freshToken(ResponseShared responseShared) {
        responseShared.setToken(UUID.randomUUID().toString());
        responseShared.setExpireTime(new Date(System.currentTimeMillis() + expireTime * 60 * 1000));
        return repository.save(responseShared);
    }

    public String getResponseSharedStringToken(Long surveyId, Long responseId, Boolean create) {
        ResponseShared responseShared = getResponseSharedEnable(surveyId, responseId, create);
        if (responseShared != null) {
            return responseShared.getToken();
        }
        return null;
    }

    public ResponseSharedDetailDto getResponseSharedDetail(Long surveyId, Long responseId, Boolean create) {
        var survey = surveyService.require(surveyId);
        var response = responseService.requireResponse(responseId);


        ResponseSharedDetailDto responseSharedDetailDto = new ResponseSharedDetailDto();

        ResponseSharedConfig responseSharedConfig = responseSharedConfigRepository.findBySurvey(survey).orElse(null);

        if (responseSharedConfig != null) {
            responseSharedDetailDto.setEnable(responseSharedConfig.getEnable());
            responseSharedDetailDto.setWithToken(responseSharedConfig.getWithToken());
            responseSharedDetailDto.setHideQid(responseSharedConfig.getHideQid());

            if (responseSharedConfig.getWithToken()) {
                ResponseShared responseShared = repository.findBySurveyAndResponse(survey, response).orElse(null);
                responseSharedDetailDto.setToken(getResponseSharedStringToken(surveyId, responseId, create));
            }
        }

        return responseSharedDetailDto;
    }

    private ResponseShared getResponseSharedEnable(Long surveyId, Long responseId, Boolean create) {
        Survey survey = surveyService.requireSurvey(surveyId);
        SurveyResponse response = responseService.requireResponse(responseId);

        ResponseSharedConfig responseSharedConfig = responseSharedConfigRepository.findBySurveyAndEnableAndWithToken(survey, true, true).orElse(null);

        if (responseSharedConfig == null) {
            return null;
        }
        ResponseShared responseShared = repository.findBySurveyAndResponse(survey, response)
                .orElseGet(() -> {
                    return
                            create ? repository.save(new ResponseShared(
                                    survey,
                                    responseSharedConfig,
                                    response,
                                    UUID.randomUUID().toString(),
                                    new Date(System.currentTimeMillis() + expireTime * 60 * 1000))
                            ) : null;
                });
        if (responseShared != null && !validToken(new ResponseSharedTokenDto(responseShared.getToken(), responseShared.getExpireTime()), responseShared.getToken())) {
            freshToken(responseShared);
        }
        return responseShared;
    }
}
