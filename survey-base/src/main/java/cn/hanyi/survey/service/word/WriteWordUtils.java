package cn.hanyi.survey.service.word;

import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;

import java.math.BigInteger;

/**
 * <AUTHOR>
 * @date 2023/3/28 15:41:50
 */
public class WriteWordUtils {

    private XWPFDocument document = new XWPFDocument();


    public void writeSurveyTitle(String title){
        XWPFParagraph paragraph = document.createParagraph();
        XWPFRun run = paragraph.createRun();
        run.setText(title);
    }
    public void writeWelcome(){

    }
    public void writeQuestionTitle(){

    }

    public static void addCustomHeadingStyle(XWPFDocument docxDocument, String strStyleId, int headingLevel) {

        CTStyle ctStyle = CTStyle.Factory.newInstance();

        ctStyle.setStyleId(strStyleId);

        CTString styleName = CTString.Factory.newInstance();

        styleName.setVal(strStyleId);

        ctStyle.setName(styleName);

        CTDecimalNumber indentNumber = CTDecimalNumber.Factory.newInstance();

        indentNumber.setVal(BigInteger.valueOf(headingLevel));

// lower number > style is more prominent in the formats bar

        ctStyle.setUiPriority(indentNumber);

        CTOnOff onoffnull = CTOnOff.Factory.newInstance();

        ctStyle.setUnhideWhenUsed(onoffnull);

// style shows up in the formats bar

        ctStyle.setQFormat(onoffnull);

// style defines a heading of the given level

        CTPPr ppr = CTPPr.Factory.newInstance();

        ppr.setOutlineLvl(indentNumber);

        ctStyle.setPPr(ppr);

        XWPFStyle style = new XWPFStyle(ctStyle);

// is a null op if already defined

        XWPFStyles styles = docxDocument.createStyles();

        style.setType(STStyleType.PARAGRAPH);

        styles.addStyle(style);

    }

}
