package cn.hanyi.survey.service;

import cn.hanyi.ctm.dto.TemplateInfoDto;
import cn.hanyi.survey.core.constant.channel.ChannelStatus;
import cn.hanyi.survey.core.constant.channel.ChannelType;
import cn.hanyi.survey.core.dto.channel.*;
import cn.hanyi.survey.core.entity.SurveyChannel;
import cn.hanyi.survey.core.entity.SurveyChannelDto;
import cn.hanyi.survey.service.channel.ChannelSendHelper;
import cn.hanyi.survey.workertrigger.ISurveyTaskTrigger;
import cn.hanyi.survey.workertrigger.dto.SendChannelSurveyDto;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.constant.UserTaskType;
import org.befun.auth.service.UserTaskService;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.utils.DateHelper;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.sms.ISmsAccountService;
import org.befun.task.entity.TaskProgress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Slf4j
@Service
public class ChannelSendService {

    @Autowired
    private ChannelService channelService;
    @Autowired
    private ISmsAccountService smsAccountService;
    @Autowired
    private ChannelSendHelper channelSendHelper;
    @Autowired
    private UserTaskService userTaskService;
    @Autowired
    private ISurveyTaskTrigger surveyTaskTrigger;
    @Autowired
    private ChannelRecordService channelRecordService;

    /**
     * 计算选择的客户数量
     */
    public CountSendDto countSelectCustomers(long surveyId, long channelId, ChannelCountCustomerDto params) {
        Long orgId = TenantContext.getCurrentTenant();
        Long userId = TenantContext.getCurrentUserId();
        SurveyChannel channel = channelService.requireChannel(channelId);
        channelService.verifyChannelSurvey(channel, surveyId);
        return countSelectCustomers(orgId, userId, channel, params.isCalcCostSms(), params.getThirdPartTemplateId(), params.getContent(), params);
    }

    /**
     * 计算选择的客户数量
     */
    private CountSendDto countSelectCustomers(Long orgId, Long userId, SurveyChannel channel, boolean calcCostSms, Long thirdPartTemplateId, Map<String, Object> content, ChannelSelectCustomerDto params) {
        long count = channelSendHelper.countSelectCustomers(orgId, userId, channel.getType(), params);
        if (calcCostSms && channel.getType() == ChannelType.PHONE_MSG) {
            int availableBalance = smsAccountService.balance(orgId);
            TemplateInfoDto template = channelSendHelper.requireSmsTemplate(thirdPartTemplateId, content);
            return new CountSendDto((int) count, template.getSmsLength(), availableBalance);
        } else {
            return new CountSendDto((int) count, 0, 0);
        }
    }

    /**
     * 计算重发的客户数量
     */
    public CountSendDto countResendCustomers(long surveyId, long channelId, ChannelCountResendCustomerDto params) {
        Long orgId = TenantContext.getCurrentTenant();
        SurveyChannel channel = channelService.requireChannel(channelId);
        channelService.verifyChannelSurvey(channel, surveyId);
        int count;
        if (params.getAll()) {
            count = channelRecordService.countSendRecordIds(channelId, params.getQueryDto());
        } else {
            count = params.getLogIds().size();
        }
        if (params.isCalcCostSms() && channel.getType() == ChannelType.PHONE_MSG) {
            int availableBalance = smsAccountService.balance(orgId);
            TemplateInfoDto template = channelSendHelper.requireSmsTemplate(params.getThirdPartTemplateId(), params.getContent());
            return new CountSendDto(count, template.getSmsLength(), availableBalance);
        } else {
            return new CountSendDto(count, 0, 0);
        }
    }

    /**
     * 发送渠道问卷
     */
    public SurveyChannelDto send(long surveyId, long channelId, ChannelSendCustomerDto params) {
        Long orgId = TenantContext.getCurrentTenant();
        Long userId = TenantContext.getCurrentUserId();
        SurveyChannel channel = channelService.requireChannel(channelId);
        channelService.verifyChannelSurvey(channel, surveyId);
        if (params.getRequireChannelType() != null && params.getRequireChannelType() != channel.getType()) {
            throw new BadRequestException(String.format("渠道类型错误，期望是%s，实际是%s", params.getRequireChannelType().getText(), channel.getType().getText()));
        }
        if (channel.getType() == ChannelType.EMAIL) {
            if (params.getContent() == null || !params.getContent().containsKey("sender")) {
                throw new BadRequestException("content 必须包含sender（发送人）");
            }
        }
        channelSendHelper.checkTemplate(params.getThirdPartTemplateId(), params.getWechatTemplateId(), channel.getType(), params.getContent());
        CountSendDto countSendDto = countSelectCustomers(orgId, userId, channel, true, params.getThirdPartTemplateId(), params.getContent(), params);
        if (channel.getType() == ChannelType.PHONE_MSG) {
            checkSmsCost(countSendDto);
        }
        // 计算发送时间，渠道有发送时间，请求参数也有发送时间
        if (params.getSendTime() == null) {
            // 请求参数没有发送时间
            if (channel.getSendTime() != null) {
                // 渠道有，直接使用渠道的发送时间
                params.setSendTime(DateHelper.formatDateTime(channel.getSendTime()));
            }
        } else {
            // 请求参数也有发送时间，覆盖渠道的发送时间
            channel.setSendTime(params.parseSendTime());
        }

        params.setSurveyId(surveyId);
        params.setChannelId(channelId);
        String data = JsonHelper.toJson(params);
        if (data.getBytes().length > 60000) {
            throw new BadRequestException("请求参数数据太长，不能超过60000字节");
        }
        // 每次发送都生成一个任务，之前的直接覆盖掉
        TaskProgress progress = userTaskService.createTask(orgId, userId, UserTaskType.sendSurveyChannel, countSendDto.getCountCustomer(), params, channelId);
        SendChannelSurveyDto dto = new SendChannelSurveyDto(orgId, userId, surveyId, channelId, progress.getId(), data);
        surveyTaskTrigger.sendChannelSurvey(dto);

        //更新渠道状态为 已设置
        channel.setTaskProgressId(progress.getId());
        channel.setStatus(ChannelStatus.RECOVERY);
        channelService.save(channel);
        return channelService.mapToDto(channel);
    }

    /**
     * 校验短信额度是否足够
     */
    private void checkSmsCost(CountSendDto cost) {
        if (cost != null && cost.getAllCost() > 0 && cost.getBalance() >= cost.getAllCost()) {
            return;
        }
        throw new BadRequestException("短信余量不足");
    }

    /**
     * 再次发送
     */
    public boolean resendMsg(long surveyId, long channelId, ResendDto params) {
        Long orgId = TenantContext.getCurrentTenant();
        Long userId = TenantContext.getCurrentUserId();
        SurveyChannel channel = channelService.requireChannel(channelId);
        channelService.verifyChannelSurvey(channel, surveyId);
        channelSendHelper.checkTemplate(params.getThirdPartTemplateId(), params.getWechatTemplateId(), channel.getType(), params.getContent());
        params.appendLogIds(channelRecordService.getSendRecordIds(channelId, params.getQueryDto()));
        channelSendHelper.resend(orgId, userId, surveyId, params.getLogIds(), channel, params.getThirdPartTemplateId(), params.getWechatTemplateId(), params.getContent());
        return true;
    }

}
