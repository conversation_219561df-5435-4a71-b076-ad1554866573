package cn.hanyi.survey.service;

import org.geotools.data.DataStore;
import org.geotools.data.DataStoreFinder;
import org.geotools.data.FeatureSource;
import org.geotools.feature.FeatureCollection;
import org.geotools.feature.FeatureIterator;
import org.opengis.feature.simple.SimpleFeature;
import org.opengis.feature.simple.SimpleFeatureType;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * Shapefile属性检查器
 * 用于检查Shapefile中包含哪些属性
 */
public class ShapefileAttributeInspector {
    
    public static void inspectShapefile(String shapefilePath) throws IOException {
        File file = new File(shapefilePath);
        if (!file.exists()) {
            System.out.println("文件不存在: " + shapefilePath);
            return;
        }
        
        Map<String, Object> params = new HashMap<>();
        params.put("url", file.toURI().toURL());

        DataStore dataStore = null;
        try {
            dataStore = DataStoreFinder.getDataStore(params);
            if (dataStore == null) {
                throw new IOException("无法打开 Shapefile: " + shapefilePath);
            }

            String typeName = dataStore.getTypeNames()[0];
            FeatureSource<SimpleFeatureType, SimpleFeature> featureSource = dataStore.getFeatureSource(typeName);
            FeatureCollection<SimpleFeatureType, SimpleFeature> features = featureSource.getFeatures();

            System.out.println("=== Shapefile 信息 ===");
            System.out.println("文件路径: " + shapefilePath);
            System.out.println("类型名称: " + typeName);
            System.out.println("要素总数: " + features.size());
            System.out.println();

            // 获取要素类型信息
            SimpleFeatureType featureType = featureSource.getSchema();
            System.out.println("=== 属性结构 ===");
            for (int i = 0; i < featureType.getAttributeCount(); i++) {
                String attributeName = featureType.getDescriptor(i).getLocalName();
                Class<?> attributeType = featureType.getDescriptor(i).getType().getBinding();
                System.out.println("属性 " + (i + 1) + ": " + attributeName + " (类型: " + attributeType.getSimpleName() + ")");
            }
            System.out.println();

            // 检查前几个要素的实际数据
            try (FeatureIterator<SimpleFeature> iterator = features.features()) {
                int count = 0;
                while (iterator.hasNext() && count < 3) {
                    SimpleFeature feature = iterator.next();
                    count++;
                    
                    System.out.println("=== 要素 " + count + " 数据示例 ===");
                    System.out.println("要素ID: " + feature.getID());
                    
                    for (int i = 0; i < featureType.getAttributeCount(); i++) {
                        String attributeName = featureType.getDescriptor(i).getLocalName();
                        Object attributeValue = feature.getAttribute(i);
                        String valueStr = (attributeValue != null) ? attributeValue.toString() : "null";
                        
                        // 限制显示长度
                        if (valueStr.length() > 100) {
                            valueStr = valueStr.substring(0, 100) + "...";
                        }
                        
                        System.out.println("  " + attributeName + ": " + valueStr);
                    }
                    System.out.println();
                }
            }
            
        } finally {
            if (dataStore != null) {
                dataStore.dispose();
            }
        }
    }

    public static void main(String[] args) {
        try {
            String shapefilePath = "/Users/<USER>/Downloads/city.shp";
            inspectShapefile(shapefilePath);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
