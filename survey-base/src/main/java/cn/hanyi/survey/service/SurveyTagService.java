package cn.hanyi.survey.service;

import cn.hanyi.survey.core.constant.survey.SurveyQuotaStatus;
import cn.hanyi.survey.core.constant.survey.SurveyStatus;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyTag;
import cn.hanyi.survey.core.entity.SurveyTagDto;
import cn.hanyi.survey.core.repository.SurveyQuotaRepository;
import cn.hanyi.survey.core.repository.SurveyTagRepository;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.service.CustomEmbeddedService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SurveyTagService extends CustomEmbeddedService<SurveyTag, SurveyTagDto, SurveyTagRepository> {

    @Autowired
    private SurveyQuotaRepository quotaRepository;

    @Autowired
    private SurveyService surveyService;

    @Autowired
    private ResponseService responseService;

    @Override
    protected Object requireParent(long l) {
        return surveyService.require(l);
    }

    /**
     * 开启配额
     * 如果该问卷下没有答题数据,就不用跑配额
     * 如果有答题数据，就需要异步跑配额
     *
     * @param survey
     */
    public void enable(Survey survey) {
        survey.setEnableQuota(true);
        survey.setStatus(SurveyStatus.STOPPED);
        //如果配额不为空 启用问卷的时候 需要重新计算配额
        if (!survey.getQuotas().isEmpty()) {
            survey.setQuotaStatus(SurveyQuotaStatus.CALCULATE_INIT);
        }
        surveyService.save(survey);
    }

    /**
     * 关闭配额
     *
     * @param survey
     */
    public void disable(Survey survey) {
        survey.setEnableQuota(false);
        surveyService.save(survey);
    }

//    /**
//     * 判断所有配额是否命中 且命中的配额是否已经达标
//     *
//     * @param survey
//     * @param responseId
//     * @return
//     */
//    public Boolean hasQuota(@NotNull Survey survey, Long responseId) {
//        List<SurveyQuota> quotaList = responseService.quotaTriggerResponse(survey.getQuotas(), responseId);
//        //未命中配额继续答题
//        if (quotaList.isEmpty()) return false;
//        return quotaAllMatchLimit.hasQuota(survey.getId(), quotaList.stream().map(SurveyQuota::getId).collect(Collectors.toList()));
//    }

}
