package cn.hanyi.survey.service;

import cn.hanyi.survey.client.service.WechatConfigureService;
import cn.hanyi.survey.core.constant.channel.ChannelResendStatus;
import cn.hanyi.survey.core.constant.channel.ChannelStatus;
import cn.hanyi.survey.core.constant.channel.ChannelType;
import cn.hanyi.survey.core.constant.channel.SurveyCollectorMethod;
import cn.hanyi.survey.core.constant.error.SurveyErrorCode;
import cn.hanyi.survey.core.constant.question.FormatType;
import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.dto.channel.ShortLinkDto;
import cn.hanyi.survey.core.dto.ext.SurveyEmbedExtDto;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyChannel;
import cn.hanyi.survey.core.entity.SurveyChannelDto;
import cn.hanyi.survey.core.exception.SurveyErrorException;
import cn.hanyi.survey.core.projection.SimpleResponse3;
import cn.hanyi.survey.core.repository.SurveyChannelRepository;
import cn.hanyi.survey.core.utilis.EasyExcelUtils;
import cn.hanyi.survey.dto.survey.ContactsDto;
import cn.hanyi.survey.dto.survey.SurveyPlusConfigureDto;
import cn.hanyi.survey.dto.survey.SurveyPlusOrderPriceDto;
import cn.hanyi.survey.properties.ContactsProperties;
import cn.hanyi.survey.workertrigger.ISurveyEventTrigger;
import cn.hanyi.survey.workertrigger.SurveyTaskTrigger;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.befun.auth.constant.UserTaskType;
import org.befun.auth.pay.constant.OrderStatus;
import org.befun.auth.pay.constant.OrderType;
import org.befun.auth.pay.constant.PayServiceRate;
import org.befun.auth.pay.entity.OrganizationOrder;
import org.befun.auth.pay.property.PayProperties;
import org.befun.auth.pay.service.OrganizationOrderService;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.UserService;
import org.befun.auth.service.UserTaskService;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.exception.BadRequestException;
import org.befun.core.exception.EntityNotFoundException;
import org.befun.core.rest.context.EmbeddedFieldContext;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.CustomEmbeddedService;
import org.befun.core.utils.DateHelper;
import org.befun.core.utils.EntityUtility;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.constant.XPackAppType;
import org.befun.extension.entity.XpackConfig;
import org.befun.extension.service.LinkService;
import org.befun.extension.service.XpackConfigService;
import org.befun.extension.sms.ISmsAccountService;
import org.befun.task.entity.TaskProgress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ChannelService extends CustomEmbeddedService<SurveyChannel, SurveyChannelDto, SurveyChannelRepository> {

    @Autowired
    private SurveyService surveyService;

    @Autowired
    private ResponseService responseService;

    @Autowired
    private SurveyChannelRepository channelRepository;

    @Autowired
    private LinkService linkService;

    @Lazy
    @Autowired
    private QuotaService quotaService;

    @Autowired
    private UserTaskService userTaskService;

    @Autowired
    private ISmsAccountService smsAccountService;

    @Autowired(required = false)
    private WechatConfigureService wechatConfigureService;

    @Autowired
    private UserService userService;

    @Autowired
    private SurveyTaskTrigger surveyTaskTrigger;

    @Autowired
    private ContactsProperties contactsProperties;

    @Autowired
    protected StringRedisTemplate stringRedisTemplate;

    @Autowired
    private ISurveyEventTrigger surveyEventTrigger;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private OrganizationOrderService organizationOrderService;

    @Autowired
    private XpackConfigService xpackConfigService;

    @Autowired
    private PayProperties payProperties;

    @Override
    protected Object requireParent(long l) {
        return l;
    }

    private static final String SURVEY_PLUS_CONTACTS_RANDOM = "survey_plus:contacts_random";
    private static final String SURVEY_PLUS_CONTACTS_HASH_KEY = "survey_plus:contacts_hash";

//    @Override
//    public SurveyChannelDto mapToDto(SurveyChannel entity) {
//        return setNumResponse(super.mapToDto(entity));
//    }
//
//    @Override
//    public List<SurveyChannelDto> mapToDto(List<SurveyChannel> entity) {
//        return super.mapToDto(entity).stream().map(i -> setNumResponse(i)).collect(Collectors.toList());
//    }
//
//    private SurveyChannelDto setNumResponse(SurveyChannelDto channelDto) {
//        Long numResponse = responseService.getNumResponseByChannel(channelDto.getSid(), channelDto.getId());
//        channelDto.setNumOfResponses(numResponse);
//        return channelDto;
//    }

    @Override
    public void afterMapToDto(List<SurveyChannel> entity, List<SurveyChannelDto> dto) {
        if (CollectionUtils.isNotEmpty(dto)) {
            dto.forEach(i -> {
                Long numResponse = responseService.getNumResponseByChannel(i.getSid(), i.getId());
                i.setNumOfResponses(numResponse);
                // 如果设置了渠道订单类型，则转换一下订单状态
                fillChannelOrderStatus(i);
            });
        }
    }

    private void fillChannelOrderStatus(SurveyChannelDto i) {
        if (ORDER_PAY_TYPE_STANDARD.equals(i.getOrderPayType())) {
            fillChannelOrderStatusByOrder(i);
        } else if (ORDER_PAY_TYPE_MANUAL.equals(i.getOrderPayType())) {
            if (i.getOrderAmount() == null || i.getOrderAmount() < 0) {
                i.setOrderStatus("none");
            } else {
                i.setOrderStatus("init");
                fillChannelOrderStatusByOrder(i);
            }
        }
    }

    private void fillChannelOrderStatusByOrder(SurveyChannelDto i) {
        // 如果已有订单，则修改为订单的状态
        OrganizationOrder order = organizationOrderService.get(i.getOrderId());
        if (order != null) {
            // 渠道订单状态：待报价(none)、待付款(init)、已付款(success)、已退款(refund)、退款失败(refund_failure)、退款中(refund_pending)
            switch (order.getStatus()) {
                case init, success, refund, refund_failure, refund_pending -> i.setOrderStatus(order.getStatus().name());
            }
        }
    }

    /**
     * 获取问卷渠道列表
     *
     * @param surveyId
     * @return
     */
    public List<SurveyChannel> getChannelList(Long surveyId) {
        return channelRepository.findAllBySid(surveyId);
    }

    /**
     * 获取渠道信息
     *
     * @param channelId
     * @return
     */
    public SurveyChannel requireChannel(@NotNull Long channelId) {
        Optional<SurveyChannel> surveyChannelOptional = channelRepository.findById(channelId);
        if (surveyChannelOptional.isEmpty()) {
            throw new EntityNotFoundException();
        }
        return surveyChannelOptional.get();
    }

    @Override
    public SurveyChannelDto findOneEmbeddedMany(Long rootId, String rootFieldNameInEmbedded, Long embeddedId) {
        SurveyChannelDto channelDto = super.findOneEmbeddedMany(rootId, rootFieldNameInEmbedded, embeddedId);
        //如果是调研家社区渠道 重新计算题目数 避免客户修改问卷
        if (channelDto.getType() == ChannelType.SURVEY_PLUS && channelDto.getStatus() == ChannelStatus.UNSET) {
            int countQuestion = surveyService.countQuestion(channelDto.getSid());
            updateSurveyPlusConfigure(channelDto, countQuestion);
        }
        return channelDto;
    }

    /**
     * 校验渠道id和问卷id 是否匹配，并且结束时间
     *
     * @param surveyChannel
     * @param surveyId
     */
    public void verifyChannelSurvey(@NotNull SurveyChannel surveyChannel, @NotNull long surveyId) {
        if (surveyChannel.getSid() != surveyId)
            throw new EntityNotFoundException();
    }

    @Override
    public SurveyChannelDto createEmbeddedMany(Long surveyId, EmbeddedFieldContext context, SurveyChannelDto data) {
        if (data.getType() == null)
            throw new SurveyErrorException(SurveyErrorCode.CHANNEL_TYPE_ERROR);
        long orgId = TenantContext.requireCurrentTenant();
        long userId = TenantContext.requireCurrentUserId();
        SurveyChannel surveyChannel = new SurveyChannel();
        surveyChannel.setSid(surveyId);
        surveyChannel.setType(data.getType());
        setChannelCreator(surveyChannel);
        if (StringUtils.isNotEmpty(data.getName())) {
            surveyChannel.setName(data.getName());
        } else {
            surveyChannel.setName(generateChannelName(surveyId, data.getType(), surveyChannel));
        }
        Optional.ofNullable(data.getStartTime()).ifPresent(surveyChannel::setStartTime);
        Optional.ofNullable(data.getEndTime()).ifPresent(surveyChannel::setEndTime);
        Optional.ofNullable(data.getSendTime()).ifPresent(surveyChannel::setSendTime);
        Optional.ofNullable(data.getResendTimes()).ifPresent(surveyChannel::setResendTimes);
        List<Pair<String, Long>> resendJobs = parseResendDelayJobs(surveyChannel);
        SurveyChannel result = channelRepository.save(surveyChannel);
        createResendJobs(surveyId, orgId, userId, surveyChannel, resendJobs);
        //初始化config参数配置
        initChannelConfigure(result);
        return mapToDto(result);
    }

    private void createResendJobs(Long surveyId, long orgId, long userId, SurveyChannel surveyChannel, List<Pair<String, Long>> resendJobs) {
        if (CollectionUtils.isNotEmpty(resendJobs)) {
            resendJobs.forEach(i -> surveyTaskTrigger.resendChannelSurveyDelay(orgId, userId, surveyId, surveyChannel.getId(), i.getLeft(), i.getRight()));
        }
    }

    private List<Pair<String, Long>> parseResendDelayJobs(SurveyChannel surveyChannel) {
        List<Pair<String, Long>> jobs = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(surveyChannel.getResendTimes())) {
            LocalDateTime now = LocalDateTime.now();
            surveyChannel.getResendTimes().forEach(i -> {
                if (StringUtils.isNotEmpty(i.getJobId()) && ChannelResendStatus.INIT == i.getStatus()) {
                    LocalDateTime resendTime = DateHelper.parseDateTime(i.getResendTime());
                    long delaySeconds;
                    if (resendTime != null && (delaySeconds = Duration.between(now, resendTime).getSeconds()) > 0) {
                        jobs.add(Pair.of(i.getJobId(), delaySeconds));
                        i.setStatus(ChannelResendStatus.QUEUE);
                    } else {
                        i.setStatus(ChannelResendStatus.CANCEL);
                    }
                }
            });
        }
        return jobs;
    }

    private void setChannelCreator(SurveyChannel surveyChannel) {
        SimpleUser simpleUser = userService.requireCurrentSimple();
        surveyChannel.setCreateUser(simpleUser.getTruename());
        surveyChannel.setUserId(simpleUser.getId());
    }

    private Integer countBySidAndType(Long surveyId, ChannelType type) {
        String sql = String.format("select count(id) " +
                "from survey_channel " +
                "where s_id=%1$s and type=%2$s ", surveyId, type.ordinal());
        log.debug("count channel total sql: {}", sql);
        return jdbcTemplate.queryForObject(sql, Integer.class);
    }


    private String generateChannelName(Long surveyId, ChannelType type, SurveyChannel surveyChannel) {
        Integer count = countBySidAndType(surveyId, type) + 1;
        String name = "";
        switch (type) {
            case SHORT_LINK:
                surveyChannel.setStatus(ChannelStatus.RECOVERY);
                name = ChannelType.SHORT_LINK.getText() + count;
                break;
            case PHONE_MSG:
                name = ChannelType.PHONE_MSG.getText() + count;
                break;
            case WECHAT_SERVICE:
                name = ChannelType.WECHAT_SERVICE.getText() + count;
                break;
            case INJECT_WEB:
                surveyChannel.setStatus(ChannelStatus.RECOVERY);
                name = ChannelType.INJECT_WEB.getText() + count;
                SurveyEmbedExtDto embed = new SurveyEmbedExtDto();
                surveyChannel.setConfigure(JsonHelper.toJson(embed));
                break;
            case SURVEY_PLUS:
                name = ChannelType.SURVEY_PLUS.getText() + count;
                surveyChannel.setEnableWechatLimit(true);
                surveyChannel.setEnableWechatReplyOnly(true);
                break;
            case SCENE_INTERACTION:
                name = ChannelType.SCENE_INTERACTION.getText() + count;
                break;
            case MP:
                name = ChannelType.MP.getText() + count;
                break;
            case EMAIL:
                name = ChannelType.EMAIL.getText() + count;
                break;
            case APP:
                surveyChannel.setStatus(ChannelStatus.RECOVERY);
                name = ChannelType.APP.getText() + count;
                break;
            default:
                surveyChannel.setStatus(ChannelStatus.RECOVERY);
                name = ChannelType.COMMON.getText() + 1;
        }
        return name;
    }

    /**
     * 初始化channel的configure
     *
     * @param channel
     */
    private void initChannelConfigure(SurveyChannel channel) {
        if (channel == null) return;
        SurveyEmbedExtDto embed = new SurveyEmbedExtDto();
        switch (channel.getType()) {
            case SHORT_LINK:
                //短链接 创建渠道的时候 生成短链接 写入configure
                ShortLinkDto shortLinkDto = new ShortLinkDto();
                Map<String, Object> params = new HashMap<>();
                params.put("channelId", channel.getId());//channelId=%d&collectorMethod=%s
                params.put("collectorMethod", SurveyCollectorMethod.SHORT_LINK.name());
                String shortUrl = linkService.toShortUrl(channel.getSid(), params);
                shortLinkDto.setShareUrl(shortUrl);
                channel.setConfigure(JsonHelper.toJson(shortLinkDto));
                break;
            case MP:
                //小程序渠道 生成二维码  问卷id:渠道id:tokenCode:injectType
                String scene = String.format("%s:%s:%s:%s", Long.toString(channel.getSid(), 36), Long.toString(channel.getId(), 36), "", embed.getInjectType());
                String url = wechatConfigureService.getUnlimitedQRCode(scene);
                embed.setQrCode(url);
                channel.setConfigure(JsonHelper.toJson(embed));
                channel.setStatus(ChannelStatus.RECOVERY);
                break;
            case SURVEY_PLUS:
                int countQuestion = surveyService.countQuestion(channel.getSid());
                channel.setConfigure(getSurveyPlusConfigure(channel.getSid(), countQuestion));
                break;
            case APP:
                embed.setInjectType("3");
                channel.setConfigure(JsonHelper.toJson(embed));
                break;
            case INJECT_WEB:
                embed.setEmbedCount(0);
                channel.setConfigure(JsonHelper.toJson(embed));
                break;
            default:
                return;
        }
        channelRepository.save(channel);
    }

    /**
     * 获取调研家社区渠道的配置参数
     *
     * @param surveyId
     * @param countQuestion
     * @return
     */
    private String getSurveyPlusConfigure(Long surveyId, int countQuestion) {
        Long orgId = TenantContext.requireCurrentTenant();
        Object channelConfig = stringRedisTemplate.opsForHash().get(SURVEY_PLUS_CONTACTS_HASH_KEY, orgId.toString());
        AtomicReference<StringBuilder> contacts = new AtomicReference<>(new StringBuilder());
        AtomicReference<StringBuilder> mobile = new AtomicReference<>(new StringBuilder());
        AtomicReference<StringBuilder> qrcode = new AtomicReference<>(new StringBuilder());
        Optional.ofNullable(channelConfig).ifPresent(c -> {
            ContactsDto dto = JsonHelper.toObject((String) c, ContactsDto.class);
            contacts.set(dto == null ? null : new StringBuilder(dto.getContacts()));
            mobile.set(dto == null ? null : new StringBuilder(dto.getMobile()));
            qrcode.set(dto == null ? null : new StringBuilder(dto.getQrcode()));
        });

        if (StringUtils.isEmpty(contacts.get()) || StringUtils.isEmpty(mobile.get()) || StringUtils.isEmpty(qrcode.get())) {
            List<Map<String, String>> users = contactsProperties.getUsers();
            if (users != null || users.size() > 0) {
                int index = new Random().nextInt(users.size());
                Map<String, String> user = users.get(index);
                if (user != null) {
                    contacts.set(new StringBuilder(user.get("contacts")));
                    mobile.set(new StringBuilder(user.get("mobile")));
                    qrcode.set(new StringBuilder(user.get("qrcode")));
                    //保存到redis
                    ContactsDto contactsDto = ContactsDto.builder()
                            .contacts(contacts.get().toString())
                            .mobile(mobile.get().toString())
                            .qrcode(qrcode.get().toString()).build();
                    stringRedisTemplate.opsForHash().put(SURVEY_PLUS_CONTACTS_HASH_KEY, orgId.toString(), JsonHelper.toJson(contactsDto));
                }
            }
        }
        return JsonHelper.toJson(SurveyPlusConfigureDto.builder()
                .contacts(contacts.get().toString())
                .mobile(mobile.get().toString())
                .qrcode(qrcode.get().toString())
                .countQuestion(countQuestion).build());
    }

    /**
     * 如果调研家社区问卷 修改了重新计算题目数
     *
     * @param channelDto
     * @param countQuestion
     */
    private void updateSurveyPlusConfigure(SurveyChannelDto channelDto, int countQuestion) {
        SurveyPlusConfigureDto configureDto = JsonHelper.toObject(channelDto.getConfigure(), SurveyPlusConfigureDto.class);
        if (configureDto == null) {
            channelDto.setConfigure(getSurveyPlusConfigure(channelDto.getSid(), countQuestion));
            channelDto.getEntity().setConfigure(channelDto.getConfigure());
            repository.save(channelDto.getEntity());
        } else if (configureDto != null && countQuestion != configureDto.getCountQuestion()) {
            configureDto.setCountQuestion(countQuestion);
            channelDto.setConfigure(JsonHelper.toJson(configureDto));
            channelDto.getEntity().setConfigure(channelDto.getConfigure());
            repository.save(channelDto.getEntity());
        }
    }

//    /**
//     * 获取问卷渠道列表
//     *
//     * @param surveyId
//     * @param params
//     * @return
//     */
//    public ResourcePageResponseDto<SurveyChannel> getAllChannelList(@NotNull Long surveyId, Map<String, Object> params) {
//        Survey survey = surveyService.requireSurvey(surveyId);
//
//        int page = Integer.parseInt(params.getOrDefault("_page", "1").toString());
//        int limit = Integer.parseInt(params.getOrDefault("_limit", "20").toString());
//
//        if (page <= 0 || limit <= 0) {
//            page = 1;
//            limit = 20;
//        }
//        // 默认从第0页开始
//        Pageable pageable = PageRequest.of(page - 1, limit, Sort.by("id").descending());
//        Page<SurveyChannel> channelList = channelRepository.findAllBySid(surveyId, pageable);
//
//        return new ResourcePageResponseDto(channelList);
//    }

//    /**
//     * 获取渠道详情
//     *
//     * @param surveyId
//     * @param channelId
//     * @return
//     */
//    public ResourceResponseDto<SurveyChannel> getOneChannel(@NotNull long surveyId, @NotNull long channelId) {
//        SurveyChannel surveyChannel = requireChannel(channelId);
//        verifyChannelSurvey(surveyChannel, surveyId);
//        return new ResourceResponseDto(surveyChannel);
//    }


    public SurveyChannelDto updateOne(long surveyId, long channelId, Map<String, Object> change) {
        surveyService.checkSurveyByOrgId(surveyId);
        SurveyChannel surveyChannel = requireChannel(channelId);
        verifyChannelSurvey(surveyChannel, surveyId);
        // 禁止编辑 orderId orderAmount orderPayType
        if (change.containsKey("orderId") || change.containsKey("orderAmount") || change.containsKey("orderPayType")) {
            throw new BadRequestException("禁止编辑订单信息");
        }

        EntityUtility.mergeEntityWithData(surveyChannel, change);

        if (surveyChannel.getType() == ChannelType.MP && change.containsKey("configure")) {
            createQrCode(surveyChannel, (String) change.getOrDefault("configure",""));
        }

//        if (surveyChannel.getStatus() == ChannelStatus.UNSET)
//            surveyChannel.setStatus(ChannelStatus.RECOVERY);

        return mapToDto(repository.save(surveyChannel));
    }

    /**
     * 生成小程序QrCode
     * @param surveyChannel
     * @param configure
     */
    private void createQrCode(SurveyChannel surveyChannel, String configure) {
        if (surveyChannel == null || StringUtils.isEmpty(configure)) return;
        SurveyEmbedExtDto embed = JsonHelper.toObject(configure, SurveyEmbedExtDto.class);
        String scene = String.format("%s:%s:%s:%s", Long.toString(surveyChannel.getSid(), 36), Long.toString(surveyChannel.getId(), 36), "", embed.getInjectType());
        String url =  wechatConfigureService.getUnlimitedQRCode(scene);
        embed.setQrCode(url);
        surveyChannel.setConfigure(JsonHelper.toJson(embed));
    }

    @Override
    public Boolean deleteOneEmbeddedMany(Long surveyId, String embeddedMapperBy, Long channelId) {
        Survey survey = surveyService.requireSurvey(surveyId);

        SurveyChannel surveyChannel = requireChannel(channelId);

        verifyChannelSurvey(surveyChannel, surveyId);
        //删除渠道
        channelRepository.deleteById(channelId);
        //删除该渠道的问卷填答数据
        List<Long> responseIds = responseService.deleteSurveyResponse(surveyId, channelId);

        //异步回退配额进度
        if (!responseIds.isEmpty() && surveyService.checkSurveyQuota(survey))
            quotaService.rollBackQuotaBatch(surveyId, responseIds);
        if (surveyChannel.getTaskProgressId() != null) {
            cancelChannelSendTask(TenantContext.requireCurrentTenant(), TenantContext.requireCurrentUserId(), surveyChannel);
        }
        return true;
    }

    /**
     * 关闭渠道和删除渠道时，取消所有的发送任务
     */
    private void cancelChannelSendTask(Long orgId, Long userId, SurveyChannel channel) {
        List<TaskProgress> list = userTaskService.getAllByType(orgId, userId, UserTaskType.sendSurveyChannel, channel.getId());
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(i -> {
                if (!i.getStatus().isCompleted()) {
                    userTaskService.cancel(i.getId());
                    if (channel.getType() == ChannelType.PHONE_MSG) {
                        smsAccountService.cancelSmsTask(orgId, userId, i.getId());
                    }
                }
            });
        }
    }

    /**
     * 暂停渠道
     *
     * @param surveyId
     * @param channelId
     * @return
     */
    public ResourceResponseDto<SurveyChannel> pause(@NotNull long surveyId, @NotNull long channelId) {
        surveyService.checkSurveyByOrgId(surveyId);
        SurveyChannel surveyChannel = requireChannel(channelId);
        verifyChannelSurvey(surveyChannel, surveyId);
        if (surveyChannel.getStatus() != ChannelStatus.RECOVERY)
            throw new SurveyErrorException(SurveyErrorCode.CHANNEL_STATUS_ERROR);

        surveyChannel.setStatus(ChannelStatus.PAUSE);
        channelRepository.save(surveyChannel);
        //调研家社区渠道 同步渠道状态
        if (surveyChannel.getType() == ChannelType.SURVEY_PLUS) surveyEventTrigger.channelPause(surveyId, channelId);
        return new ResourceResponseDto<>();
    }

    /**
     * 关闭渠道
     *
     * @param surveyId
     * @param channelId
     * @return
     */
    public ResourceResponseDto close(@NotNull long surveyId, @NotNull long channelId) {
        surveyService.checkSurveyByOrgId(surveyId);
        SurveyChannel surveyChannel = requireChannel(channelId);
        verifyChannelSurvey(surveyChannel, surveyId);
        if (surveyChannel.getStatus() == ChannelStatus.COMPLETE)
            throw new SurveyErrorException(SurveyErrorCode.CHANNEL_TYPE_COMPLETE);

        surveyChannel.setStatus(ChannelStatus.COMPLETE);
        channelRepository.save(surveyChannel);
        if (surveyChannel.getTaskProgressId() != null) {
            cancelChannelSendTask(TenantContext.requireCurrentTenant(), TenantContext.requireCurrentUserId(), surveyChannel);
        }
        //调研家社区渠道 同步渠道状态
        if (surveyChannel.getType() == ChannelType.SURVEY_PLUS) surveyEventTrigger.channelClose(surveyId, channelId);
        return new ResourceResponseDto<>();
    }


    /**
     * 重启渠道
     *
     * @param surveyId
     * @param channelId
     * @return
     */
    public ResourceResponseDto restart(@NotNull long surveyId, @NotNull long channelId) {
        surveyService.checkSurveyByOrgId(surveyId);
        SurveyChannel surveyChannel = requireChannel(channelId);
        verifyChannelSurvey(surveyChannel, surveyId);
        if (surveyChannel.getStatus() != ChannelStatus.PAUSE)
            throw new SurveyErrorException(SurveyErrorCode.CHANNEL_STATUS_ERROR);

        surveyChannel.setStatus(ChannelStatus.RECOVERY);
        channelRepository.save(surveyChannel);
        return new ResourceResponseDto();
    }

    /**
     * 清空填答数据
     *
     * @param surveyId
     * @param channelId
     * @return
     */
    public ResourceResponseDto wipeData(@NotNull long surveyId, @NotNull long channelId) {
        surveyService.checkSurveyByOrgId(surveyId);
        Survey survey = surveyService.requireSurvey(surveyId);

        SurveyChannel surveyChannel = requireChannel(channelId);
        verifyChannelSurvey(surveyChannel, surveyId);

        //删除该渠道的问卷填答数据
        List<Long> responseIds = responseService.deleteSurveyResponse(surveyId, channelId);

        //异步回退配额进度
        if (!responseIds.isEmpty() && surveyService.checkSurveyQuota(survey))
            quotaService.rollBackQuotaBatch(surveyId, responseIds);
        return new ResourceResponseDto();
    }

    public static final String ORDER_PAY_TYPE_STANDARD = "standard";
    public static final String ORDER_PAY_TYPE_MANUAL = "manual";

    /**
     * 创建调研家社区订单
     *
     * @param surveyId
     * @param channelId
     * @param dto
     * @return
     */
    @Transactional
    public SurveyChannelDto createSurveyPlusOrder(Long surveyId, Long channelId, SurveyPlusConfigureDto dto) {
        SurveyChannel surveyChannel = requireChannel(channelId);
        verifyChannelSurvey(surveyChannel, surveyId);
        Optional.ofNullable(dto).ifPresent(d -> {
            updateSurveyPlusOrder(dto, surveyChannel);
            dto.setCreateTime(new Date());
            surveyChannel.setConfigure(JsonHelper.toJson(dto));
            repository.save(surveyChannel);
            surveyEventTrigger.adminxChannelOrderRequest(
                    TenantContext.requireCurrentTenant(),
                    TenantContext.requireCurrentUserId(),
                    surveyId,
                    channelId,
                    dto.getContacts(),
                    DateHelper.formatDateTime(dto.getCreateTime())
            );
        });
        //this.channelCreateSampleOrder(surveyChannel);
        surveyEventTrigger.channelCreate(surveyChannel.getSid(), surveyChannel.getId());
        SurveyChannelDto result = mapToDto(surveyChannel);
        fillChannelOrderStatus(result);
        return result;
    }

    /**
     * 创建样本订单
     *
     * @param surveyChannel
     */
    public void channelCreateSampleOrder(SurveyChannel surveyChannel) {
        OrganizationOrder order = null;
        if (surveyChannel.getOrderId() != null) {
            order = organizationOrderService.get(surveyChannel.getOrderId());
        }
        // 标准订单 提交未支付不创建样本订单
        if (!(ORDER_PAY_TYPE_STANDARD.equals(surveyChannel.getOrderPayType())
                && (order == null || OrderStatus.success != order.getStatus()))) {
            surveyEventTrigger.channelCreate(surveyChannel.getSid(), surveyChannel.getId());
        }
    }

    /**
     * 校验订单状态
     * 1 如果渠道已经设置了人工报价，不能再修改类型
     * 2 如果渠道设置了标准报价，则取消旧的订单(如果订单已支付，直接抛出异常，未支付则会取消)
     */
    private void updateSurveyPlusOrder(SurveyPlusConfigureDto dto, SurveyChannel surveyChannel) {
        if (ORDER_PAY_TYPE_MANUAL.equals(surveyChannel.getOrderPayType())) {
            throw new BadRequestException("已提交人工报价订单");
        } else if (ORDER_PAY_TYPE_STANDARD.equals(surveyChannel.getOrderPayType())) {
            if (surveyChannel.getOrderId() != null && surveyChannel.getOrderId() > 0) {
                organizationOrderService.cancelOrder(TenantContext.requireCurrentTenant(), TenantContext.requireCurrentUserId(), OrderType.order_adminx_channel, surveyChannel.getOrderId());
            }
        }
        int amount = -1;
        if (!"待报价".equals(dto.getTotalPrice())) {
            Map<String, Integer> costAmountInfoMap = new HashMap<>();
            amount = getOrderAmountWithServiceRate(dto, costAmountInfoMap);
        }
        if (amount < 0) {
            // 人工报价
            surveyChannel.setOrderPayType(ORDER_PAY_TYPE_MANUAL);
            surveyChannel.setOrderAmount(null);
            surveyChannel.setOrderId(null);
            surveyChannel.setStatus(ChannelStatus.SUBMIT);
        } else {
            // 标准报价
            surveyChannel.setOrderPayType(ORDER_PAY_TYPE_STANDARD);
            surveyChannel.setOrderAmount(amount);
            surveyChannel.setOrderId(null);
            //标准报价跟进人
            dto.setContacts(contactsProperties.getStandardContact());
        }
    }

    private int getOrderAmountWithServiceRate(SurveyPlusConfigureDto orderInfo, Map<String, Integer> costAmountInfo) {
        int sampleAmount = getOrderAmount(orderInfo, costAmountInfo);
        int totalAmount = -1;
        int serviceAmount = 0;
        if (sampleAmount > 0) {
            float serviceRate = payProperties.getServiceRate().getOrDefault(PayServiceRate.order_adminx_channel, 0f);
            serviceAmount = Float.valueOf(serviceRate * sampleAmount).intValue();
            totalAmount = serviceAmount + sampleAmount;
        }
        costAmountInfo.put("sampleAmount", sampleAmount);
        costAmountInfo.put("totalAmount", totalAmount);
        costAmountInfo.put("serviceAmount", serviceAmount);
        return totalAmount;
    }

    /**
     * 计算样本订单价格（未包含服务费）
     */
    private int getOrderAmount(SurveyPlusConfigureDto orderInfo, Map<String, Integer> costAmountInfo) {
        XpackConfig price = xpackConfigService.getConfigByType(XPackAppType.ADMINX_CHANNEL_PRICE);
        SurveyPlusOrderPriceDto orderPrice = null;
        if (price != null && price.getEnabled() != null && price.getEnabled()) {
            orderPrice = JsonHelper.toObject(price.getConfig(), SurveyPlusOrderPriceDto.class);
        }
        if (orderPrice != null) {
            return orderPrice.amount(orderInfo, costAmountInfo);
        }
        throw new BadRequestException("样本特征价格未定义");
    }

    /**
     * * 调研家社区订单拦截设置
     *
     * @param surveyId
     * @param channelId
     */
    public void orderSetting(Long surveyId, Long channelId) {
        SurveyChannel surveyChannel = requireChannel(channelId);
        verifyChannelSurvey(surveyChannel, surveyId);
        if (surveyChannel.getType() != ChannelType.SURVEY_PLUS) return;

        Survey survey = surveyService.requireSurvey(surveyId);

        survey.setGoNextPageAutomatic(false);
        survey.setEnableResponseLimit(false);
        survey.setEnableDateLimit(false);
        survey.getQuestions().forEach(question -> {
            if (question.getType() == QuestionType.AREA && question.getAreaType() == FormatType.ADDRESS)
                question.setAreaType(FormatType.COUNTY);
        });
        surveyService.save(survey);
    }

    @SneakyThrows
    public void download(HttpServletResponse response, Long surveyId, Long channelId) throws IOException {
        surveyService.checkSurveyByOrgId(surveyId);
        SurveyChannel channel = requireChannel(channelId);
        if (channel.getType() != ChannelType.INJECT_WEB) return;

        SurveyEmbedExtDto embed = JsonHelper.toObject(channel.getConfigure(), SurveyEmbedExtDto.class);
        if (embed == null) return;

        List<Object> userIds = embed.getEmbedUserId();

        List<String> externalUserIds = userIds.stream()
                .map(Object::toString)
                .collect(Collectors.toList());

        List<String> headers =  List.of("用户ID", "企业ID", "最后一次填答状态", "最后一次显示时间");

        List<List> dataList = new ArrayList<>();

        Map<String, SimpleResponse3> response3Map = responseService.getResponseByExternalUserId(surveyId, channelId, externalUserIds);
        for (String externalUserId : externalUserIds) {
            List row = new ArrayList<>();

            if (!response3Map.isEmpty() && response3Map.containsKey(externalUserId)) {
                SimpleResponse3 response3 = response3Map.get(externalUserId);
                String status = "未提交";
                Date time = response3.getCreateTime();
                switch(response3.getStatus()){
                    case FINAL_SUBMIT:
                        status = "已提交";
                        time = response3.getFinishTime();
                        break;
                    case EARLY_COMPLETED:
                        status = "提前结束";
                        break;
                    case INVALID:
                        status = "已作废";
                        time = response3.getFinishTime();
                        break;
                    case QUOTA_FUll:
                        status = "配额已满";
                        break;
                    default:
                }
                row.addAll(List.of(externalUserId, StringUtils.isEmpty(response3.getExternalCompanyId()) ? "":response3.getExternalCompanyId(), status, DateHelper.formatDateTime(time)));
            } else {
                row.addAll(List.of(externalUserId,"","未访问",""));
            }
            dataList.add(row);
        }
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode(channel.getName() + "_" + LocalDate.now(), "UTF-8").replaceAll("\\+", "%20");
        response.addHeader("Content-Disposition", "attachment;fileName=" + fileName + ".xlsx");

        ByteArrayOutputStream byteArrayOutputStream = EasyExcelUtils.easyExcel(headers, dataList, null);
        response.getOutputStream().write(byteArrayOutputStream.toByteArray());
    }

}
