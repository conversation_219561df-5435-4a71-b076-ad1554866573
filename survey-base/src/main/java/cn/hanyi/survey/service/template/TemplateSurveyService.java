package cn.hanyi.survey.service.template;

import cn.hanyi.survey.core.constant.Template.TemplateType;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyDto;
import cn.hanyi.survey.core.entity.SurveyGroup;
import cn.hanyi.survey.core.entity.template.TemplateGroup;
import cn.hanyi.survey.core.entity.template.TemplateSurvey;
import cn.hanyi.survey.core.entity.template.TemplateSurveyDto;
import cn.hanyi.survey.core.repository.TemplateSurveyRepository;
import cn.hanyi.survey.dto.template.SaveTemplateRequestDto;
import cn.hanyi.survey.dto.template.TemplateSurveyQueryDto;
import cn.hanyi.survey.service.SurveyGroupService;
import cn.hanyi.survey.service.SurveyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.entity.User;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.UserService;
import org.befun.core.constant.QueryOperator;
import org.befun.core.dto.query.ResourceCustomQueryDto;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.dto.query.ResourceQueryCriteria;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Slf4j
public class TemplateSurveyService extends BaseService<TemplateSurvey, TemplateSurveyDto, TemplateSurveyRepository> {

    @Autowired
    private TemplateSurveyRepository templateSurveyRepository;
    @Autowired
    private TemplateGroupService templateGroupService;
    @Autowired
    private UserService userService;
    @Autowired
    private SurveyService surveyService;
    @Autowired
    private SurveyGroupService surveyGroupService;
    @Autowired
    private TemplateMapService templateMapService;

    @Value("${survey.public-template-org-id:}")
    private Long publicTemplateOrgId;

    @Override
    public <S extends ResourceCustomQueryDto> Page<TemplateSurveyDto> findAll(S query) {
        TemplateSurveyQueryDto dto = (TemplateSurveyQueryDto) query;
        Long searchGroupId = dto.getGroupId() == null || dto.getGroupId() < 0 ? null : dto.getGroupId();
        ResourceEntityQueryDto<TemplateSurveyDto> params = dto.transform();
        if (StringUtils.isNotEmpty(dto.getQ())) {
            params.getQueryCriteriaList().add(new ResourceQueryCriteria("title", dto.getQ(), QueryOperator.LIKE));
        }
        if (searchGroupId != null) {
            params.getQueryCriteriaList().add(new ResourceQueryCriteria("group", searchGroupId));
        }
        return findAll(params);
    }

    @Override
    public void afterMapToDto(List<TemplateSurvey> entity, List<TemplateSurveyDto> dto) {
        Set<Long> updateUserIds = new HashSet<>();
        dto.forEach(survey -> {
            if (survey.getGroup() == null) {
                survey.setGroup(templateGroupService.mapToDto(templateGroupService.getEmptyGroup(TemplateType.SURVEY)));
            }
            Optional.ofNullable(survey.getUserId()).ifPresent(updateUserIds::add);
        });
        Map<Long, SimpleUser> userMap = userService.getGroupMapByIds(new ArrayList<>(updateUserIds), User::getId, SimpleUser::fromUser);
        dto.forEach(survey -> {
            Optional.ofNullable(survey.getUserId()).ifPresent(editorUser -> survey.setCreator(userMap.get(editorUser)));
        });
    }

    public boolean updateTemplate(long id, Long groupId, String templateName) {
        TemplateSurvey entity = require(id);
        checkIsCurrentOrg(entity);
        Optional.ofNullable(groupId).ifPresent(i -> {
            TemplateGroup group = null;
            if (groupId <= 0) {
                group = templateGroupService.getDefaultGroup(TemplateType.SURVEY, groupId);
            } else {
                group = templateGroupService.require(groupId);
                templateGroupService.checkIsCurrentOrg(group);
            }
            entity.setGroup(group);
        });
        Optional.ofNullable(templateName).ifPresent(entity::setTitle);
        templateSurveyRepository.save(entity);
        return true;
    }

    public TemplateSurveyDto saveTemplate(SaveTemplateRequestDto dto) {
        TemplateGroup group = null;
        if (dto.getGroupId() > 0) {
            group = templateGroupService.require(dto.getGroupId());
            templateGroupService.checkIsCurrentOrg(group);
        } else {
            group = templateGroupService.getEmptyGroup(TemplateType.SURVEY);
        }
        Survey source = surveyService.requireSurvey(dto.getSurveyId());
        return mapToDto(templateMapService.mapToTemplate(source, group, dto.getTemplateName(), true, dto.getRemark()));
    }

    public SurveyDto useTemplate(long templateId, Long groupId) {
        TemplateSurvey source = repository.getOne(templateId);
        checkIsCurrentOrg(source, publicTemplateOrgId);
        if (groupId > 0) {
            SurveyGroup group = surveyGroupService.require(groupId);
            if (group.getOrgId() == null
                    || !group.getOrgId().equals(TenantContext.getCurrentTenant())
                    || group.getUserId() == null
                    || !group.getUserId().equals(TenantContext.getCurrentUserId())) {
                throw new BadRequestException("问卷目录不存在");
            }
        } else {
            groupId = 0L;
        }
        return surveyService.mapToDto(templateMapService.mapToSurvey(groupId, source, true));
    }
}
