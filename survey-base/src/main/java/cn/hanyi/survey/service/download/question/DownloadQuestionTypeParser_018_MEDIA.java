package cn.hanyi.survey.service.download.question;

import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.core.entity.SurveyResponseCell;
import cn.hanyi.survey.service.download.IDownloadQuestionTypeParser;
import cn.hanyi.survey.service.download.dto.DownloadColumnQuestion;
import cn.hanyi.survey.service.download.dto.DownloadColumnQuestionGroup;
import cn.hanyi.survey.service.download.dto.ResponseDownloadContext;
import org.befun.core.utils.JsonHelper;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class DownloadQuestionTypeParser_018_MEDIA implements IDownloadQuestionTypeParser {

    @Override
    public QuestionType type() {
        return QuestionType.MEDIA;
    }

    @Override
    public DownloadColumnQuestionGroup buildGroup(ResponseDownloadContext context, SurveyQuestion question) {
        DownloadColumnQuestionGroup group = new DownloadColumnQuestionGroup(question, question.getItems(), question.getColumns());
        if (question.getIsMediaRandom() != null && question.getIsMediaRandom()) {
            group.getColumns().add(new DownloadColumnQuestion_MEDIA(context, group));
        }
        return group;
    }


    public class DownloadColumnQuestion_MEDIA extends DownloadColumnQuestion {

        public DownloadColumnQuestion_MEDIA(ResponseDownloadContext context, DownloadColumnQuestionGroup group) {
            super(context, group, group.getCode() + "_" + group.getTitle() + "_随机结果", group.getCode() + "_display");
        }

        @Override
        public Object getCode(SurveyResponseCell cell) {
            return getFileName(cell);
        }

        @Override
        public Object getLabel(SurveyResponseCell cell) {
            return getFileName(cell);
        }

        private String getFileName(SurveyResponseCell cell) {
            List<Map> maps = JsonHelper.toList(cell.getStrValue(), Map.class);
            Object value = null;
            if (maps != null && maps.size() > 0) {
                Map map = maps.get(0);
                value = map.get("fileName");
            }
            return value == null ? "" : value.toString();
        }
    }
}
