package cn.hanyi.survey.service.download.dto;

import cn.hanyi.survey.core.constant.DownloadFromType;
import cn.hanyi.survey.core.constant.DownloadType;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyChannel;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.core.entity.SurveyQuestionItem;
import cn.hanyi.survey.core.utilis.RegularExpressionUtils;
import cn.hanyi.survey.service.download.ext.DownloadExtType;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;

@Getter
@Setter
public class ResponseDownloadContext {

    private final long surveyId;
    private DownloadType downloadType;
    private DownloadFromType fromType;
    private Survey survey;
    private String safeTitle;
    private Map<Long, SurveyChannel> channelMap = new HashMap<>();
    private List<SurveyQuestion> originQuestions;
    private List<SurveyQuestion> originSortGroups = new ArrayList<>();
    private List<SurveyQuestion> originSortQuestions = new ArrayList<>();
    private List<SurveyQuestion> filterAndSortQuestions;

    // 进度相关
    private Long taskId;
    private int total;
    private int progressed;
    private int totalRows;
    private int progressedRows;
    private double logPoint;
    private double logPointStep;

    // 构建答卷数据的临时数据
    private DownloadColumnResponseGroup responseGroup;
    private Map<Long, DownloadColumnQuestionGroup> questionGroupMap = new HashMap<>();
    private int columnSize = 0;
    private List<String> labelHeaders = new ArrayList<>();
    private List<String> codeHeaders = new ArrayList<>();
    private final LinkedHashMap<Long, Object[]> labelRowMap = new LinkedHashMap<>();
    private final LinkedHashMap<Long, Object[]> codeRowMap = new LinkedHashMap<>();

    private ResponseExportFile fileLabel;
    private ResponseExportFile fileCode;
    private Map<Long, List<ResponseAdditionalFile>> additionalFiles = new HashMap<>();
    private Map<DownloadExtType, ResponseExportFile> extFileMap = new HashMap<>();

    private Boolean existGroupOrQuestion = false;
    private Boolean existItem = false;
    private Map<Long, List<SurveyQuestionItem>> questionItemMap = new HashMap<>();

    public ResponseDownloadContext(Long taskId, long surveyId, DownloadType downloadType, Survey survey, List<SurveyChannel> channels) {
        this.taskId = taskId;
        this.surveyId = surveyId;
        this.downloadType = downloadType;
        this.survey = survey;
        this.safeTitle = RegularExpressionUtils.safeTitle(survey.getTitle());
        if (CollectionUtils.isNotEmpty(channels)) {
            channels.forEach(c -> channelMap.put(c.getId(), c));
        }
    }

    public Object[] getEmptyRow() {
        return new Object[columnSize];
    }

    public void plusColumnSize() {
        columnSize++;
    }

    public void addLabelHeader(String labelHeader) {
        labelHeaders.add(labelHeader);
    }

    public void addCodeHeader(String codeHeader) {
        codeHeaders.add(codeHeader);
    }

    public void appendProgressed(int append) {
        int i = this.progressed + append;
        this.progressed = Math.min(i, total);
    }

    public void plusProgressedRows() {
        progressedRows++;
    }

    public void calcNextLogPointAndProgressed() {
        if (this.logPoint > 0 && this.logPointStep > 0 && this.totalRows > 0) {
            do {
                this.logPoint += this.logPointStep;
            } while (this.logPoint < this.progressedRows);
            int relativeProgressed = (int) ((this.progressedRows * 1.0 / this.totalRows) * (this.total / 2));
            int append = relativeProgressed + (this.total / 2) - progressed;
            appendProgressed(append);
        }
    }

    public void clearTemp() {
        labelHeaders.clear();
        codeHeaders.clear();
        labelRowMap.clear();
        codeRowMap.clear();
    }

    public boolean hasExtFiles() {
        return getDownloadType() == DownloadType.EXCEL || getDownloadType() == DownloadType.CSV;
    }


//    public static void main(String[] args) {
//        ResponseDownloadContext context = new ResponseDownloadContext(null, 0, null, new Survey(), null);
//        context.setTotal(1234);
//        context.setProgressed(context.total / 2);
//        context.setTotalRows(3333);
//        context.setLogPointStep(3333 * 1.0 / 50);
//        context.setLogPoint(context.getLogPointStep());
//        IntStream.range(0, 3333).forEach(i -> {
//            context.plusProgressedRows();
//            if (context.getProgressedRows() >= context.getLogPoint()) {
//                context.calcNextLogPointAndProgressed();
//                System.out.printf("答卷下载：总数：%d，进度数：%d%n", context.getTotal(), context.getProgressed());
//            }
//        });
//    }
}
