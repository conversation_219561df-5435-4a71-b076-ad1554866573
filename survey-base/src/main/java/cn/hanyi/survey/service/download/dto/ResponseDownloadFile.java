package cn.hanyi.survey.service.download.dto;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.utils.JsonHelper;

import java.nio.charset.StandardCharsets;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
public class ResponseDownloadFile {

    private String fileName;
    private byte[] bytes;
    private Map<String, Object> json;

    public ResponseDownloadFile(String fileName, byte[] bytes) {
        this.fileName = fileName;
        this.bytes = bytes;
    }

    public ResponseDownloadFile(String fileName, Map<String, Object> json) {
        this.fileName = fileName;
        this.json = json;
    }

    public byte[] getBytes() {
        if (bytes == null) {
            if (json != null) {
                String s = JsonHelper.toJson(json);
                bytes = s.getBytes(StandardCharsets.UTF_8);
            }
        }
        return bytes;
    }
}
