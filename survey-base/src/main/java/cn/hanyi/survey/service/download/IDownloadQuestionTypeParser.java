package cn.hanyi.survey.service.download;

import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.service.download.dto.DownloadColumnQuestionGroup;
import cn.hanyi.survey.service.download.dto.ResponseDownloadContext;

public interface IDownloadQuestionTypeParser {

    QuestionType type();

    DownloadColumnQuestionGroup buildGroup(ResponseDownloadContext context, SurveyQuestion question);
}
