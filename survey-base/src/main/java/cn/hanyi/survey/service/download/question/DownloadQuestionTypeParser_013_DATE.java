package cn.hanyi.survey.service.download.question;

import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.core.entity.SurveyResponseCell;
import cn.hanyi.survey.service.download.IDownloadQuestionTypeParser;
import cn.hanyi.survey.service.download.dto.DownloadColumnQuestion;
import cn.hanyi.survey.service.download.dto.DownloadColumnQuestionGroup;
import cn.hanyi.survey.service.download.dto.ResponseDownloadContext;
import org.befun.core.utils.DateHelper;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.Date;

@Component
public class DownloadQuestionTypeParser_013_DATE implements IDownloadQuestionTypeParser {

    @Override
    public QuestionType type() {
        return QuestionType.DATE;
    }

    @Override
    public DownloadColumnQuestionGroup buildGroup(ResponseDownloadContext context, SurveyQuestion question) {
        DownloadColumnQuestionGroup group = new DownloadColumnQuestionGroup(question, question.getItems(), question.getColumns());
        group.getColumns().add(new DownloadColumnQuestion_DATE(context, group));
        return group;
    }

    public static class DownloadColumnQuestion_DATE extends DownloadColumnQuestion {

        private final DateTimeFormatter formatter;

        public DownloadColumnQuestion_DATE(ResponseDownloadContext context, DownloadColumnQuestionGroup group) {
            super(context, group);
            this.formatter = DateTimeFormatter.ofPattern(group.getQuestion().getAreaType().getFormat());
        }

        @Override
        public Object getCode(SurveyResponseCell cell) {
            Long ms = cell.getDateValue();
            if (ms != null) {
                return DateHelper.format(new Date(ms), formatter);
            }
            return null;
        }

        @Override
        public Object getLabel(SurveyResponseCell cell) {
            return getCode(cell);
        }
    }

}
