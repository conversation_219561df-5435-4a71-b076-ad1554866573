package cn.hanyi.survey.service.download.question;

import cn.hanyi.survey.core.constant.AreaCodeEnum;
import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.core.entity.SurveyResponseCell;
import cn.hanyi.survey.service.download.IDownloadQuestionTypeParser;
import cn.hanyi.survey.service.download.dto.DownloadColumnQuestion;
import cn.hanyi.survey.service.download.dto.DownloadColumnQuestionGroup;
import cn.hanyi.survey.service.download.dto.DownloadColumnQuestion_string;
import cn.hanyi.survey.service.download.dto.ResponseDownloadContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
public class DownloadQuestionTypeParser_008_MOBILE implements IDownloadQuestionTypeParser {

    @Override
    public QuestionType type() {
        return QuestionType.MOBILE;
    }

    @Override
    public DownloadColumnQuestionGroup buildGroup(ResponseDownloadContext context, SurveyQuestion question) {
        DownloadColumnQuestionGroup group = new DownloadColumnQuestionGroup(question, question.getItems(), question.getColumns());
        if (question.getOtherCountryNumber() != null && question.getOtherCountryNumber()) {
            group.getColumns().add(new DownloadColumnQuestion_AreaCode(context, group));
        }
        group.getColumns().add(new DownloadColumnQuestion_string(context, group));
        if (question.getIsVerifyMobile() != null && question.getIsVerifyMobile()) {
            // 如果开启了手机号校验，则增加一列
            group.getColumns().add(new DownloadColumnQuestion_MobileVerify(context, group));
        }
        return group;
    }

    public static class DownloadColumnQuestion_MobileVerify extends DownloadColumnQuestion {

        public DownloadColumnQuestion_MobileVerify(ResponseDownloadContext context, DownloadColumnQuestionGroup group) {
            super(context, group,
                    group.getCode() + "_" + group.getTitle() + "_短信验证",
                    group.getCode() + "_短信验证");
        }

        @Override
        public Object getCode(SurveyResponseCell cell) {
            return cell.getCommentValue() != null && cell.getCommentValue().equals("1") ? 1 : 0;
        }

        @Override
        public Object getLabel(SurveyResponseCell cell) {
            return cell.getCommentValue() != null && cell.getCommentValue().equals("1") ? "是" : "否";
        }
    }

    public static class DownloadColumnQuestion_AreaCode extends DownloadColumnQuestion {

        public DownloadColumnQuestion_AreaCode(ResponseDownloadContext context, DownloadColumnQuestionGroup group) {
            super(context, group, group.getCode() + "_" + group.getTitle() + "_区号", group.getCode() + "_area");
        }

        @Override
        public Object getCode(SurveyResponseCell cell) {
            if (StringUtils.isEmpty(cell.getTags())) {
                return null;
            }
            AreaCodeEnum areaCodeEnum = AreaCodeEnum.getByName(cell.getTags());
            if (areaCodeEnum != null) {
                return areaCodeEnum.getName() + areaCodeEnum.getCode();
            }
            return cell.getTags();
        }

        public static void main(String[] args) {
            System.out.println();
        }

        @Override
        public Object getLabel(SurveyResponseCell cell) {
            if (StringUtils.isEmpty(cell.getTags())) {
                return null;
            }
            AreaCodeEnum areaCodeEnum = AreaCodeEnum.getByName(cell.getTags());
            if (areaCodeEnum != null) {
                return areaCodeEnum.getName() + areaCodeEnum.getCode();
            }
            return cell.getTags();
        }
    }

}
