package cn.hanyi.survey.service.word;

import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.dto.question.DropDownDto;
import org.befun.core.utils.JsonHelper;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/28 11:28:27
 */
@Component
public class DownloadDropDown implements IDownloadSurvey{

    @Override
    public QuestionType type() {
        return QuestionType.DROP_DOWN;
    }

    /*
            品牌,年份,车型
            宝马,2018,B1
            宝马,2021,B2
            大众,2019,D1
            大众,2020,D2
            特斯拉,2020,T1
            特斯拉,2021,T2
     */
    @Override
    public String buildQuestion(SurveyQuestion question) {
        DropDownDto dropDown = JsonHelper.toObject(question.getConfigure(), DropDownDto.class);
        StringBuffer res = new StringBuffer();
        res.append(question.getCode()).append(".").append(question.getTitle()).append("["+question.getType().getText()+"]").append("<br/>");
        Optional.ofNullable(dropDown).ifPresent(x->{
             String props = x.getProps().stream().collect(Collectors.joining(","));
             res.append(props);
             x.getDatas().forEach(d->{
                 String data = d.stream().collect(Collectors.joining(","));
                 res.append(data);
             });
        });
        return res.toString();
    }
}















