package cn.hanyi.survey.service.response;

import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import org.apache.poi.ss.usermodel.Row;
import org.befun.auth.service.UserTaskService;

import java.util.concurrent.atomic.AtomicLong;


/**
 * easyExcel 不是被spring管理的
 * DownloadProgress 是被new出来的 注册到easyExcel中
 * <AUTHOR>
 */
public class DownloadProgressHandler implements RowWriteHandler {

    public static UserTaskService userTaskService;
    private AtomicLong taskId;

    public DownloadProgressHandler(Long taskId, UserTaskService userTaskService){
        if(this.taskId == null && taskId != null){
            this.taskId = new AtomicLong(taskId);
        }

        if (DownloadProgressHandler.userTaskService == null && userTaskService != null) {
            DownloadProgressHandler.userTaskService = userTaskService;
        }
    }

    @Override
    public void afterRowDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Integer relativeRowIndex, Boolean isHead) {
        if(taskId != null && userTaskService != null){
            userTaskService.appendTaskSuccessSize(taskId.get(), 1, false);
        }
    }


}
