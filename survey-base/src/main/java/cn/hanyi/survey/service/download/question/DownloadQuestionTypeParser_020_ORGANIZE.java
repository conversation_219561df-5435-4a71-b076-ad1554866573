package cn.hanyi.survey.service.download.question;

import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.core.entity.SurveyResponseCell;
import cn.hanyi.survey.service.download.IDownloadQuestionTypeParser;
import cn.hanyi.survey.service.download.dto.DownloadColumnQuestion;
import cn.hanyi.survey.service.download.dto.DownloadColumnQuestionGroup;
import cn.hanyi.survey.service.download.dto.ResponseDownloadContext;
import org.springframework.stereotype.Component;

@Component
public class DownloadQuestionTypeParser_020_ORGANIZE implements IDownloadQuestionTypeParser {

    @Override
    public QuestionType type() {
        return QuestionType.ORGANIZE;
    }

    @Override
    public DownloadColumnQuestionGroup buildGroup(ResponseDownloadContext context, SurveyQuestion question) {
        DownloadColumnQuestionGroup group = new DownloadColumnQuestionGroup(question, question.getItems(), question.getColumns());
        group.getColumns().add(new DownloadColumnQuestion_ORGANIZE(context, group));
        return group;
    }

    public static class DownloadColumnQuestion_ORGANIZE extends DownloadColumnQuestion {

        public DownloadColumnQuestion_ORGANIZE(ResponseDownloadContext context, DownloadColumnQuestionGroup group) {
            super(context, group);
        }

        @Override
        public Object getCode(SurveyResponseCell cell) {
            return cell.getStrValue().replaceAll(";", "/");
        }

        @Override
        public Object getLabel(SurveyResponseCell cell) {
            return getCode(cell);
        }
    }


}
