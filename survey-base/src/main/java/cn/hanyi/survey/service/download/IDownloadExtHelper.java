package cn.hanyi.survey.service.download;

import cn.hanyi.survey.core.entity.SurveyResponse;
import cn.hanyi.survey.service.download.dto.ResponseDownloadContext;
import cn.hanyi.survey.service.download.dto.ResponseDownloadFile;
import cn.hanyi.survey.service.download.dto.ResponseExportFile;
import cn.hanyi.survey.service.download.ext.DownloadExtType;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.handler.WriteHandler;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

public interface IDownloadExtHelper<F extends ResponseExportFile> {

    DownloadExtType extType();

    F buildFile(ResponseDownloadContext context);

    default void download(ResponseDownloadContext context, List<SurveyResponse> responseList, boolean sequential) {
        Optional.ofNullable(getFile(context)).ifPresent(file -> download(context, file, responseList, sequential));
    }

    void download(ResponseDownloadContext context, F file, List<SurveyResponse> responseList, boolean sequential);

    default void copyColumns(ResponseDownloadContext context) {
        Optional.ofNullable(getFile(context)).ifPresent(file -> copyColumns(context, file));
    }

    void copyColumns(ResponseDownloadContext context, F file);

    @SuppressWarnings("unchecked")
    default F getFile(ResponseDownloadContext context) {
        return (F) context.getExtFileMap().get(extType());
    }

    void writeFile(ResponseDownloadContext context,  List<ResponseDownloadFile> files,WriteHandler handler, ExcelTypeEnum type) throws IOException;
}
