package cn.hanyi.survey.service.download.ext;

import cn.hanyi.survey.client.service.SurveyRandomResultService;
import cn.hanyi.survey.core.constant.question.QuestionRandomType;
import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.core.entity.SurveyQuestionItem;
import cn.hanyi.survey.core.entity.SurveyRandomResult;
import cn.hanyi.survey.core.entity.SurveyResponse;
import cn.hanyi.survey.core.repository.SurveyRandomResultRepository;
import cn.hanyi.survey.core.utilis.RegularExpressionUtils;
import cn.hanyi.survey.service.download.IDownloadExtHelper;
import cn.hanyi.survey.service.download.dto.*;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.handler.WriteHandler;
import com.alibaba.excel.write.metadata.WriteSheet;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.entity.BaseEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Component
public class DownloadRandomResultHelper implements IDownloadExtHelper<ResponseExportRandomFile> {

    @Autowired
    private SurveyRandomResultRepository surveyRandomResultRepository;

    @Autowired
    private SurveyRandomResultService surveyRandomResultService;

    private static List<QuestionType> questionTypes = List.of(QuestionType.SINGLE_CHOICE, QuestionType.MULTIPLE_CHOICES, QuestionType.COMBOBOX,
            QuestionType.EVALUATION, QuestionType.SCORE_EVALUATION);


    @Override
    public DownloadExtType extType() {
        return DownloadExtType.random_result;
    }

    @Override
    public ResponseExportRandomFile buildFile(ResponseDownloadContext context) {
        Long randomResult = surveyRandomResultRepository.existsRandomResult(context.getSurveyId());
        if (randomResult != null) {
            ResponseExportRandomFile file = new ResponseExportRandomFile(context.getSafeTitle() + "_随机结果" + context.getDownloadType().getSuffix());
            DownloadColumnRandomGroup randomGroup = new DownloadColumnRandomGroup();
            buildGroup(context, file, randomGroup);
            file.setRandomGroup(randomGroup);
            return file;
        }
        return null;
    }

    //构建每个问题的索引和标题
    private void buildGroup(ResponseDownloadContext context, ResponseExportRandomFile file, DownloadColumnRandomGroup randomGroup) {
        boolean existGroup = surveyRandomResultRepository.existsBySurveyIdAndType(context.getSurveyId(), QuestionRandomType.GROUP);
        if (existGroup) {
            context.setExistGroupOrQuestion(true);
            for (SurveyQuestion group : context.getOriginSortGroups()) {
                randomGroup.getColumnMap().put(group.getId(), new DownloadColumnRandom(file.getRandomSize(), group.getCode() + "_display"));
                file.plusRandomSize();
            }
        }
        boolean existQuestion = surveyRandomResultRepository.existsBySurveyIdAndType(context.getSurveyId(), QuestionRandomType.QUESTION);
        if (existQuestion) {
            context.setExistGroupOrQuestion(true);
            for (SurveyQuestion question : context.getOriginSortQuestions()) {
                randomGroup.getColumnMap().put(question.getId(), new DownloadColumnRandom(file.getRandomSize(), question.getCode() + "_display"));
                file.plusRandomSize();
            }
        }
        boolean existItem = surveyRandomResultRepository.existsBySurveyIdAndType(context.getSurveyId(), QuestionRandomType.ITEM);
        if (existItem) {
            context.setExistItem(true);
            for (SurveyQuestion question : context.getOriginSortQuestions()) {
                buildItem(file, randomGroup, question);
            }
        }
    }


    public void buildItem(ResponseExportRandomFile file, DownloadColumnRandomGroup randomGroup, SurveyQuestion question) {
        //只记录支持随机选项的问题
        if (questionTypes.contains(question.getType())) {
            List<SurveyQuestionItem> items = question.getItems().stream().
                    sorted(Comparator.comparing(SurveyQuestionItem::getSequence)).collect(Collectors.toList());
            for (SurveyQuestionItem item : items) {
                if (question.getType().equals(QuestionType.EVALUATION) || question.getType().equals(QuestionType.SCORE_EVALUATION)) {
                    file.getQuestionItemMap().put(item.getId(), item.getConfigure());
                    if (StringUtils.isEmpty(item.getConfigure().trim())) {
                        continue;
                    }
                    String[] labels = item.getConfigure().split(",");
                    List<DownloadColumnRandomItem> columnRandoms = new ArrayList<>();
                    for (int i = 0; i < labels.length; i++) {
                        String title = String.format("%s_%s_%s_%s", question.getCode(), RegularExpressionUtils.replaceHtml(item.getText()), labels[i], "display");
                        columnRandoms.add(new DownloadColumnRandomItem(file.getRandomItemSize(), title, labels[i]));
                        file.plusItemRandomSize();
                    }
                    randomGroup.getColumnItemMap().put(item.getId(), columnRandoms);

                } else {
                    String title = String.format("%s_%s_%s", question.getCode(), RegularExpressionUtils.replaceHtml(item.getText()), "display");
                    randomGroup.getColumnItemMap().put(item.getId(), List.of(new DownloadColumnRandomItem(file.getRandomItemSize(), title, null)));
                    file.plusItemRandomSize();
                }
            }
        }
    }

    @Override
    public void download(ResponseDownloadContext context, ResponseExportRandomFile file, List<SurveyResponse> responseList, boolean sequential) {
        List<SurveyRandomResult> records;
        if (sequential) {
            long min = responseList.get(0).getId();
            long max = responseList.get(responseList.size() - 1).getId();
            records = getByRIdRange(context.getSurveyId(), min, max);
        } else {
            List<Long> rIds = responseList.stream().map(BaseEntity::getId).collect(Collectors.toList());
            records = getByRIds(context.getSurveyId(), rIds);
        }
        if (CollectionUtils.isNotEmpty(responseList)) {
            responseList.forEach(i -> fillId(file, i));
            responseList.forEach(i -> fillRandomItemId(file, i));
        }
        if (CollectionUtils.isNotEmpty(records)) {
            records.stream().filter(x -> x.getType().equals(QuestionRandomType.GROUP) || x.getType().equals(QuestionRandomType.QUESTION))
                    .forEach(i -> fillValue(file, i));
            records.stream().filter(x -> x.getType().equals(QuestionRandomType.ITEM))
                    .forEach(i -> fillRandomItemValue(file, i));
        }
    }

    private void fillId(ResponseExportRandomFile file, SurveyResponse response) {
        Object[] row = new Object[file.getRandomSize()];
        row[0] = response.getId().toString();
        file.getRandomRowMap().put(response.getId(), row);
    }

    private void fillRandomItemId(ResponseExportRandomFile file, SurveyResponse response) {
        Object[] row = new Object[file.getRandomItemSize()];
        row[0] = response.getId().toString();
        file.getRandomItemRowMap().put(response.getId(), row);
    }

    private void fillValue(ResponseExportRandomFile file, SurveyRandomResult record) {
        if (record.getResponseId() == null && record.getQuestionId() != null) {
            return;
        }
        DownloadColumnRandom columnRandom = file.getRandomGroup().getColumnMap().get(record.getQuestionId());
        if (columnRandom == null) {
            return;
        }
        Object[] row = file.getRandomRowMap().get(record.getResponseId());
        if (row == null) {
            return;
        }
        //答卷行 问题索引对应的值  填充excel表中的值
        row[columnRandom.getIndex()] = columnRandom.getCode(record);
    }

    private void fillRandomItemValue(ResponseExportRandomFile file, SurveyRandomResult record) {
        if (record.getResponseId() == null && record.getQuestionId() != null) {
            return;
        }

        List<DownloadColumnRandomItem> columnRandoms = file.getRandomGroup().getColumnItemMap().get(record.getItemId());
        if (columnRandoms == null) {
            return;
        }

        Object[] row = file.getRandomItemRowMap().get(record.getResponseId());
        if (row == null) {
            return;
        }
        //选项里包含的标签
        String label = file.getQuestionItemMap().get(record.getItemId());
        columnRandoms.forEach(columnRandom -> {
            if (label == null) {
                //不是评价题
                row[columnRandom.getIndex()] = record.getIsShow() != null && record.getIsShow() ? 1 : 0;
            } else {
                row[columnRandom.getIndex()] = record.getItemConfig() == null ? 0 : record.getItemConfig().contains(columnRandom.getLabel()) ? 1 : 0;
            }
        });
    }

    @Override
    public void copyColumns(ResponseDownloadContext context, ResponseExportRandomFile fileRandom) {
        //临时记录的标题
        List<String> originHeaders = fileRandom.getRandomGroup().originHeaders();
        List<List<String>> formatHeaders = fileRandom.getHeaders();

        //临时保存的每个答卷对应的数据
        Collection<Object[]> originRows = fileRandom.getRandomRowMap().values();
        List<List<Object>> formatRows = fileRandom.getRows();
        //List<Integer> deleteColumns = fileRandom.getRandomGroup().deleteColumns();
        IntStream.range(0, fileRandom.getRandomSize()).forEach(i -> {
            String header = originHeaders.get(i);
            formatHeaders.add(List.of(header));
        });

        //随机答卷的数量
        originRows.forEach(originRow -> {
            List<Object> row = new ArrayList<>();
            //一行的答卷
            IntStream.range(0, fileRandom.getRandomSize()).forEach(i -> {
                Object v = originRow[i];
                row.add(v == null ? 1 : v);
            });
            formatRows.add(row);
        });

        //选项
        List<String> originItemHeaders = fileRandom.getRandomGroup().originItemHeaders();
        List<List<String>> formatItemHeaders = fileRandom.getItemHeader();
        IntStream.range(0, fileRandom.getRandomItemSize()).forEach(i -> {
            String header = originItemHeaders.get(i);
            formatItemHeaders.add(List.of(header));
        });

        Collection<Object[]> originItemRows = fileRandom.getRandomItemRowMap().values();
        List<List<Object>> formatItemRows = fileRandom.getItemRows();
        originItemRows.forEach(originItemRow -> {
            List<Object> row = new ArrayList<>();
            //一行的答卷
            IntStream.range(0, fileRandom.getRandomItemSize()).forEach(i -> {
                Object v = originItemRow[i];
                row.add(v == null ? 1 : v);
            });
            formatItemRows.add(row);
        });

        fileRandom.getRandomRowMap().clear();
        fileRandom.getRandomItemRowMap().clear();
    }

    private List<SurveyRandomResult> getByRIdRange(Long surveyId, long minRId, long maxRId) {
        return surveyRandomResultRepository.findBySurveyIdAndResponseIdBetween(surveyId, minRId, maxRId);
    }

    private List<SurveyRandomResult> getByRIds(Long surveyId, List<Long> rIds) {
        return surveyRandomResultRepository.findBySurveyIdAndResponseIdIn(surveyId, rIds);
    }

    @Override
    public void writeFile(ResponseDownloadContext context, List<ResponseDownloadFile> files, WriteHandler handler, ExcelTypeEnum type) throws IOException {
        ResponseExportRandomFile file = (ResponseExportRandomFile) context.getExtFileMap().get(extType());
        if (ExcelTypeEnum.XLSX.equals(type)) {
            writeFileSheetsByXLSX(context, files, file, handler, type);
        } else {
            if (context.getExistGroupOrQuestion()) {
                writeFileSheetsByCSV(files, handler, type, file.getHeaders(), file.getRows(), "题组、题目随机表.csv");
            }
            if (context.getExistItem()) {
                writeFileSheetsByCSV(files, handler, type, file.getItemHeader(), file.getItemRows(), "选项随机表.csv");
            }
        }
    }

    public void writeFileSheetsByXLSX(ResponseDownloadContext context, List<ResponseDownloadFile> files, ResponseExportRandomFile file, WriteHandler handler, ExcelTypeEnum type) throws IOException {
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        ExcelWriterBuilder excelWriterBuilder = EasyExcel.write(bos).excelType(type).useDefaultStyle(false);
        excelWriterBuilder.registerWriteHandler(handler);
        ExcelWriter excelWriter = excelWriterBuilder.build();
        if (context.getExistGroupOrQuestion()) {
            WriteSheet question = EasyExcel.writerSheet(1, "题组、问题随机表").head(file.getHeaders()).build();
            excelWriter.write(file.getRows(), question);
        }
        if (context.getExistItem()) {
            WriteSheet item = EasyExcel.writerSheet(2, "选项随机表").head(file.getItemHeader()).build();
            excelWriter.write(file.getItemRows(), item);
        }
        excelWriter.finish();
        bos.flush();
        files.add(new ResponseDownloadFile(file.getFileName(), bos.toByteArray()));
        file.clear();
    }

    public void writeFileSheetsByCSV(List<ResponseDownloadFile> files, WriteHandler handler, ExcelTypeEnum type,
                                     List<List<String>> headers, List<List<Object>> rows, String fileName) throws IOException {
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        ExcelWriterBuilder excelWriterBuilder = EasyExcel.write(bos).excelType(type).useDefaultStyle(false);
        if (handler != null) {
            excelWriterBuilder.registerWriteHandler(handler);
        }

        excelWriterBuilder.head(headers).sheet(fileName).doWrite(rows);
        bos.flush();
        files.add(new ResponseDownloadFile(fileName, bos.toByteArray()));
        headers.clear();
        rows.clear();
    }
}
