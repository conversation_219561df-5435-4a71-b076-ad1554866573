package cn.hanyi.survey.service.channel;

import cn.hanyi.ctm.entity.Customer;
import cn.hanyi.survey.core.constant.channel.ChannelType;
import cn.hanyi.survey.core.dto.channel.ChannelSelectCustomerDto;

import java.util.List;
import java.util.Optional;
import java.util.function.BiFunction;
import java.util.function.Consumer;

public abstract class ChannelSendDataHelper {

    public abstract long countCustomers(Long orgId, Long userId, ChannelType channelType, ChannelSelectCustomerDto select);

    public abstract void foreachCustomers(Long orgId, Long userId, ChannelType channelType, int pageSize, ChannelSelectCustomerDto select, Consumer<List<Customer>> consumerCustomers);

    protected void pageable(long count, int pageSize, BiFunction<Integer, Integer, List<Customer>> getPageCustomers, Consumer<List<Customer>> consumerCustomers) {
        if (count > 0) {
            int page = 1;
            boolean hasNext = true;
            while (hasNext) {
                List<Customer> customers = getPageCustomers.apply(page, pageSize);
                Optional.ofNullable(customers).ifPresent(consumerCustomers);
                page++;
                hasNext = ((page - 1L) * pageSize) < count;
            }
        }
    }

}
