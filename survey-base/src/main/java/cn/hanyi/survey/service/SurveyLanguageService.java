package cn.hanyi.survey.service;

import cn.hanyi.cem.core.dto.task.TaskSurveyTranslateDto;
import cn.hanyi.survey.core.constant.survey.TranslateStatus;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyLanguage;
import cn.hanyi.survey.core.entity.SurveyLanguageDto;
import cn.hanyi.survey.core.repository.SurveyLanguageRepository;
import cn.hanyi.survey.dto.SurveyLanguageTranslateRequestDto;
import cn.hanyi.survey.dto.SurveyLanguageTranslateTaskDto;
import cn.hanyi.survey.workertrigger.SurveyTaskTrigger;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.constant.UserTaskType;
import org.befun.auth.service.UserTaskService;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.MapperService;
import org.befun.core.utils.JsonHelper;
import org.befun.task.entity.TaskProgress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;


@Service
@Slf4j
public class SurveyLanguageService {

    @Autowired
    private SurveyLanguageRepository surveyLanguageRepository;

    @Autowired
    private MapperService mapperService;

    @Autowired
    private UserTaskService userTaskService;

    @Autowired
    private SurveyTaskTrigger surveyTaskTrigger;

    public SurveyLanguageDto findBySurveyAndLanguage(Long sid, String type) {
        Survey survey = new Survey();
        survey.setId(sid);
        Optional<SurveyLanguage> language = surveyLanguageRepository.findBySurveyAndLanguage(survey, type);
        return language.isEmpty() ? null : mapperService.map(language.get(), SurveyLanguageDto.class);
    }

    public SurveyLanguage findById(Long languageId) {
        Optional<SurveyLanguage> language = surveyLanguageRepository.findById(languageId);
        return language.isEmpty() ? null : language.get();
    }

    public Boolean translateAsync(Long sid, SurveyLanguageTranslateRequestDto requestDto) {
        SurveyLanguage surveyLanguage = findById(requestDto.getLanguageId());
        try {
            if (surveyLanguage == null) {
                throw new BadRequestException();
            }
            TaskSurveyTranslateDto taskDto = new TaskSurveyTranslateDto();
            taskDto.setOrgId(TenantContext.getCurrentTenant());
            taskDto.setUserId(TenantContext.getCurrentUserId());
            taskDto.setLanguage(surveyLanguage.getLanguage());
            taskDto.setSid(sid);
            taskDto.setPages(requestDto.getPages().stream().map(Enum::name).collect(Collectors.toList()));
            taskDto.setRemarkIds(requestDto.getRemarkIds());
            taskDto.setLanguageId(requestDto.getLanguageId());
            surveyLanguage.setStatus(TranslateStatus.TRANSLATING);
            surveyLanguage.setSourceQuestions(requestDto.getQuestions());
            surveyLanguageRepository.save(surveyLanguage);
            TaskProgress progress = userTaskService.createTask(TenantContext.getCurrentTenant(),
                    TenantContext.getCurrentUserId(), UserTaskType.translateSurvey, 0, null, requestDto.getLanguageId());
            surveyTaskTrigger.translateSurvey(TenantContext.getCurrentTenant(), TenantContext.getCurrentUserId(), sid, JsonHelper.toJson(taskDto));
            log.info("translate survey: {}, task progress: {}", sid, progress.getId());
            return Boolean.TRUE;
        } catch (Exception ex) {
            log.error("failed to trigger translate task");
            ex.printStackTrace();
            return Boolean.FALSE;
        }
    }
}
