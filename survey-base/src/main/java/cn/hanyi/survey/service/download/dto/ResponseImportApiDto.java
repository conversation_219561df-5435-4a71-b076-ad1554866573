package cn.hanyi.survey.service.download.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.*;

@Setter
@Getter
public class ResponseImportApiDto {
    private Date startTime;
    private Date finishTime;
    private String ip;
    private Integer duration;
    private String externalUserId;
    private String departmentCode;
    private Map<String,Object> parameters = new HashMap<>();
    private List<ResponseImportDataDto> data = new ArrayList<>();
}
