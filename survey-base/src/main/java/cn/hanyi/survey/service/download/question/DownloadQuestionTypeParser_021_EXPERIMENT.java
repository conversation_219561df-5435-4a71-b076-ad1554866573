package cn.hanyi.survey.service.download.question;

import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.core.entity.SurveyResponseCell;
import cn.hanyi.survey.core.utilis.RegularExpressionUtils;
import cn.hanyi.survey.dto.ExperimentDto;
import cn.hanyi.survey.service.download.IDownloadQuestionTypeParser;
import cn.hanyi.survey.service.download.dto.DownloadColumnQuestion;
import cn.hanyi.survey.service.download.dto.DownloadColumnQuestionGroup;
import cn.hanyi.survey.service.download.dto.ResponseDownloadContext;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.befun.core.utils.JsonHelper;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

@Component
public class DownloadQuestionTypeParser_021_EXPERIMENT implements IDownloadQuestionTypeParser {

    @Override
    public QuestionType type() {
        return QuestionType.EXPERIMENT;
    }

    @Override
    public DownloadColumnQuestionGroup buildGroup(ResponseDownloadContext context, SurveyQuestion question) {
        DownloadColumnQuestionGroup group = new DownloadColumnQuestionGroup(question, question.getItems(), question.getColumns());
        AtomicInteger indexItem = new AtomicInteger(1);
        List<ExperimentAttr> attrs = getAttrs(question.getConfigure());
        group.getQuestionItems().forEach(i -> {
            int itemIndex = indexItem.getAndIncrement();
            AtomicInteger indexAttr = new AtomicInteger(1);
            attrs.forEach(a -> {
                String text = RegularExpressionUtils.replaceHtml(i.getText());
                String attribure = RegularExpressionUtils.replaceHtml(a.getAttribute());
                String label = group.getCode() + "_" + group.getTitle() + "_" + text + "_" + attribure;
                String code = group.getCode() + "_" + itemIndex + "_" + indexAttr.getAndIncrement();
                group.getColumns().add(new DownloadColumnQuestion_EXPERIMENT_item(context, group, label, code,
                        i.getValue(), a.getId(), a.getValueReplaceMap(), a.getValueIndexMap()));
            });
        });
        return group;
    }

    private List<ExperimentAttr> getAttrs(String configure) {
        List<ExperimentAttr> attrs = JsonHelper.toList(configure, ExperimentAttr.class);
        if (CollectionUtils.isEmpty(attrs)) {
            return List.of();
        } else {
            attrs.forEach(attr -> {
                Optional.ofNullable(attr.getColumn()).ifPresent(values -> {
                    AtomicInteger indexValue = new AtomicInteger(1);
                    values.forEach(value -> {
                        attr.getValueReplaceMap().put(RegularExpressionUtils.replaceHtml(value.getValue()), RegularExpressionUtils.replaceHtml(value.getValue()));
                        attr.getValueIndexMap().put(value.getValue(), indexValue.getAndIncrement());
                    });
                });
            });
            return attrs;
        }
    }

    public static class DownloadColumnQuestion_EXPERIMENT_item extends DownloadColumnQuestion {

        private final String item;
        private final String attr;
        private final Map<String, String> valueReplaceMap;
        private final Map<String, Integer> valueIndexMap;

        public DownloadColumnQuestion_EXPERIMENT_item(ResponseDownloadContext context, DownloadColumnQuestionGroup group,
                                                      String label, String code,
                                                      String item, String attr,
                                                      Map<String, String> valueReplaceMap,
                                                      Map<String, Integer> valueIndexMap) {
            super(context, group, label, code);
            this.item = item;
            this.attr = attr;
            this.valueReplaceMap = valueReplaceMap;
            this.valueIndexMap = valueIndexMap;
        }

        @Override
        public Object getCode(SurveyResponseCell cell) {
            return Optional.ofNullable(getExperimentMap(cell))
                    .flatMap(itemMap -> Optional.ofNullable(itemMap.get(item)))
                    .flatMap(attrMap -> Optional.ofNullable(RegularExpressionUtils.replaceHtml(attrMap.get(attr))))
                    .map(valueReplaceMap::get).orElse(null);
        }

        @Override
        public Object getLabel(SurveyResponseCell cell) {
            return getCode(cell);
        }

        /**
         * attributes:[{"属性1": ["水平1","EtHl9"]}]
         */
        private Map<String/*item*/, Map<String/*attr*/, String/*value*/>> getExperimentMap(SurveyResponseCell cell) {
            if (cell.getExperimentValue() != null) {
                return cell.getExperimentValue();
            }
            List<ExperimentDto> itemList = JsonHelper.toList(cell.getStrValue(), ExperimentDto.class);
            Map<String/*item*/, Map<String/*attr*/, String/*value*/>> experimentValue = new HashMap<>();
            if (CollectionUtils.isNotEmpty(itemList)) {
                itemList.forEach(item -> {
                    List<Map<String, List<String>>> attrList = item.getAttributes();
                    if (CollectionUtils.isNotEmpty(attrList)) {
                        attrList.forEach(attr -> {
                            Collection<List<String>> values = attr.values();
                            if (CollectionUtils.isNotEmpty(values)) {
                                List<String> first = values.iterator().next();
                                if (first != null && first.size() == 2) {
                                    experimentValue.computeIfAbsent(item.getCode(), k -> new HashMap<>()).put(first.get(1), first.get(0));
                                }
                            }
                        });
                    }
                });
            }
            cell.setExperimentValue(experimentValue);
            return experimentValue;
        }
    }

    @Getter
    @Setter
    public static class ExperimentAttr {
        private String attribute;
        private String id;
        private List<ExperimentValue> column;
        private Map<String, String> valueReplaceMap = new HashMap<>();
        private Map<String, Integer> valueIndexMap = new HashMap<>();
    }

    @Getter
    @Setter
    public static class ExperimentValue {
        private String value;
    }

//    {
//        "attribute": "属性1",
//            "sequence": "1",
//            "column": [
//        {
//            "value": "水平1",
//                "sequence": "1"
//        },
//        {
//            "value": "水平2",
//                "sequence": "2"
//        }
//        ],
//        "id": "EtHl9"
//    }

//    {
//        "groupName": {
//        "value": "TmjTB",
//                "text": "组合1",
//                "excludeOther": false,
//                "enableTextInput": false,
//                "code": "TmjTB",
//                "isFix": false,
//                "isRequired": false
//    },
//        "attributes": [
//        {
//            "属性1": [
//            "水平1",
//                    "EtHl9"
//                ]
//        },
//        {
//            "属性2": [
//            "水平2",
//                    "E2AzO"
//                ]
//        }
//        ],
//        "code": "TmjTB"
//    }

}
