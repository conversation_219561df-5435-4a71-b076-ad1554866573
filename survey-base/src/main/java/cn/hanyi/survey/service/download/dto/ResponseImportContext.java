package cn.hanyi.survey.service.download.dto;

import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ResponseImportContext {
    private Long surveyId;
    private Integer count = 0;
    @JsonIgnore
    private Integer dataIndex = 0;
    private String url;
    private Boolean submit = false;
    // true 表示异步下载数据，false 表示同步下载
    private Boolean async = true;

    @JsonIgnore
    private Long orgId;
    @JsonIgnore
    private Long userId;

    private LinkedHashMap<Integer, String> headers = new LinkedHashMap<>();
    private List<String> errorList = new ArrayList<>();
    private List<Map<Integer, String>> responseList = new ArrayList<>();
    @JsonIgnore
    private List<SurveyQuestion> questionList = new ArrayList<>();
    @JsonIgnore
    private Survey survey;

    //导入的答卷数量
    private AtomicInteger responseNum = new AtomicInteger(0);

    public ResponseImportContext(Long surveyId) {
        this.surveyId = surveyId;
    }

    public ResponseImportContext(Long surveyId, String url, Boolean submit) {
        this.surveyId = surveyId;
        this.url = url;
        this.submit = submit;
    }

    public ResponseImportContext(Long surveyId, Long orgId, Long userId) {
        this.surveyId = surveyId;
        this.orgId = orgId;
        this.userId = userId;
        this.submit = true;
    }

    public void plusResponseNum() {
        this.responseNum.incrementAndGet();
    }
}
