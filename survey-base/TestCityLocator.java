import cn.hanyi.survey.service.InMemoryCityLocator;

public class TestCityLocator {
    public static void main(String[] args) {
        try {
            InMemoryCityLocator locator = new InMemoryCityLocator();
            System.out.println("开始加载Shapefile数据...");
            locator.loadData("/Users/<USER>/Downloads/city.shp");
            
            System.out.println("\n开始测试坐标查询...");
            
            // 测试北京坐标 (39.9042° N, 116.4074° E)
            String city = locator.getCity(39.9042, 116.4074);
            System.out.println("北京坐标 (39.9042, 116.4074) 所在城市: " + city);

            // 测试上海坐标 (31.2304° N, 121.4737° E)
            city = locator.getCity(31.2304, 121.4737);
            System.out.println("上海坐标 (31.2304, 121.4737) 所在城市: " + city);
            
            // 测试广州坐标 (23.1291° N, 113.2644° E)
            city = locator.getCity(23.1291, 113.2644);
            System.out.println("广州坐标 (23.1291, 113.2644) 所在城市: " + city);
            
            // 测试深圳坐标 (22.5431° N, 114.0579° E)
            city = locator.getCity(22.5431, 114.0579);
            System.out.println("深圳坐标 (22.5431, 114.0579) 所在城市: " + city);
            
            // 测试一个可能不在任何城市的坐标
            city = locator.getCity(0.0, 0.0);
            System.out.println("海洋坐标 (0.0, 0.0) 所在城市: " + city);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
