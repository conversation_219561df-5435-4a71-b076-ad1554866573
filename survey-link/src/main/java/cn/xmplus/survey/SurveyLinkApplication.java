package cn.xmplus.survey;

import cn.hanyi.common.file.storage.FileStorageAutoConfiguration;
import org.befun.auth.Aspects.TenantContext;
import org.befun.core.repository.impl.BaseRepositoryImpl;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

@SpringBootApplication(
        scanBasePackages = {
                "org.befun.core",
                "cn.xmplus.survey",
                "cn.hanyi.survey.core.service"
        },
        exclude = {
                FileStorageAutoConfiguration.class,
        })
public class SurveyLinkApplication implements ApplicationRunner, BeanFactoryAware {

    private BeanFactory factory;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        factory.getBean(TenantContext.class);
    }

    @Override
    public void setBeanFactory(BeanFactory beanFactory) throws BeansException {
        factory = beanFactory;
    }

    @Configuration
    @EntityScan({
            "cn.hanyi.survey.core.entity",
            "org.befun.core.entity",
            "org.befun.auth.entity",
            "org.befun.task.entity",
            "org.befun.extension.entity",
    })
    @EnableJpaRepositories(
            basePackages = {
                    "cn.hanyi.survey.core.repository",
                    "org.befun.core.repo",
                    "org.befun.auth.repository",
                    "org.befun.task.repository",
                    "org.befun.extension.repository",
            },
            repositoryBaseClass = BaseRepositoryImpl.class)
    public class SurveyJPAConfig {
    }

    @Bean
    @ConditionalOnMissingBean
    public TenantContext openApiTenantContext() {
        return new TenantContext();
    }

    public static void main(String[] args) {
        SpringApplication.run(SurveyLinkApplication.class, args);
    }
}