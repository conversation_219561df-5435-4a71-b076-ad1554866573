package cn.xmplus.survey.controller;

import cn.hanyi.survey.core.dto.SurveyLinkDto;
import cn.hanyi.survey.core.dto.survey.CreateShortLinkDto;
import cn.hanyi.survey.core.service.SurveyLinkService;
import io.swagger.v3.oas.annotations.Operation;
import org.befun.core.dto.ResourceResponseDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@PreAuthorize("isAuthenticated()")
public class SurveyLinkController {

    @Autowired
    private SurveyLinkService service;

    @PostMapping(value = "surveys/createShortLink")
    @Operation(summary = "1.10.3-创建问卷短链")
    public ResourceResponseDto<SurveyLinkDto> createShortLink(@Valid @RequestBody CreateShortLinkDto dto) {
        return new ResourceResponseDto<>(service.createShortLink(dto, 2, false));
    }

}
