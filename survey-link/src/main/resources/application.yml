xmplus:
  domain: ${XMPLUS_DOMAIN:https://dev.xmplus.cn}
  short: ${XMPLUS_SHORT:https://dev-t.xmplus.cn}
server:
  forward-headers-strategy: native
  shutdown: graceful
  port: ${PORT:8085}
  servlet:
    context-path: /api/link
  error:
    whitelabel:
      enabled: false
  tomcat:
    accept-count: 100 # 并发线程数
    threads:
      max: ${TOMCAT_MAX_THREADS:400}


spring:
  lifecycle:
    timeout-per-shutdown-phase: 60s
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
  mvc:
    throw-exception-if-no-handler-found: true
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ${MYSQL_URL:*********************************************************************************************************************************}
    username: ${MYSQL_USER:surveyuser}
    password: ${MYSQL_PASSWORD:C1E9M-P2l0t}
    hikari:
      connection-init-sql: SET NAMES utf8mb4
      connection-test-query: SELECT 1 FROM DUAL
      maximum-pool-size: ${MYSQL_MAX_POOL_SIZE:10} # 最大连接数, 核心数2倍+核心数
  jpa:
    generate-ddl: false
    show-sql: ${SHOW_SQL:true}
    hibernate:
      ddl-auto: none
    properties:
      hibernate:
#        dialect: cn.hanyi.survey.dialect.ExtendMySqlDialect
        enable_lazy_load_no_trans: true
        # 批处理
        jdbc.batch_size: 50
        order_inserts: true
        #        generate_statistics: true
        SQL_SLOW: ${LOG_LEVEL:info}
        session:
          events:
            log:
              LOG_QUERIES_SLOWER_THAN_MS: 1000
  #        connection.handling_mode: DELAYED_ACQUISITION_AND_RELEASE_AFTER_TRANSACTION
  data:
    redis.repositories.enabled: false
    jdbc.repositories.enabled: false
    rest:
      default-media-type: application/json
  cache:
    type: redis
    redis.time-to-live: 60m
  redis:
    host: ${REDIS_HOST:*********}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:bsxrzQ2l2zm}
logging:
  level:
    root: ${LOG_LEVEL:info}
    org.befun: ${LOG_LEVEL_BEFUN:info}
    org.hibernate:
      SQL: ${LOG_LEVEL_SQL:error}
      type.descriptor.sql: ${LOG_LEVEL_SQL:error}

springdoc:
  swagger-ui.enabled: ${ENABLE_DOC:false}
  api-docs.enabled: ${ENABLE_DOC:false}

befun:
  server:
    enable-open-api-filter: true
  extension:
    http-log.enable: ${LOG_HTTP:false}
    shorturl:
      root: ${xmplus.short}
      survey-client-prefix: ${SURVEY_CLIENT_PREFIX:https://dev.xmplus.cn/lite/l}
