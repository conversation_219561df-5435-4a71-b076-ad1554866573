package cn.hanyi.survey.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration()
@ConfigurationProperties(prefix = "survey.quota.sync")
public class QuotaProperties {

    private int sizePerPage = 500;
    private int autoFailureInterval = 180; // 如果活跃时间大于180秒，则标记任务失败
}
