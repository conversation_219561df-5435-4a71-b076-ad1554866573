package cn.hanyi.survey.service.quota;

import cn.hanyi.survey.properties.QuotaProperties;
import cn.hanyi.survey.service.quota.bean.*;
import cn.hanyi.survey.workertrigger.ISurveyTaskTrigger;
import cn.hanyi.survey.workertrigger.dto.TaskQuotaSyncDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.constant.UserTaskType;
import org.befun.auth.service.UserTaskService;
import org.befun.core.exception.BadRequestException;
import org.befun.core.utils.JsonHelper;
import org.befun.task.dto.TaskProgressDto;
import org.befun.task.entity.TaskProgress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@Service
@Primary
@SuppressWarnings({"rawtypes", "unchecked"})
public class QuotaAllMatchLimit extends AbstractQuotaLimit {

    @Autowired
    private QuotaProperties quotaProperties;
    @Autowired
    private ISurveyTaskTrigger surveyTaskTrigger;
    @Autowired
    private UserTaskService userTaskService;

    /**
     * 同步配额
     * 每次启动问卷的时候，如果开启了配额，则会调用此方法，同步配额
     *
     * @param surveyId            问卷id
     * @param allQuotas           最新的所有的配额（配额id和配额的最大值）
     * @param syncCemQuotas       需要同步的 cem 配额
     * @param countCemResponse    需要重新计算配额的 cem 答卷总数
     * @param syncAdminxQuotas    需要同步的 adminx 配额
     * @param countAdminxResponse 需要重新计算配额的 adminx 答卷总数
     */
    public boolean syncQuotaStat(
            Long orgId, Long surveyId,
            List<QuotaInit> allQuotas,
            List<Long> syncCemQuotas,
            Long countCemResponse,
            List<Long> syncAdminxQuotas,
            Long countAdminxResponse) {
        requireSurveyId(surveyId);
        if (CollectionUtils.isEmpty(allQuotas)) {
            throw new BadRequestException("allQuotas 不能为空");
        }
        if (syncLock(surveyId)) {
            boolean success = true;
            boolean unlock = true;
            QuotaStat stat;
            try {
                stat = QuotaStat.init(surveyId, allQuotas);
                // 如果已经存在统计数据，则需要比较是否需要删除一些无效的数据
                // 不在 allQuotas 中的配额 和 syncQuotas 中的配额 需要删除
                getQuotaStatFromRedis(surveyId).ifPresent(
                        oldStat -> syncDeleteQuota(surveyId, oldStat, allQuotas, syncCemQuotas, syncAdminxQuotas));
                // 重置统计数据
                resetQuotaStat(stat);
                TaskProgress taskProgress = null;
                // 重新计算配额
                // cem 配额
                if (countCemResponse != null && countCemResponse > 0 && CollectionUtils.isNotEmpty(syncCemQuotas)) {
                    success = false;
                    unlock = false;
                    taskProgress = createTask(orgId, surveyId, countCemResponse, countAdminxResponse);
                    addSyncTask(true, surveyId, countCemResponse, syncCemQuotas, taskProgress);
                }
                // adminx 配额
                if (countAdminxResponse != null && countAdminxResponse > 0 && CollectionUtils.isNotEmpty(syncAdminxQuotas)) {
                    success = false;
                    unlock = false;
                    if (taskProgress == null) {
                        taskProgress = createTask(orgId, surveyId, 0, countAdminxResponse);
                    }
                    addSyncTask(false, surveyId, countAdminxResponse, syncAdminxQuotas, taskProgress);
                }
            } catch (Throwable e) {
                unlock = true;
                log.error("问卷({})，配额同步失败", surveyId, e);
                throw new BadRequestException("配额同步失败");
            } finally {
                if (unlock) {
                    syncUnLock(surveyId);
                }
            }
            return success;
        } else {
            log.error("问卷({})，正在同步配额", surveyId);
            throw new BadRequestException("正在同步配额");
        }
    }

    private TaskProgress createTask(Long orgId, Long surveyId, long countCemResponse, long countAdminxResponse) {
        return userTaskService.createTask(orgId, null, UserTaskType.quotaSync,
                (int) (countCemResponse + countAdminxResponse), null, surveyId);
    }

    private void addSyncTask(boolean cem, long surveyId, long count, List<Long> syncQuotas, TaskProgress taskProgress) {
        int size = quotaProperties.getSizePerPage();
        int mod = (int) (count % size);
        int div = (int) (count / size);
        int maxPage = mod > 0 ? (div + 1) : div;
        for (int page = 0; page < maxPage; page++) {
            int realSize = page == maxPage - 1 ? mod : size;
            TaskQuotaSyncDto dto = new TaskQuotaSyncDto(cem, taskProgress.getId(), surveyId, page, size, realSize, syncQuotas);
            surveyTaskTrigger.quotaSync(taskProgress.getOrgId(), taskProgress.getUserId(), dto);
        }
    }

    /**
     * 找到无效的配额数据，并删除
     */
    private void syncDeleteQuota(Long surveyId, QuotaStat serverStat,
                                 List<QuotaInit> allQuotas, List<Long> syncCemQuotas, List<Long> syncAdminxQuotas) {
        // 不在 allQuotas
        Set<String> deleteTokenKeys = serverStat.items.stream()
                .filter(i -> allQuotas.stream().noneMatch(j -> i.quotaId == j.quotaId))
                .map(k -> getQuotaTokenKey(surveyId, k.quotaId))
                .collect(Collectors.toSet());
        // 在 syncQuotas
        syncCemQuotas.forEach(i -> deleteTokenKeys.add(getQuotaTokenKey(surveyId, i)));
        syncAdminxQuotas.forEach(i -> deleteTokenKeys.add(getQuotaTokenKey(surveyId, i)));
        log.info("问卷({})，以下配额将被删除：{}", surveyId, String.join(",", deleteTokenKeys));
        stringRedisTemplate.delete(deleteTokenKeys);
    }

    /**
     * 同步的时候批量添加 答卷id 到匹配的配额列表中
     */
    public void syncBatchAddQuota(Long surveyId, Map<Long, List<Long>> responseIds) {
        if (MapUtils.isNotEmpty(responseIds)) {
            redisBatchOpt(operations -> {
                SetOperations setOpt = operations.opsForSet();
                responseIds.forEach((k, v) -> {
                    if (k != null && CollectionUtils.isNotEmpty(v)) {
                        String key = getQuotaTokenKey(surveyId, k);
                        Object[] ids = v.stream().map(Objects::toString).toArray();
                        setOpt.add(key, ids);
                    }
                });
                return null;
            });
        }
    }

    /**
     * 重置配额统计数据
     * 先全部清空，然后重新计算
     */
    private void resetQuotaStat(QuotaStat stat) {
        String statKey = getQuotaStatKey(stat.surveyId);
        stringRedisTemplate.delete(statKey);
        SetOperations<String, String> setOpt = getSetOpt();
        stat.items.parallelStream().forEach(i -> {
            i.use = unbox(setOpt.size(getQuotaTokenKey(stat.surveyId, i.quotaId)));
        });
        getHashOpt().putAll(statKey, stat.toCache());
    }

    /**
     * 查询同步配额进度
     */
    @Override
    public TaskProgressDto syncProgress(Long orgId, Long surveyId) {
        Optional<TaskProgress> progress = userTaskService.getLastByType(orgId, null, UserTaskType.quotaSync, surveyId);
        return progress.map(taskProgress -> userTaskService.progress(taskProgress.getId(), quotaProperties.getAutoFailureInterval())).orElse(null);
    }

    @Override
    public void syncCompleted(Long surveyId, boolean success) {
        if (success) {
            statQuota(getQuotaStatThrowable(surveyId));
        }
        syncUnLock(surveyId);
    }

    /**
     * 从 redis 中获取配额统计信息
     */
    private Optional<QuotaStat> getQuotaStatFromRedis(Long surveyId) {
        requireSurveyId(surveyId);
        return QuotaStat.parse(surveyId, getHashOpt().entries(getQuotaStatKey(surveyId)));
    }

    /**
     * 配额id获取配额使用情况
     */
    public Long getOneQuotaUseStat(Long surveyId, Long eid) {
        requireSurveyId(surveyId);
        String use = getHashOpt().get(getQuotaStatKey(surveyId), getQuotaStatUseHashKey(eid));
        return !StringUtils.isNumeric(use) ? 0L : Long.parseLong(use);
    }

    /**
     * 查询配额进度
     */
    @Override
    public List<QuotaStatItem> getQuotaStat(Long surveyId) {
        return getQuotaStatFromRedis(surveyId).map(i -> i.items).orElse(List.of());
    }

    /**
     * 获取配额的统计信息
     */
    private QuotaStat getQuotaStatThrowable(Long surveyId) {
        return getQuotaStatFromRedis(surveyId).orElseThrow(() -> new BadRequestException(String.format("问卷：%d 的配额统计未初始化", surveyId)));
    }

    /**
     * 查询是否有配额
     */
    public boolean hasQuota(Long surveyId, List<Long> matchQuoteIds) {
        requireSurveyId(surveyId);
        requireNotSync(surveyId);
        return getQuotaStatThrowable(surveyId).hasQuota(matchQuoteIds);
    }

    /**
     * 使用配额
     */
    public boolean useQuota(Long surveyId, Long responseId, List<Long> matchQuoteIds) {
        requireSurveyId(surveyId);
        requireResponseId(responseId);
        requireNotSync(surveyId);
        if (CollectionUtils.isEmpty(matchQuoteIds)) {
            throw new BadRequestException("匹配的配额不能为空");
        }
        QuotaStat stat = null;
        boolean reStat = false;
        boolean rollbackIfThrowable = false;
        try {
            stat = getQuotaStatThrowable(surveyId);
            List<QuotaToken> tokens = convertQuotaIdToToken(surveyId, responseId, matchQuoteIds);
            checkUsedQuota(surveyId, responseId, tokens);
            if (stat.hasQuota(matchQuoteIds)) {
                log.info("问卷({})的配额已满", surveyId);
                return false;
            }
            rollbackIfThrowable = addQuota(tokens, stat);
            reStat = true;
            if (useQuotaSuccess(stat, tokens)) {
                return true;
            } else {
                // 配额已满，把得到的配额回滚
                log.info("问卷({})的配额已满", surveyId);
                rollbackQuota(tokens.stream().map(QuotaToken::mapToRollback).filter(Objects::nonNull).collect(Collectors.toList()));
                return false;
            }
        } catch (Throwable e) {
            log.error("问卷({})使用配额失败，答卷({})，匹配的配额：{}", surveyId, responseId, matchQuoteIds.stream().map(Objects::toString).collect(Collectors.joining(",")), e);
            if (rollbackIfThrowable) {
                deleteResponse(surveyId, responseId, matchQuoteIds);
            }
            throw new BadRequestException(e.getMessage());
        } finally {
            if (reStat) {
                statQuota(stat);
            }
        }
    }

    private List<QuotaToken> convertQuotaIdToToken(Long surveyId, Long responseId, List<Long> matchQuoteIds) {
        return matchQuoteIds
                .stream()
                .map(quotaId -> new QuotaToken(surveyId, quotaId, responseId, getQuotaTokenKey(surveyId, quotaId)))
                .collect(Collectors.toList());
    }

    private void checkUsedQuota(Long surveyId, Long responseId, List<QuotaToken> tokens) {
        List<QuotaToken> exists = tokens.stream().parallel().filter(i -> getSetOpt().isMember(i.key, String.valueOf(responseId))).collect(Collectors.toList());
        if (!exists.isEmpty()) {
            log.error("问卷({})使用配额失败，答卷({})已使用过以下配额：{}", surveyId, responseId, exists.stream().map(i -> i.quotaId).map(Objects::toString).collect(Collectors.joining(",")));
            throw new BadRequestException("答卷已使用配额");
        }
    }

    /**
     * 1 向 redis 中添加答卷id，使用配额
     * 2 同时把所有的配额统计数据查询出来
     * 以上 2 步骤必须保证原子性
     */
    private boolean addQuota(List<QuotaToken> tokens, QuotaStat stat) {
        Object r = redisBatchOpt(operations -> {
            operations.multi();
            SetOperations setOpt = operations.opsForSet();
            tokens.forEach(i -> setOpt.add(i.key, String.valueOf(i.responseId)));
            stat.items.forEach(i -> setOpt.size(getQuotaTokenKey(stat.surveyId, i.quotaId)));
            return operations.exec();
        });
        log.debug("使用配额结果：tokens={}, result={}", writeJson(tokens), writeJson(r));
        if (r instanceof List) {
            List<Long> rr = (List<Long>) r;
            int tokenSize = tokens.size();
            int quotaSize = stat.items.size();
            if (rr.size() == tokenSize + quotaSize) {
                IntStream.range(0, rr.size()).forEach(i -> {
                    if (i < tokenSize) {
                        tokens.get(i).success = rr.get(i);
                    } else {
                        stat.items.get(i - tokenSize).use = rr.get(i);
                    }
                });
            }
        }
        return true;
    }

    /**
     * 使用配额，忽略容量，满了也可以使用
     */
    @Override
    public boolean useQuotaIgnoreCapacity(Long surveyId, Long responseId, List<Long> matchQuoteIds) {
        requireSurveyId(surveyId);
        requireResponseId(responseId);
        requireNotSync(surveyId);
        if (CollectionUtils.isEmpty(matchQuoteIds)) {
            throw new BadRequestException("匹配的配额不能为空");
        }
        QuotaStat stat = null;
        boolean reStat = false;
        boolean rollbackIfThrowable = false;
        try {
            stat = getQuotaStatThrowable(surveyId);
            List<QuotaToken> tokens = convertQuotaIdToToken(surveyId, responseId, matchQuoteIds);
            checkUsedQuota(surveyId, responseId, tokens);
            rollbackIfThrowable = addQuota(tokens, stat);
            reStat = true;
            if (useQuotaSuccess(stat, tokens, true)) {
                return true;
            } else {
                log.info("问卷({})的配额未添加成功", surveyId);
                rollbackQuota(tokens.stream().map(QuotaToken::mapToRollback).filter(Objects::nonNull).collect(Collectors.toList()));
                return false;
            }
        } catch (Throwable e) {
            log.error("问卷({})使用配额失败，答卷({})，匹配的配额：{}", surveyId, responseId, matchQuoteIds.stream().map(Objects::toString).collect(Collectors.joining(",")), e);
            if (rollbackIfThrowable) {
                deleteResponse(surveyId, responseId, matchQuoteIds);
            }
            throw new BadRequestException(e.getMessage());
        } finally {
            if (reStat) {
                statQuota(stat);
            }
        }
    }

    /**
     * 1 所有配额中，至少有一个没有超过配额额度
     * 2 必须要保证每个匹配的配额列表中，都添加了答卷id
     */
    private boolean useQuotaSuccess(QuotaStat stat, List<QuotaToken> tokens) {
        return useQuotaSuccess(stat, tokens, false);
    }

    /**
     * 1 所有配额中，至少有一个没有超过配额额度
     * 2 必须要保证每个匹配的配额列表中，都添加了答卷id
     */
    private boolean useQuotaSuccess(QuotaStat stat, List<QuotaToken> tokens, boolean ignoreCapacity) {
        if (!ignoreCapacity) {
            boolean success = tokens.stream().anyMatch(quota -> stat.items.stream().anyMatch(i -> i.use > i.max && i.quotaId == quota.getQuotaId()));
            if (success) {
                return false;
            }
        }
        for (QuotaToken token : tokens) {
            // 如果有配额列表中没有答卷id，则失败
            if (!token.success()) {
                return false;
            }
        }
        return true;
    }

    /**
     * 删除答卷，回滚配额
     *
     * @param supportQuotaIds 支持回滚的配额
     */
    public boolean deleteResponse(Long surveyId, Long responseId, List<Long> supportQuotaIds) {
        requireSurveyId(surveyId);
        requireResponseId(responseId);
        requireNotSync(surveyId);
        try {
            QuotaStat stat = getQuotaStatThrowable(surveyId);
            List<QuotaRollback> rollbacks = stat.items.stream()
                    .filter(i -> supportQuotaIds.contains(i.quotaId))
                    .map(i -> new QuotaRollback(getQuotaTokenKey(surveyId, i.quotaId), responseId))
                    .collect(Collectors.toList());
            if (rollbackQuota(rollbacks)) {
                statQuota(stat);
            }
            return true;
        } catch (Throwable e) {
            log.error("问卷({})回滚配额失败，答卷：{}", surveyId, responseId, e);
            throw new BadRequestException(e.getMessage());
        }
    }

    /**
     * 每次使用配额和回滚配额都需要重新统计
     */
    private void statQuota(QuotaStat stat) {
        String statKey = getQuotaStatKey(stat.surveyId);
        SetOperations<String, String> setOpt = getSetOpt();
        HashOperations<String, String, String> hashOpt = getHashOpt();
        stat.items.parallelStream().forEach(i -> {
            i.use = unbox(setOpt.size(getQuotaTokenKey(stat.surveyId, i.quotaId)));
            hashOpt.put(statKey, QuotaStat.getQuotaStatUseKey(i.quotaId), String.valueOf(i.use));
        });
    }

    /**
     * 向 redis 中移除答卷id，回滚配额 (回滚多个配额的时候必须保证原子性)
     */
    private boolean rollbackQuota(List<QuotaRollback> rollbacks) {
        if (!rollbacks.isEmpty()) {
            Object r = redisBatchOpt(operations -> {
                operations.multi();
                SetOperations setOpt = operations.opsForSet();
                rollbacks.forEach(i -> setOpt.remove(i.key, String.valueOf(i.responseId)));
                return operations.exec();
            });
            log.debug("回滚配额结果：rollbacks={}, result={}", writeJson(rollbacks), writeJson(r));
            return true;
        }
        return false;
    }

    private Object redisBatchOpt(Function<RedisOperations, Object> opt) {
        return stringRedisTemplate.execute(opt::apply);
    }

    private String writeJson(Object obj) {
        return JsonHelper.toJson(obj);
    }

    /**
     * 更新配额数量
     */
    public boolean updateQuotaMax(Long surveyId, Long quotaId, Integer max) {
        requireSurveyId(surveyId);
        requireQuotaId(quotaId);
        try {
            stringRedisTemplate.opsForHash().put(getQuotaStatKey(surveyId), getQuotaStatMaxHashKey(quotaId), max.toString());
            log.debug("问卷({})更新配额数量{}成功，配额Id：{}", surveyId, max, getQuotaStatMaxHashKey(quotaId));
            return true;
        } catch (Throwable e) {
            log.error("问卷({})更新配额数量失败，配额Id：{}", surveyId, quotaId, e);
            throw new BadRequestException(e.getMessage());
        }
    }

    /**
     * 获取命中配额的rid
     */
    public List getQuotaToken(Long surveyId, Long quotaId) {
        requireSurveyId(surveyId);
        requireQuotaId(quotaId);
        try {
            return List.of(stringRedisTemplate.opsForSet().members(getQuotaTokenKey(surveyId, quotaId)).toArray());
        } catch (Exception e) {
            log.error("问卷({})获取命中答卷id失败，配额Id：{}", surveyId, quotaId, e);
        }
        return null;
    }
}
