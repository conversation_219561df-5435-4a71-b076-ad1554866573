package cn.hanyi.survey.service.quota;

import cn.hanyi.survey.service.quota.bean.QuotaInit;
import cn.hanyi.survey.service.quota.bean.QuotaStatItem;
import org.befun.task.dto.TaskProgressDto;

import java.util.List;

public interface QuotaLimit {

    boolean syncQuotaStat( Long orgId,Long surveyId,
                          List<QuotaInit> allQuotas,
                          List<Long> syncCemQuotas,
                          Long countCemResponse,
                          List<Long> syncAdminxQuotas,
                          Long countAdminxResponse);

    /**
     * 查询同步配额进度
     */
    TaskProgressDto syncProgress(Long orgId, Long surveyId);

    void syncCompleted(Long surveyId, boolean success);

    /**
     * 查询配额进度
     */
    List<QuotaStatItem> getQuotaStat(Long surveyId);

    /**
     * 查询是否有配额
     */
    boolean hasQuota(Long surveyId, List<Long> matchQuoteIds);

    /**
     * 使用配额，如果容量已满，会失败
     */
    boolean useQuota(Long surveyId, Long responseId, List<Long> matchQuoteIds);

    /**
     * 使用配额，忽略容量，满了也可以使用
     */
    boolean useQuotaIgnoreCapacity(Long surveyId, Long responseId, List<Long> matchQuoteIds);

    /**
     * 删除答卷，回滚配额
     */
    boolean deleteResponse(Long surveyId, Long responseId, List<Long> supportQuotaIds);

}
